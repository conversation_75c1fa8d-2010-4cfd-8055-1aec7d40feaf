  Why you should do it regularly: https://github.com/browserslist/update-db#readme
2:53:56 PM [express] GET /api/permissions 304 in 75ms :: ["document_management","test_management"]
2:53:56 PM [express] GET /api/user-projects 304 in 45ms :: []
2:53:57 PM [express] GET /api/ai-models 304 in 23ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
2:53:57 PM [express] GET /api/documents 200 in 49ms :: []
o1/o3 modelleri için Azure OpenAI API anahtarı: ******GrPml
2:54:48 PM [express] POST /api/documents/upload 201 in 35388ms :: {"document":{"id":25,"name":"AD_TL…
2:54:49 PM [express] GET /api/documents 304 in 50ms :: []
2:55:09 PM [express] GET /api/ai-models 304 in 29ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
2:55:15 PM [express] GET /api/ai-models 304 in 26ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
2:55:15 PM [express] GET /api/user-projects 304 in 45ms :: []
2:55:15 PM [express] GET /api/permissions 200 in 47ms :: ["user_management","project_management","do…
2:55:21 PM [express] GET /api/users 200 in 45ms :: [{"id":1,"username":"netas","role":"admin","name"…
2:55:21 PM [express] GET /api/permissions/all 200 in 46ms :: [{"id":1,"name":"user_management","desc…
2:55:25 PM [express] GET /api/users/4/permissions 304 in 70ms :: [{"id":3,"name":"document_managemen…
2:55:37 PM [express] GET /api/ai-models 304 in 23ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
2:55:43 PM [express] GET /api/user-projects 304 in 45ms :: []
2:55:43 PM [express] GET /api/permissions 304 in 48ms :: ["user_management","project_management","do…
2:55:43 PM [express] GET /api/prompts 304 in 26ms :: [{"key":"DOCUMENT_ANALYSIS_PROMPT","value":"Lüt…
2:55:44 PM [express] GET /api/user-projects 304 in 47ms :: []
2:55:44 PM [express] GET /api/permissions 304 in 53ms :: ["user_management","project_management","do…
2:55:44 PM [express] GET /api/ai-models 304 in 24ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
2:55:44 PM [express] GET /api/documents 200 in 209ms :: [{"id":21,"name":"TrendKart_Analiz_Dokumani.…
2:55:50 PM [express] GET /api/ai-models 304 in 35ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
2:55:58 PM [express] GET /api/ai-models 304 in 23ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
2:56:05 PM [express] GET /api/documents/23/requirements 304 in 47ms :: [{"id":256,"documentId":23,"c…
2:56:05 PM [express] GET /api/documents/23/components 304 in 52ms :: [{"id":117,"documentId":23,"nam…
2:56:05 PM [express] GET /api/documents/23/api-endpoints 304 in 44ms :: [{"id":88,"documentId":23,"u…
2:56:05 PM [express] GET /api/documents/23/ai-analysis 304 in 54ms :: {"id":22,"documentId":23,"obse…
2:56:05 PM [express] GET /api/documents/23 403 in 147ms :: {"error":"Bu dokümanı görüntüleme yetkini…
2:56:06 PM [express] GET /api/documents/23 403 in 145ms :: {"error":"Bu dokümanı görüntüleme yetkini…
2:56:08 PM [express] GET /api/documents/23 403 in 148ms :: {"error":"Bu dokümanı görüntüleme yetkini…
2:56:11 PM [express] GET /api/documents/21/components 304 in 43ms :: [{"id":107,"documentId":21,"nam…
2:56:11 PM [express] GET /api/documents/21/ai-analysis 304 in 45ms :: {"id":20,"documentId":21,"obse…
2:56:11 PM [express] GET /api/documents/21/requirements 304 in 48ms :: [{"id":234,"documentId":21,"c…
2:56:11 PM [express] GET /api/documents/21/api-endpoints 304 in 51ms :: [{"id":83,"documentId":21,"u…
2:56:11 PM [express] GET /api/documents/21 403 in 171ms :: {"error":"Bu dokümanı görüntüleme yetkini…
2:56:12 PM [express] GET /api/documents/25/requirements 200 in 45ms :: [{"id":279,"documentId":25,"c…
2:56:12 PM [express] GET /api/documents/25/api-endpoints 200 in 44ms :: [{"id":94,"documentId":25,"u…
2:56:12 PM [express] GET /api/documents/25/components 200 in 50ms :: [{"id":132,"documentId":25,"nam…
2:56:12 PM [express] GET /api/documents/25 403 in 123ms :: {"error":"Bu dokümanı görüntüleme yetkini…
2:56:13 PM [express] GET /api/documents/25/ai-analysis 200 in 58ms :: {"id":24,"documentId":25,"obse…
2:56:13 PM [express] GET /api/documents/21 403 in 285ms :: {"error":"Bu dokümanı görüntüleme yetkini…
2:56:13 PM [express] GET /api/documents/25/test-scenarios 200 in 62ms :: []
2:56:14 PM [express] GET /api/documents/25 403 in 90ms :: {"error":"Bu dokümanı görüntüleme yetkiniz…
2:56:15 PM [express] GET /api/documents/23 403 in 158ms :: {"error":"Bu dokümanı görüntüleme yetkini…
2:56:16 PM [express] GET /api/documents/23/test-scenarios 304 in 55ms :: [{"id":275,"documentId":23,…
2:56:17 PM [express] GET /api/documents/23 403 in 129ms :: {"error":"Bu dokümanı görüntüleme yetkini…
2:56:18 PM [express] GET /api/documents/25 403 in 127ms :: {"error":"Bu dokümanı görüntüleme yetkini…
2:56:20 PM [express] GET /api/documents/25 403 in 133ms :: {"error":"Bu dokümanı görüntüleme yetkini…
2:56:22 PM [express] GET /api/documents/25 403 in 137ms :: {"error":"Bu dokümanı görüntüleme yetkini…
2:56:26 PM [express] GET /api/documents/25 403 in 124ms :: {"error":"Bu dokümanı görüntüleme yetkini…