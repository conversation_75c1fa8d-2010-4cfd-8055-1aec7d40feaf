Browserslist: browsers data (caniuse-lite) is 6 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
İzinleri getirme hatası: TypeError: storage.getUserPermissions is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:120:45)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1:04:17 PM [express] GET /api/permissions 500 in 32ms :: {"error":"İzinler getirilirken bir hata olu…
Kullanıcı projeleri getirme hatası: TypeError: storage.getUserProjects is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:494:42)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1:04:18 PM [express] GET /api/user-projects 500 in 24ms :: {"error":"Kullanıcı projeleri getirilirke…
1:04:18 PM [express] GET /api/ai-models 304 in 22ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
1:04:18 PM [express] GET /api/documents 304 in 208ms :: [{"id":21,"name":"TrendKart_Analiz_Dokumani.…
İzinleri getirme hatası: TypeError: storage.getUserPermissions is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:120:45)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1:04:19 PM [express] GET /api/permissions 500 in 24ms :: {"error":"İzinler getirilirken bir hata olu…
Kullanıcı projeleri getirme hatası: TypeError: storage.getUserProjects is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:494:42)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1:04:19 PM [express] GET /api/user-projects 500 in 24ms :: {"error":"Kullanıcı projeleri getirilirke…
İzinleri getirme hatası: TypeError: storage.getUserPermissions is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:120:45)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1:04:21 PM [express] GET /api/permissions 500 in 25ms :: {"error":"İzinler getirilirken bir hata olu…
Kullanıcı projeleri getirme hatası: TypeError: storage.getUserProjects is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:494:42)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1:04:21 PM [express] GET /api/user-projects 500 in 22ms :: {"error":"Kullanıcı projeleri getirilirke…
İzinleri getirme hatası: TypeError: storage.getUserPermissions is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:120:45)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1:04:25 PM [express] GET /api/permissions 500 in 23ms :: {"error":"İzinler getirilirken bir hata olu…
Kullanıcı projeleri getirme hatası: TypeError: storage.getUserProjects is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:494:42)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1:04:25 PM [express] GET /api/user-projects 500 in 26ms :: {"error":"Kullanıcı projeleri getirilirke…
1:04:53 PM [express] GET /api/prompts 304 in 24ms :: [{"key":"DOCUMENT_ANALYSIS_PROMPT","value":"Lüt…
