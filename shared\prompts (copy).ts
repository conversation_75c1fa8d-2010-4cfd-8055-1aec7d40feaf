/**
 * OpenAI için sabit prompt metinleri
 * <PERSON><PERSON>, uygulama genelinde kullanılan AI prompt metinlerini içerir
 */

// Doküman analizi için prompt
export const DOCUMENT_ANALYSIS_PROMPT = `
    Lütfen aşağıdaki dokümanı inceleyerek içerisindeki yazılım gereksinimlerini, bileşenleri ve API uç noktaları ile ilgili tüm bilgileri çıkar:
    
    {{documentContent}}
    
    Analiz sonuçlarını aşağıdaki JSON formatında döndür:
    {
      "components": [
        {
          "name": "Komponent Adı",
          "description": "Komponentin detaylı açıklaması",
          "type": "UI Component|Service|Database|External System|Utility",
          "isNew": true|false
        }
      ],
      "requirements": [
        {
          "code": "Gereksinim Kodu",
          "description": "Gereksinimin açıklaması",
          "category": "Fonksiyonel|Performans|Güvenlik|Kullanılabilirlik|Uyumluluk"
        }
      ],
      "apiEndpoints": [
        {
          "url": "/api/resource/{id}",
          "method": "GET|POST|PUT|DELETE",
          "parameters": { "id": "string", "name": "string" },
          "requirementCode": "İlgili gereksinim kodu"
        }
      ],
      "observations": [
        {
          "text": "Gözlem metni",
          "type": "general|missing_info|suggestion|performance|integration|usability|functional",
          "importance": "low|medium|high",
          "recommendedAction": "Önerilen eylem"
        }
      ]
    }
    
    Talimatlar:
    - Komponentler için UI bileşenleri, servisler, veritabanları veya dış sistemler olabilir.
    - Her gereksinim için bir kod ata (REQ-001, REQ-002 gibi).
    - API uç noktalarını doküman içinden tespit et ve uygun HTTP metotlarıyla eşleştir.
    - Eksik veya belirsiz bilgileri "observations" kısmında belirt.
    - Dokümanı test gereksinimleri açısından analiz et, yeterli test kapsamı sağlamak için gereken gereksinimlere ve kritik noktalara dikkat et.
    - Dokümanda açıkça belirtilmemiş ancak test edilmesi gereken durumları da tespit et.
    - API uç noktalarını detaylı şekilde çıkararak test edilebilecek tüm parametreleri belirle.
    `;

// Test senaryoları oluşturma için prompt
export const TEST_SCENARIOS_GENERATION_PROMPT = `
    Lütfen aşağıdaki gereksinimleri ve doküman içeriğini kullanarak detaylı test senaryoları oluştur:
    
    GEREKSİNİMLER:
    {{requirementsText}}
    
    {{documentContentSection}}
    
    Lütfen test senaryolarını oluştururken aşağıdaki standartlara uy:
    
    1. TEST ADIM YAZIMINDA DETAYLI OLUN: 
       - Her test adımı dokümandan elde edilebilecek maksimum detaya sahip olmalı
       - Her adım en az 2-3 cümle uzunluğunda ve çok detaylı açıklamalar içermeli
       - KONTROLLERİ DETAYLANDIR: Her doğrulama adımını ayrıntılı aç, doğrulama için kullanılan kriterleri belirt
       
 İŞLEM SIRASINI NET BELİRT: "Önce X yapılır, ardından Y ekranında Z kontrolü gerçekleştirilir, daha sonra W tuşuna basılarak T işlemi tamamlanır" şeklinde detaylı adım sıralaması yap
       - Her kontrol adımı için beklenen davranış, ekranda görülmesi beklenen bilgiler ve bunların nasıl doğrulanacağı ayrıntılı açıklanmalı
    
    2. KAPSAMLI SENARYOLAR: 
    - Black box test senaryo kavramına uygun şekilde Başarılı Akış (happy path) test senaryo adımlarını oluştur
    - Başarılı Akış adımlarının içinde test adımları, beklenen sonuçlar olabilir onları ayrıştırıp senaryolaştırman gerekiyor. 
    - Alternatif akışları çıkarmamalısın. sadece Başarılı Akış senaryoları oluştur
    - İşlem zaman aşımı testleri - zaman aşımı süreleriyle
    - Özellikle hata mesajları, hataları ele alma gibi detaylar dokümanda varsa bunları MUTLAKA dahil et
    
    3. ÖN KOŞULLAR VE BEKLENEN SONUÇLAR:
       - Her senaryo için dokümanda var ise ayrıntılı ön koşul belirt
       - Her ön koşulun nasıl sağlanacağını ayrıntılı açıkla
       - Doğrulama adımlarını spesifik olarak belirt (neyin, nasıl, hangi değerlerle doğrulanacağı)
       - Beklenen sonuçları sayısal değerler, metinler ve görseller bazında detaylandır
    
    4. DOKÜMAN DEĞERLERİNİ KULLAN:
       - Dokümanda belirtilen TÜM değerleri, örnek girdileri ve beklentileri aynen kullan
       - Terminolojiyi tamamen dokümandan al, hiçbir terim uydurma
       - Örnek değerler, sınır değerler gibi unsurlar dokümanda belirtilmişse MUTLAKA bunları aynı şekilde kullan
       - Dokümanda belirtilen tüm veri alanlarını, ekran alanlarını ve kontrolleri test adımlarında aynen belirt
    
    5. HER SENARYO KAPSAMLI VE DETAYLI OLSUN:
       - Her senaryo, use case adımlarının tamamını içerecek şekilde test adımlarına ayrıştırılmalı
       - Test adımları sırayla ve birbirini tamamlayacak şekilde düzenlenmeli
       - Her senaryo bir gereksinimin tüm yönlerini test etsin
       - Senaryolar çok net ve ayrıntılı başlıklara sahip olsun
    
    Format olarak SADECE "default" formatını kullan, başka bir format kabul edilmeyecektir:
    - Her adım en az 2-3 cümle uzunluğunda olmalı
    - Adımları "Tıkla, incele, kontrol et" gibi kısa ifadeler yerine tam ve uzun cümleler halinde belirt
    
 Adımlar tam olarak ne yapılacağını, hangi değerlerle yapılacağını, nerede yapılacağını ve sonuçların nasıl doğrulanacağını içermeli
    
    Cevap aşağıdaki alanları içeren bir JSON nesnesi olmalı:
    - scenarios: Tüm test senaryolarının bulunduğu dizi
    - coverageRate: Gereksinimlerin test kapsamı oranı (0-1 arası)
    - missingRequirements: Yeterli test kapsamı olmayan gereksinimlerin kodları
    
    Her senaryo şöyle formatlanmalıdır:
    {
      "title": "Gereksinim kodu ve uzun, açıklayıcı başlık",
      "preconditions": ["Detaylı ön koşul 1 (en az bir uzun cümle)", "Detaylı ön koşul 2 (en az bir uzun cümle)"],
      "steps": ["Aşırı detaylı adım 1 (en az 2-3 cümle)", "Aşırı detaylı adım 2 (en az 2-3 cümle)", "..."],
      "expectedResults": "Beklenen sonuçların aşırı kapsamlı açıklaması (en az 5-6 cümle)",
      "requirementCode": "İlgili gereksinim kodu",
      "format": "default"
    }
    
    
    BU TEST SENARYOLARININ  DETAYLI OLMALARI SON DERECE ÖNEMLİDİR. LÜTFEN HER ADIMI DOKÜMANDAKİ TÜM DETAYLARI İÇERECEK ŞEKİLDE UZUN, AÇIKLAYICI VE KAPSAMLI OLARAK YAZIN. TEST ADIMLARINI OLUŞTURURKEN DOKÜMANDAKİ ANLAM BÜTÜNLÜĞÜNE SADIK KAL VE DOKÜMANDA YER ALMAYAN  EKRAN, BUTON VE TERİMLERİ İÇERİĞİNDE KULLANMA. MÜMKÜN OLAN EN DETAYLI ADIM AÇIKLAMALARINI HAZIRLAYIN.
    `;

// Test senaryosu doğrulama için prompt
export const TEST_SCENARIO_VALIDATION_PROMPT = `
    Lütfen aşağıdaki test senaryosunun ilgili gereksinimi ne kadar iyi karşıladığını değerlendir:
    
    GEREKSİNİM:
    Kod: {{requirementCode}}
    Açıklama: {{requirementDescription}}
    Kategori: {{requirementCategory}}
    
    TEST SENARYOSU:
    Başlık: {{scenarioTitle}}
    Ön Koşullar: {{scenarioPreconditions}}
    Adımlar:
    {{scenarioSteps}}
    Beklenen Sonuçlar: {{scenarioExpectedResults}}
    Format: {{scenarioFormat}}
    
    Lütfen aşağıdaki formatta JSON yanıtı oluştur:
    {
      "score": 0-100 arası bir puan,
      "feedback": "Genel değerlendirme içeren bir metin",
      "improvements": [
        "İyileştirme önerisi 1",
        "İyileştirme önerisi 2",
        "..."
      ],
      "coverage": {
        "functional": 0-100 arası bir puan (işlevsel kapsam),
        "edge_cases": 0-100 arası bir puan (sınır durumları kapsama),
        "negative_tests": 0-100 arası bir puan (negatif test kapsama)
      }
    }
    
    Değerlendirme Kriterleri:
    - Adımlar gereksinimi test etmek için yeterince kapsamlı mı?
    - Beklenen sonuçlar net ve doğrulanabilir mi?
    - Test, gereksinimin farklı yönlerini kapsıyor mu?
    - Sınır değerleri ve hata durumları test ediliyor mu?
    - Test adımları açık ve anlaşılır mı?
    `;

// Doküman kapsama doğrulama için prompt
export const DOCUMENT_COVERAGE_VALIDATION_PROMPT = `
    Lütfen aşağıdaki test senaryolarının verilen gereksinimleri ne kadar iyi kapsadığını değerlendir:
    
    GEREKSİNİMLER:
    {{requirementsText}}
    
    TEST SENARYOLARI:
    {{scenariosText}}
    
    Lütfen aşağıdaki formatta JSON yanıtı oluştur:
    {
      "overallCoverage": 0-100 arası bir puan (genel kapsama derecesi),
      "feedbackSummary": "Genel değerlendirme özeti",
      "requirementCoverage": [
        {
          "requirementCode": "REQ-001",
          "coverageRate": 0-100 arası bir puan,
          "uncoveredAspects": ["Kapsanmayan yön 1", "Kapsanmayan yön 2"]
        },
        ...
      ],
      "recommendations": [
        "İyileştirme önerisi 1",
        "İyileştirme önerisi 2",
        ...
      ]
    }
    
    Değerlendirme Kriterleri:
    - Her gereksinim için yeterli sayıda test senaryosu var mı?
    - Her gereksinimin farklı yönleri test ediliyor mu?
    - Uygun negatif test senaryoları var mı?
    - Sınır değer testleri ve özel durum kontrolleri test ediliyor mu?
    - Kapsanmayan gereksinimler veya gereksinim yönleri nelerdir?
    `;

// AI Asistanı için prompt
export const AI_ASSISTANT_PROMPT = `
    Sen bir doküman analiz asistanısın. Teknik dokümanları analiz edip, gereksinimleri ve test senaryolarını belirleyebilirsin.
    
    {{memoryContext}}
    
    Aşağıdaki soruya Türkçe olarak cevap ver:
    
    {{userQuery}}
    `;

// Daha fazla prompt eklenebilir