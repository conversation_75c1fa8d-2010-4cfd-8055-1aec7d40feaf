penAI ile doküman analizi hatası: BadRequestError: 400 Unsupported parameter: 'temperature' is not supported with this model.
    at Function.generate (/home/<USER>/workspace/node_modules/openai/src/error.ts:72:14)
    at OpenAI.makeStatusError (/home/<USER>/workspace/node_modules/openai/src/core.ts:462:21)
    at OpenAI.makeRequest (/home/<USER>/workspace/node_modules/openai/src/core.ts:526:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Object.analyzeDocument (/home/<USER>/workspace/server/services/openai.ts:185:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:87:34) {
  status: 400,
  headers: {
    'apim-request-id': '480262ce-93e1-4427-91cf-be67a3406a6b',
    'azureml-model-session': 'v20250319-1-164616836',
    'content-length': '210',
    'content-type': 'application/json',
    date: 'Mon, 07 Apr 2025 22:58:39 GMT',
    'ms-azureml-model-error-reason': 'model_error',
    'ms-azureml-model-error-statuscode': '400',
    'ms-azureml-model-time': '19',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    'x-aml-cluster': 'hyena-northcentralus-02',
    'x-content-type-options': 'nosniff',
    'x-envoy-upstream-service-time': '22',
    'x-ms-client-request-id': '480262ce-93e1-4427-91cf-be67a3406a6b',
    'x-ms-rai-invoked': 'true',
    'x-ms-region': 'East US',
    'x-ratelimit-limit-requests': '100',
    'x-ratelimit-limit-tokens': '600000',
    'x-ratelimit-remaining-requests': '99',
    'x-ratelimit-remaining-tokens': '595904',
    'x-request-id': '266caf0a-1aba-464c-81bb-49bfa820ecc3'
  },
  request_id: '266caf0a-1aba-464c-81bb-49bfa820ecc3',
  error: {
    message: "Unsupported parameter: 'temperature' is not supported with this model.",
    type: 'invalid_request_error',
    param: 'temperature',
    code: 'unsupported_parameter'
  },
  code: 'unsupported_parameter',
  param: 'temperature',
  type: 'invalid_request_error'
}
Doküman analiz hatası: Error: Doküman analizi başarısız oldu: 400 Unsupported parameter: 'temperature' is not supported with this model.
    at Object.analyzeDocument (/home/<USER>/workspace/server/services/openai.ts:195:11)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:87:34)