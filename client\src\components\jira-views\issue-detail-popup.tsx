import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  X,
  Maximize2,
  Minimize2,
  Edit,
  Save,
  User,
  Calendar,
  MessageSquare,
  Plus,
  Trash2,
  Flag,
  Clock,
  CheckCircle,
  Circle,
  AlertCircle
} from 'lucide-react';

interface JiraIssue {
  id: string;
  key: string;
  summary: string;
  description: string;
  issueType: string;
  status: string;
  priority: string;
  assignee?: string;
  reporter?: string;
  created: string;
  updated: string;
  comments: Array<{
    id: string;
    author: string;
    body: string;
    created: string;
  }>;
}

interface JiraConnection {
  id: number;
  name: string;
  baseUrl: string;
  username: string;
  token: string;
  status: string;
}

interface JiraProject {
  id: string;
  key: string;
  name: string;
  description?: string;
  lead?: string;
}

interface IssueDetailPopupProps {
  issue: JiraIssue;
  isMaximized: boolean;
  onClose: () => void;
  onToggleSize: () => void;
  connection: JiraConnection | null;
  project: JiraProject | null;
}

export default function IssueDetailPopup({
  issue,
  isMaximized,
  onClose,
  onToggleSize,
  connection,
  project
}: IssueDetailPopupProps) {

  const [isEditing, setIsEditing] = useState(false);
  const [editedIssue, setEditedIssue] = useState({
    summary: issue.summary,
    description: issue.description,
    status: issue.status,
    priority: issue.priority,
    assignee: issue.assignee || ''
  });
  const [newComment, setNewComment] = useState('');
  const [isAddingComment, setIsAddingComment] = useState(false);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Issue güncelleme
  const updateIssueMutation = useMutation({
    mutationFn: async () => {
      if (!connection) throw new Error('Bağlantı bulunamadı');

      const response = await apiRequest('PUT', `/api/integrations/jira/issues/${issue.key}`, {
        url: connection.baseUrl,
        username: connection.username,
        apiToken: connection.token,
        updates: editedIssue
      });
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Issue güncellendi',
        description: 'Issue başarıyla güncellendi.',
      });
      setIsEditing(false);
      queryClient.invalidateQueries({ queryKey: ['jira-issues'] });
    },
    onError: (error: any) => {
      toast({
        title: 'Güncelleme hatası',
        description: error.message || 'Issue güncellenemedi.',
        variant: 'destructive',
      });
    }
  });

  // Yorum ekleme
  const addCommentMutation = useMutation({
    mutationFn: async () => {
      if (!connection || !newComment.trim()) throw new Error('Yorum boş olamaz');

      const response = await apiRequest('POST', `/api/integrations/jira/issues/${issue.key}/comments`, {
        url: connection.baseUrl,
        username: connection.username,
        apiToken: connection.token,
        comment: newComment
      });
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Yorum eklendi',
        description: 'Yorum başarıyla eklendi.',
      });
      setNewComment('');
      setIsAddingComment(false);
      queryClient.invalidateQueries({ queryKey: ['jira-issues'] });
    },
    onError: (error: any) => {
      toast({
        title: 'Yorum hatası',
        description: error.message || 'Yorum eklenemedi.',
        variant: 'destructive',
      });
    }
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'done':
        return <CheckCircle size={16} className="text-green-600" />;
      case 'in progress':
        return <Clock size={16} className="text-yellow-600" />;
      case 'in review':
        return <AlertCircle size={16} className="text-blue-600" />;
      case 'blocked':
        return <Flag size={16} className="text-red-600" />;
      default:
        return <Circle size={16} className="text-gray-400" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'highest':
        return 'text-red-600 bg-red-100';
      case 'high':
        return 'text-orange-600 bg-orange-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'low':
        return 'text-green-600 bg-green-100';
      case 'lowest':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const popupClasses = isMaximized
    ? 'fixed inset-4 z-50'
    : 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[90vw] max-w-4xl h-[80vh] z-50';

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 z-40"
        onClick={onClose}
      />

      {/* Popup */}
      <div className={`${popupClasses} bg-white rounded-lg shadow-xl flex flex-col`}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              {getStatusIcon(issue.status)}
              <h2 className="text-lg font-semibold text-gray-900">
                {issue.key}
              </h2>
            </div>
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              {issue.issueType}
            </Badge>
            <Badge variant="secondary" className={getPriorityColor(issue.priority)}>
              {issue.priority}
            </Badge>
          </div>

          <div className="flex items-center gap-2">
            {!isEditing ? (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
              >
                <Edit size={16} className="mr-2" />
                Düzenle
              </Button>
            ) : (
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setIsEditing(false);
                    setEditedIssue({
                      summary: issue.summary,
                      description: issue.description,
                      status: issue.status,
                      priority: issue.priority,
                      assignee: issue.assignee || ''
                    });
                  }}
                >
                  İptal
                </Button>
                <Button
                  size="sm"
                  onClick={() => updateIssueMutation.mutate()}
                  disabled={updateIssueMutation.isPending}
                >
                  <Save size={16} className="mr-2" />
                  {updateIssueMutation.isPending ? 'Kaydediliyor...' : 'Kaydet'}
                </Button>
              </div>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={onToggleSize}
            >
              {isMaximized ? (
                <Minimize2 size={16} />
              ) : (
                <Maximize2 size={16} />
              )}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={onClose}
            >
              <X size={16} />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full flex">
            {/* Main Content */}
            <div className="flex-1 flex flex-col">
              <ScrollArea className="flex-1 p-6">
                <div className="space-y-6">
                  {/* Summary */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Özet
                    </label>
                    {isEditing ? (
                      <Input
                        value={editedIssue.summary}
                        onChange={(e) => setEditedIssue(prev => ({
                          ...prev,
                          summary: e.target.value
                        }))}
                        className="w-full"
                      />
                    ) : (
                      <h3 className="text-xl font-medium text-gray-900">
                        {issue.summary}
                      </h3>
                    )}
                  </div>

                  {/* Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Açıklama
                    </label>
                    {isEditing ? (
                      <Textarea
                        value={editedIssue.description}
                        onChange={(e) => setEditedIssue(prev => ({
                          ...prev,
                          description: e.target.value
                        }))}
                        rows={6}
                        className="w-full"
                      />
                    ) : (
                      <div className="prose prose-sm max-w-none">
                        {issue.description ? (
                          <p className="text-gray-700 whitespace-pre-wrap">
                            {issue.description}
                          </p>
                        ) : (
                          <p className="text-gray-500 italic">
                            Açıklama bulunmuyor
                          </p>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Comments */}
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                        <MessageSquare size={18} />
                        Yorumlar ({issue.comments.length})
                      </h4>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsAddingComment(true)}
                      >
                        <Plus size={16} className="mr-2" />
                        Yorum Ekle
                      </Button>
                    </div>

                    {/* Add Comment Form */}
                    {isAddingComment && (
                      <div className="mb-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
                        <Textarea
                          value={newComment}
                          onChange={(e) => setNewComment(e.target.value)}
                          placeholder="Yorumunuzu yazın..."
                          rows={3}
                          className="w-full mb-3"
                        />
                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            onClick={() => addCommentMutation.mutate()}
                            disabled={!newComment.trim() || addCommentMutation.isPending}
                          >
                            {addCommentMutation.isPending ? 'Ekleniyor...' : 'Yorum Ekle'}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setIsAddingComment(false);
                              setNewComment('');
                            }}
                          >
                            İptal
                          </Button>
                        </div>
                      </div>
                    )}

                    {/* Comments List */}
                    <div className="space-y-4">
                      {issue.comments.length === 0 ? (
                        <p className="text-gray-500 text-center py-8">
                          Henüz yorum yapılmamış
                        </p>
                      ) : (
                        issue.comments.map(comment => (
                          <div key={comment.id} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <User size={16} className="text-gray-400" />
                                <span className="font-medium text-gray-900">
                                  {comment.author}
                                </span>
                              </div>
                              <div className="flex items-center gap-2 text-sm text-gray-500">
                                <Calendar size={14} />
                                {formatDate(comment.created)}
                              </div>
                            </div>
                            <p className="text-gray-700 whitespace-pre-wrap">
                              {comment.body}
                            </p>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                </div>
              </ScrollArea>
            </div>

            {/* Sidebar */}
            <div className="w-80 border-l border-gray-200 p-6">
              <div className="space-y-6">
                {/* Status */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Durum
                  </label>
                  {isEditing ? (
                    <select
                      value={editedIssue.status}
                      onChange={(e) => setEditedIssue(prev => ({
                        ...prev,
                        status: e.target.value
                      }))}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="To Do">To Do</option>
                      <option value="In Progress">In Progress</option>
                      <option value="In Review">In Review</option>
                      <option value="Done">Done</option>
                      <option value="Blocked">Blocked</option>
                    </select>
                  ) : (
                    <div className="flex items-center gap-2">
                      {getStatusIcon(issue.status)}
                      <span className="text-gray-900">{issue.status}</span>
                    </div>
                  )}
                </div>

                {/* Priority */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Öncelik
                  </label>
                  {isEditing ? (
                    <select
                      value={editedIssue.priority}
                      onChange={(e) => setEditedIssue(prev => ({
                        ...prev,
                        priority: e.target.value
                      }))}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="Highest">Highest</option>
                      <option value="High">High</option>
                      <option value="Medium">Medium</option>
                      <option value="Low">Low</option>
                      <option value="Lowest">Lowest</option>
                    </select>
                  ) : (
                    <Badge variant="secondary" className={getPriorityColor(issue.priority)}>
                      {issue.priority}
                    </Badge>
                  )}
                </div>

                {/* Assignee */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Atanan Kişi
                  </label>
                  {isEditing ? (
                    <Input
                      value={editedIssue.assignee}
                      onChange={(e) => setEditedIssue(prev => ({
                        ...prev,
                        assignee: e.target.value
                      }))}
                      placeholder="Kullanıcı adı..."
                    />
                  ) : (
                    <div className="flex items-center gap-2">
                      <User size={16} className="text-gray-400" />
                      <span className="text-gray-900">
                        {issue.assignee || 'Atanmamış'}
                      </span>
                    </div>
                  )}
                </div>

                {/* Reporter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Rapor Eden
                  </label>
                  <div className="flex items-center gap-2">
                    <User size={16} className="text-gray-400" />
                    <span className="text-gray-900">
                      {issue.reporter || 'Bilinmiyor'}
                    </span>
                  </div>
                </div>

                {/* Dates */}
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Oluşturulma
                    </label>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Calendar size={14} />
                      {formatDate(issue.created)}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Son Güncelleme
                    </label>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Calendar size={14} />
                      {formatDate(issue.updated)}
                    </div>
                  </div>
                </div>

                {/* Project Info */}
                {project && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Proje
                    </label>
                    <div className="text-sm text-gray-900">
                      {project.name} ({project.key})
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
