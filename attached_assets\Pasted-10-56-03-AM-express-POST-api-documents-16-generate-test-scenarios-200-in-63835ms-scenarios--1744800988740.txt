10:56:03 AM [express] POST /api/documents/16/generate-test-scenarios 200 in 63835ms :: {"scenarios":[…
10:56:04 AM [express] GET /api/documents/16/test-scenarios 200 in 98ms :: [{"id":379,"documentId":16,…
Test senaryoları silme hatası: error: syntax error at or near "("
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:502:9) {
  length: 90,
  severity: 'ERROR',
  code: '42601',
  detail: undefined,
  hint: undefined,
  position: '13',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'scan.l',
  line: '1244',
  routine: 'scanner_yyerror'
}
10:56:08 AM [express] DELETE /api/documents/16/test-scenarios 500 in 328ms :: {"error":"Test senaryol…
Test senaryoları silme hatası: error: syntax error at or near "("
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:502:9) {
  length: 90,
  severity: 'ERROR',
  code: '42601',
  detail: undefined,
  hint: undefined,
  position: '13',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'scan.l',
  line: '1244',
  routine: 'scanner_yyerror'
}
10:56:09 AM [express] DELETE /api/documents/16/test-scenarios 500 in 413ms :: {"error":"Test senaryol…
Test senaryoları silme hatası: error: syntax error at or near "("
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:502:9) {
  length: 90,
  severity: 'ERROR',
  code: '42601',
  detail: undefined,
  hint: undefined,
  position: '13',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,