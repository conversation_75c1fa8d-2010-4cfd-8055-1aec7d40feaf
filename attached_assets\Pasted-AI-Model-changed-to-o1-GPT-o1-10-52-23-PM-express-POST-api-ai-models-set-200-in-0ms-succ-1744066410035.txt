AI Model changed to: o1 (GPT-o1)
10:52:23 PM [express] POST /api/ai-models/set 200 in 0ms :: {"success":true,"message":"AI modeli 'o1'…
OpenAI ile doküman analizi hatası: BadRequestError: 400 Model {modelName} is enabled only for api versions 2024-12-01-preview and later
    at Function.generate (/home/<USER>/workspace/node_modules/openai/src/error.ts:72:14)
    at OpenAI.makeStatusError (/home/<USER>/workspace/node_modules/openai/src/core.ts:462:21)
    at OpenAI.makeRequest (/home/<USER>/workspace/node_modules/openai/src/core.ts:526:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Object.analyzeDocument (/home/<USER>/workspace/server/services/openai.ts:170:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:87:34) {
  status: 400,
  headers: {
    'apim-request-id': '15df76bd-f990-4c41-b8fb-df999d05c0ec',
    'content-length': '123',
    'content-type': 'application/json',
    date: 'Mon, 07 Apr 2025 22:52:37 GMT',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    'x-content-type-options': 'nosniff',
    'x-ms-region': 'East US'
  },
  request_id: undefined,
  error: {
    code: 'BadRequest',
    message: 'Model {modelName} is enabled only for api versions 2024-12-01-preview and later'
  },
  code: 'BadRequest',
  param: undefined,
  type: undefined
}
Doküman analiz hatası: Error: Doküman analizi başarısız oldu: 400 Model {modelName} is enabled only for api versions 2024-12-01-preview and later
    at Object.analyzeDocument (/home/<USER>/workspace/server/services/openai.ts:186:11)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:87:34)
10:52:38 PM [express] POST /api/documents/upload 201 in 3500ms :: {"document":{"id":22,"name":"demo.d…
10:52:38 PM [express] GET /api/documents 200 in 1ms :: [{"id":22,"name":"demo.docx","type":"docx","co…
