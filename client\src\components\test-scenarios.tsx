import { useState, useMemo } from "react";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { type Requirement, type TestScenario } from "@shared/schema";
import { validateTestScenario, validateDocumentCoverage, improveTestScenarioSteps, updateTestScenario, deleteAllTestScenarios } from "@/lib/api";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";

interface TestScenariosProps {
  documentId: number | null;
  requirements: Requirement[];
  testScenarios: TestScenario[];
}

const TestScenarios = ({ documentId, requirements, testScenarios }: TestScenariosProps) => {
  const [expandedScenario, setExpandedScenario] = useState<number | null>(null);
  const [selectedFormat, setSelectedFormat] = useState<string>("all");
  const [selectedRequirement, setSelectedRequirement] = useState<string>("all");
  const [exportModalOpen, setExportModalOpen] = useState(false);
  const [maxTokens, setMaxTokens] = useState<number>(15000);
  const [editingScenario, setEditingScenario] = useState<TestScenario | null>(null);
  const [editSteps, setEditSteps] = useState<string[]>([]);
  const [editTitle, setEditTitle] = useState<string>("");
  const [editPreconditions, setEditPreconditions] = useState<string>("");
  const [generatedCode, setGeneratedCode] = useState<string>("");
  const [codeFormat, setCodeFormat] = useState<string>("");
  const [showCodeDialog, setShowCodeDialog] = useState<boolean>(false);
  const [editExpectedResults, setEditExpectedResults] = useState<string>("");
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isImproving, setIsImproving] = useState<boolean>(false);
  const [improvedSteps, setImprovedSteps] = useState<string[]>([]);
  const [generatingPopupOpen, setGeneratingPopupOpen] = useState<boolean>(false);
  const { toast } = useToast();

  // Test senaryolarını formatlarına göre grupla
  const formatGroups = useMemo(() => {
    const groups: Record<string, TestScenario[]> = {};
    
    testScenarios.forEach(scenario => {
      const format = scenario.format || "default";
      if (!groups[format]) {
        groups[format] = [];
      }
      groups[format].push(scenario);
    });
    
    return groups;
  }, [testScenarios]);

  // Gereksinimlere göre grupla
  const requirementGroups = useMemo(() => {
    const groups: Record<string, TestScenario[]> = {};
    
    testScenarios.forEach(scenario => {
      const reqCode = scenario.requirementCode || "uncategorized";
      if (!groups[reqCode]) {
        groups[reqCode] = [];
      }
      groups[reqCode].push(scenario);
    });
    
    return groups;
  }, [testScenarios]);

  // Filtrelenmiş senaryolar
  const filteredScenarios = useMemo(() => {
    return testScenarios.filter(scenario => {
      // Format filtreleme
      if (selectedFormat !== "all" && scenario.format !== selectedFormat) {
        return false;
      }
      
      // Gereksinim filtreleme
      if (selectedRequirement !== "all" && scenario.requirementCode !== selectedRequirement) {
        return false;
      }
      
      return true;
    });
  }, [testScenarios, selectedFormat, selectedRequirement]);

  // İstatistikler
  const statistics = useMemo(() => {
    return {
      total: testScenarios.length,
      byFormat: Object.entries(formatGroups).map(([format, scenarios]) => ({
        format,
        count: scenarios.length,
        percentage: Math.round((scenarios.length / testScenarios.length) * 100)
      })),
      byRequirement: Object.entries(requirementGroups).map(([reqCode, scenarios]) => ({
        requirementCode: reqCode,
        count: scenarios.length,
        percentage: Math.round((scenarios.length / testScenarios.length) * 100)
      }))
    };
  }, [formatGroups, requirementGroups, testScenarios.length]);

  const generateMutation = useMutation({
    mutationFn: async () => {
      if (!documentId) throw new Error("No document selected");
      
      // Popup'ı aç
      setGeneratingPopupOpen(true);
      
      const response = await apiRequest("POST", `/api/documents/${documentId}/generate-test-scenarios`, {
        maxTokens: maxTokens
      });
      return response.json();
    },
    onSuccess: (data) => {
      // Popup'ı kapat
      setGeneratingPopupOpen(false);
      
      toast({
        title: "Test senaryoları oluşturuldu",
        description: `${data.scenarios.length} senaryo başarıyla oluşturuldu. Kapsama oranı: %${Math.round(data.coverageRate * 100)}`,
        variant: "default",
      });
      
      queryClient.invalidateQueries({ queryKey: [`/api/documents/${documentId}/test-scenarios`] });
    },
    onError: (error) => {
      // Hata durumunda da popup'ı kapat
      setGeneratingPopupOpen(false);
      
      toast({
        title: "Hata",
        description: `Test senaryoları oluşturulurken bir hata oluştu: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`,
        variant: "destructive",
      });
    },
  });
  
  // Test senaryosu adımlarını AI ile iyileştirme fonksiyonu
  const improveStepsMutation = useMutation({
    mutationFn: async (scenarioId: number) => {
      const response = await improveTestScenarioSteps(scenarioId);
      return response.json();
    },
    onSuccess: (data) => {
      setImprovedSteps(data.improvedSteps);
      setIsImproving(false);
      toast({
        title: "Adımlar İyileştirildi",
        description: "Test senaryosu adımları başarıyla iyileştirildi. İnceleyip kaydetmek ister misiniz?",
        variant: "default",
      });
    },
    onError: (error) => {
      setIsImproving(false);
      toast({
        title: "Hata",
        description: `Adımlar iyileştirilirken bir hata oluştu: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`,
        variant: "destructive",
      });
    },
  });
  
  // Test senaryosu güncelleme fonksiyonu
  const updateScenarioMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number, data: any }) => {
      const response = await updateTestScenario(id, data);
      return response.json();
    },
    onSuccess: (data) => {
      setIsEditing(false);
      setEditingScenario(null);
      toast({
        title: "Senaryo Güncellendi",
        description: "Test senaryosu başarıyla güncellendi.",
        variant: "default",
      });
      
      if (documentId) {
        queryClient.invalidateQueries({ queryKey: [`/api/documents/${documentId}/test-scenarios`] });
      }
    },
    onError: (error) => {
      toast({
        title: "Hata",
        description: `Senaryo güncellenirken bir hata oluştu: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`,
        variant: "destructive",
      });
    },
  });
  
  // Tüm test senaryolarını silme mutasyonu
  const deleteAllScenariosMutation = useMutation({
    mutationFn: async () => {
      if (!documentId) throw new Error("Doküman seçilmedi");
      const response = await deleteAllTestScenarios(documentId);
      return response.json();
    },
    onSuccess: (data) => {
      toast({
        title: "Senaryolar Silindi",
        description: `${data.count} test senaryosu başarıyla silindi. Yenileri oluşturuluyor...`,
        variant: "default",
      });
      
      // Cache'i yenile
      if (documentId) {
        queryClient.invalidateQueries({ queryKey: [`/api/documents/${documentId}/test-scenarios`] });
        
        // Mevcut senaryolar silindikten sonra otomatik olarak yeni senaryolar üret
        generateMutation.mutate();
      }
    },
    onError: (error) => {
      toast({
        title: "Hata",
        description: `Test senaryoları silinirken bir hata oluştu: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`,
        variant: "destructive",
      });
    },
  });

  const toggleScenario = (id: number) => {
    if (expandedScenario === id) {
      setExpandedScenario(null);
    } else {
      setExpandedScenario(id);
    }
  };

  const getFormatBadgeColor = (format: string) => {
    switch (format?.toLowerCase()) {
      case "gherkin": return "bg-green-100 text-green-800 hover:bg-green-200";
      case "selenium": return "bg-blue-100 text-blue-800 hover:bg-blue-200";
      case "cucumber": return "bg-emerald-100 text-emerald-800 hover:bg-emerald-200";
      case "junit": return "bg-red-100 text-red-800 hover:bg-red-200";
      case "testng": return "bg-orange-100 text-orange-800 hover:bg-orange-200";
      case "cypressjs": return "bg-indigo-100 text-indigo-800 hover:bg-indigo-200";
      case "playwright": return "bg-purple-100 text-purple-800 hover:bg-purple-200";
      case "puppeteer": return "bg-teal-100 text-teal-800 hover:bg-teal-200";
      case "mocha": return "bg-amber-100 text-amber-800 hover:bg-amber-200";
      case "jest": return "bg-pink-100 text-pink-800 hover:bg-pink-200";
      case "robot": return "bg-lime-100 text-lime-800 hover:bg-lime-200";
      case "specflow": return "bg-sky-100 text-sky-800 hover:bg-sky-200";
      case "katalon": return "bg-violet-100 text-violet-800 hover:bg-violet-200";
      case "appium": return "bg-rose-100 text-rose-800 hover:bg-rose-200";
      case "protractor": return "bg-amber-100 text-amber-800 hover:bg-amber-200";
      default: return "bg-gray-100 text-gray-800 hover:bg-gray-200";
    }
  };

  const getFormatIcon = (format: string) => {
    switch (format?.toLowerCase()) {
      case "gherkin": return "fas fa-leaf";
      case "selenium": return "fas fa-robot";
      case "cucumber": return "fas fa-seedling";
      case "junit": return "fas fa-flask";
      case "testng": return "fas fa-cogs";
      case "cypressjs": return "fas fa-microscope";
      case "cypress": return "fas fa-microscope";
      case "playwright": return "fas fa-theater-masks";
      case "puppeteer": return "fas fa-ghost";
      case "mocha": return "fas fa-coffee";
      case "jest": return "fas fa-atom";
      case "robot": return "fas fa-android";
      case "specflow": return "fas fa-project-diagram";
      case "katalon": return "fas fa-pencil-ruler";
      case "appium": return "fas fa-mobile-alt";
      case "protractor": return "fas fa-tachometer-alt";
      case "visium": return "fas fa-vial";
      case "visiumgo": return "fas fa-vial";
      default: return "fas fa-vial";
    }
  };

  // Senaryoların kodlama formatlarını oluşturma
  const formatScenarioForExport = (scenario: TestScenario, format: string) => {
    // Adım listesini hazırla
    const steps = Array.isArray(scenario.steps) ? scenario.steps : [];
    const title = scenario.title || 'Adsız Senaryo';
    
    // preconditions bir dizi veya JSON stringi olabilir, onu string'e dönüştürelim
    let preconditionsStr = '';
    if (typeof scenario.preconditions === 'string') {
      try {
        // JSON string olabilir, parse etmeyi deneyelim
        // Önce string'in bir JSON olup olmadığını kontrol edelim
        let inputStr = scenario.preconditions.trim();
        
        if (inputStr.startsWith('{') || inputStr.startsWith('[')) {
          try {
            // Basit temizleme işlemleri
            inputStr = inputStr
              .replace(/\\"/g, '"')
              .replace(/\\'/g, "'")
              .replace(/\\&/g, "&")
              .replace(/\\r/g, "\r")
              .replace(/\\n/g, "\n")
              .replace(/\\t/g, "\t")
              .replace(/\\b/g, "\b")
              .replace(/\\f/g, "\f");
            
            // Tek tırnak yerine çift tırnak kullan
            inputStr = inputStr.replace(/'/g, '"');
            
            // JSON'ı parse et
            const parsed = JSON.parse(inputStr);
            
            if (Array.isArray(parsed)) {
              // Array, her elemanı satır olarak birleştir
              preconditionsStr = parsed
                .filter(item => item !== null && item !== undefined)
                .join('\n');
            } else if (typeof parsed === 'object' && parsed !== null) {
              // Obje ise, değerlerini birleştir
              const values = Object.values(parsed)
                .filter(value => value !== null && value !== undefined)
                .map(value => typeof value === 'string' ? value : JSON.stringify(value));
              
              preconditionsStr = values.join('\n');
            } else {
              preconditionsStr = String(parsed);
            }
          } catch (parseError) {
            // JSON parse edilemezse, manuel olarak parantezleri kaldır
            const content = inputStr.substring(1, inputStr.length - 1);
            
            // JSON dizisi ise virgülle ayrılmış değerleri al
            if (inputStr.startsWith('[')) {
              const items = content.split(',')
                .map(item => item.trim())
                .map(item => {
                  // Çift tırnaklı değerleri temizle
                  if (item.startsWith('"') && item.endsWith('"')) {
                    return item.substring(1, item.length - 1);
                  }
                  return item;
                });
              
              preconditionsStr = items.join('\n');
            } else {
              // Obje ise, daha basitleştirilmiş bir yaklaşım - sadece metin formatı
              preconditionsStr = content
                .replace(/"/g, '') // Tırnak işaretlerini kaldır
                .replace(/,/g, '\n'); // Virgülleri satır sonu yapar
            }
          }
        } else {
          // JSON formatında değilse doğrudan kullan
          preconditionsStr = scenario.preconditions;
        }
      } catch (e) {
        console.error("Ön koşulları ayrıştırma hatası:", e);
        // Herhangi bir hata durumunda sadece görüntülenebilir metin olarak işle
        // Süslü parantezleri kaldır ve temiz bir görüntü oluştur
        if (scenario.preconditions.includes('{') || scenario.preconditions.includes('[')) {
          preconditionsStr = scenario.preconditions
            .replace(/{|\[/g, '')   // Açılış parantezlerini kaldır
            .replace(/}|\]/g, '')   // Kapanış parantezlerini kaldır
            .replace(/"/g, '')      // Tırnak işaretlerini kaldır
            .replace(/,/g, '\n');   // Virgülleri satır sonu yap
        } else {
          preconditionsStr = scenario.preconditions;
        }
      }
    } else if (Array.isArray(scenario.preconditions)) {
      preconditionsStr = scenario.preconditions.join('\n');
    } else {
      preconditionsStr = scenario.preconditions || '';
    }
    
    const expectedResults = scenario.expectedResults || '';
    const reqCode = scenario.requirementCode || '';
    
    switch (format) {
      case "markdown":
        return `# ${title}\n\n## Gereksinim\n${reqCode}\n\n## Ön Koşullar\n${preconditionsStr}\n\n## Adımlar\n${steps.map((step, i) => `${i+1}. ${step}`).join('\n')}\n\n## Beklenen Sonuçlar\n${expectedResults}\n`;
      
      case "csv":
        return `"${title}","${reqCode}","${preconditionsStr.replace(/"/g, '""')}","${steps.join(' | ').replace(/"/g, '""')}","${expectedResults.replace(/"/g, '""')}"`;
      
      case "gherkin":
        return `# language: en\n\nFeature: ${title}\n\n  Scenario: ${title}\n    Given ${preconditionsStr}\n${steps.map(step => `    And ${step}`).join('\n')}\n    Then ${expectedResults}\n`;
      
      case "selenium":
        const className = title.replace(/[^a-zA-Z0-9]/g, '');
        return `import org.openqa.selenium.WebDriver;\nimport org.openqa.selenium.chrome.ChromeDriver;\nimport org.junit.Test;\n\npublic class ${className}Test {\n\n  @Test\n  public void test${className}() {\n    // Tests requirement ${reqCode}\n    WebDriver driver = new ChromeDriver();\n    \n    // Prerequisites\n    // ${preconditionsStr}\n    \n${steps.map((step, i) => `    // Step ${i+1}: ${step}\n`).join('')}\n    \n    // Expected result: ${expectedResults}\n    \n    driver.quit();\n  }\n}\n`;
      
      case "cucumber":
        return `# language: en\n\nFeature: ${title}\n  Test for requirement ${reqCode}\n  ${preconditionsStr}\n\n  Scenario: ${title}\n${steps.map((step, i) => `    * ${step}`).join('\n')}\n    Then ${expectedResults}\n`;
      
      case "junit":
        const junitClassName = title.replace(/[^a-zA-Z0-9]/g, '');
        return `import org.junit.Test;\nimport static org.junit.Assert.*;\n\npublic class ${junitClassName}Test {\n\n  /**\n   * Requirement ${reqCode} test\n   * Prerequisites: ${preconditionsStr}\n   */\n  @Test\n  public void test${junitClassName}() {\n${steps.map((step, i) => `    // Step ${i+1}: ${step}\n`).join('')}\n    // Expected: ${expectedResults}\n    assertTrue(true);\n  }\n}\n`;
      
      case "testng":
        const testNGClassName = title.replace(/[^a-zA-Z0-9]/g, '');
        return `import org.testng.annotations.Test;\nimport static org.testng.Assert.*;\n\npublic class ${testNGClassName}Test {\n\n  /**\n   * Requirement ${reqCode} test\n   */\n  @Test(description = "${title}")\n  public void test${testNGClassName}() {\n    // Prerequisites: ${preconditionsStr}\n${steps.map((step, i) => `    // Step ${i+1}: ${step}\n`).join('')}\n    // Expected: ${expectedResults}\n    assertTrue(true, "Test successful");\n  }\n}\n`;
      

      
      case "playwright":
        return `const { test, expect } = require('@playwright/test');\n\n// Requirement ${reqCode} test\ntest('${title}', async ({ page }) => {\n  // Prerequisites: ${preconditionsStr}\n  \n${steps.map((step, i) => `  // Step ${i+1}: ${step}\n`).join('')}\n  \n  // Expected result: ${expectedResults}\n});\n`;
      
      case "puppeteer":
        return `const puppeteer = require('puppeteer');\n\n(async () => {\n  // ${title} - Requirement ${reqCode} test\n  // Prerequisites: ${preconditionsStr}\n  const browser = await puppeteer.launch();\n  const page = await browser.newPage();\n  \n${steps.map((step, i) => `  // Step ${i+1}: ${step}\n`).join('')}\n  \n  // Expected result: ${expectedResults}\n  \n  await browser.close();\n})();\n`;
      
      case "mocha":
        return `const assert = require('assert');\n\ndescribe('${title}', function() {\n  // Testing requirement ${reqCode}\n  // Prerequisites: ${preconditionsStr}\n  \n  it('${title}', function() {\n${steps.map((step, i) => `    // Step ${i+1}: ${step}\n`).join('')}\n    // Expected result: ${expectedResults}\n    assert.ok(true);\n  });\n});\n`;
      
      case "jest":
        return `// Requirement ${reqCode} test\n// Prerequisites: ${preconditionsStr}\n\ntest('${title}', () => {\n${steps.map((step, i) => `  // Step ${i+1}: ${step}\n`).join('')}\n  \n  // Expected result: ${expectedResults}\n  expect(true).toBeTruthy();\n});\n`;
      
      case "visiumgo":
        return `// Visium Go Test Script
// Test: ${title}
// Requirement: ${reqCode}

// Prerequisites:
// ${preconditionsStr}

// Steps:
${steps.map((step, i) => `// ${i+1}. ${step}`).join('\n')}

// Expected Results:
// ${expectedResults}`;

      case "cypressjs":
        return `/// <reference types="cypress" />

describe('${title}', () => {
  // Testing requirement ${reqCode}
  // Prerequisites: ${preconditionsStr}
  
  it('should ${title.toLowerCase()}', () => {
${steps.map((step, i) => `    // Step ${i+1}: ${step}\n`).join('')}
    // Expected result: ${expectedResults}
    cy.log('Test completed');
  });
});`;

      default:
        return `# Test Scenario: ${title}
Requirement: ${reqCode}

## Prerequisites:
${preconditionsStr}

## Steps:
${steps.map((step, i) => `${i+1}. ${step}`).join('\n')}

## Expected Results:
${expectedResults}`;
    }
  };

  const exportScenarios = (format: string) => {
    let content = "";
    let filename = `test-scenarios-${new Date().toISOString().slice(0, 10)}`;
    let mimeType = "text/plain";
    let extension = ".txt";
    
    // Test araçları formatları
    if (["jira", "alm", "testrail", "visium"].includes(format)) {
      // Araç bağlantı formatlarını oluştur
      content = `# Test Senaryoları - ${format.toUpperCase()} Entegrasyonu\n\n`;
      
      filteredScenarios.forEach(scenario => {
        content += formatScenarioForExport(scenario, "markdown") + "\n\n---\n\n";
      });
      
      // Her araç için belirli biçimlendirme ekle
      if (format === "jira") {
        content += "\n\n# Jira entegrasyonu için gerekli alanlar\n";
        content += "# Proje Kodu: PROJECT_KEY\n";
        content += "# Issue Tipi: Test\n";
        extension = ".jira";
      } else if (format === "alm") {
        content += "\n\n# ALM entegrasyonu için gerekli alanlar\n";
        content += "# Domain: DEFAULT\n";
        content += "# Proje: PROJECT_NAME\n";
        extension = ".alm";
      } else if (format === "testrail") {
        content += "\n\n# TestRail entegrasyonu için XML formatı\n";
        extension = ".xml";
      } else if (format === "visium") {
        content += "\n\n# Visium Manage entegrasyonu için JSON formatı\n";
        extension = ".visium";
      }
    }
    // Doküman formatları
    else if (format === "excel") {
      content = `"Başlık","Gereksinim Kodu","Ön Koşullar","Adımlar","Beklenen Sonuçlar"\n`;
      mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      extension = ".xlsx";
      
      filteredScenarios.forEach(scenario => {
        content += formatScenarioForExport(scenario, "csv") + "\n";
      });
    }
    else if (format === "pdf") {
      content = "PDF formatında export için hazırlanan içerik:\n\n";
      filteredScenarios.forEach(scenario => {
        content += formatScenarioForExport(scenario, "markdown") + "\n\n---\n\n";
      });
      mimeType = "application/pdf";
      extension = ".pdf";
    } 
    else if (format === "csv") {
      content = `"Başlık","Gereksinim Kodu","Ön Koşullar","Adımlar","Beklenen Sonuçlar"\n`;
      mimeType = "text/csv";
      extension = ".csv";
      
      filteredScenarios.forEach(scenario => {
        content += formatScenarioForExport(scenario, "csv") + "\n";
      });
    } 
    else if (format === "json") {
      mimeType = "application/json";
      extension = ".json";
      content = JSON.stringify(filteredScenarios, null, 2);
    } 
    else if (["gherkin", "cucumber"].includes(format)) {
      mimeType = "text/plain";
      extension = ".feature";
      
      // All scenarios in a single feature file for Gherkin format
      content = `# language: en\n\nFeature: Test Scenarios\n  Test scenarios generated from requirement document\n\n`;
      
      filteredScenarios.forEach(scenario => {
        content += formatScenarioForExport(scenario, format) + "\n\n";
      });
    } else if (["selenium", "junit", "testng"].includes(format)) {
      mimeType = "text/plain";
      extension = ".java";
      
      // Her senaryo için ayrı bir test sınıfı
      filteredScenarios.forEach((scenario, index) => {
        if (index > 0) content += "\n\n// ------------------------------------------------\n\n";
        content += formatScenarioForExport(scenario, format);
      });
    } else if (["cypressjs", "playwright", "puppeteer", "mocha", "jest"].includes(format)) {
      mimeType = "text/plain";
      extension = ".js";
      
      // JS tabanlı test kodu
      filteredScenarios.forEach((scenario, index) => {
        if (index > 0) content += "\n\n// ------------------------------------------------\n\n";
        content += formatScenarioForExport(scenario, format);
      });
    } else {
      // Diğer herhangi bir format
      mimeType = "text/plain";
      
      filteredScenarios.forEach((scenario, index) => {
        if (index > 0) content += "\n\n// ------------------------------------------------\n\n";
        content += formatScenarioForExport(scenario, format);
      });
    }
    
    filename += extension;
    
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);
    
    setExportModalOpen(false);
    toast({
      title: "Dışa aktarma başarılı",
      description: `${filteredScenarios.length} test senaryosu ${format.toUpperCase()} formatında dışa aktarıldı.`,
      variant: "default",
    });
  };

  // Senaryo düzenleme modunu aç
  const startEditingScenario = (scenario: TestScenario) => {
    setEditingScenario(scenario);
    setEditTitle(scenario.title || "");
    setEditPreconditions(scenario.preconditions || "");
    setEditExpectedResults(scenario.expectedResults || "");
    
    // Adımları düzenleme için hazırla
    const steps = Array.isArray(scenario.steps) ? scenario.steps : [];
    setEditSteps([...steps]);
    
    setIsEditing(true);
    setIsImproving(false);
  };
  
  // Düzenlemeyi iptal et
  const cancelEditing = () => {
    setIsEditing(false);
    setEditingScenario(null);
    setEditSteps([]);
    setEditTitle("");
    setEditPreconditions("");
    setEditExpectedResults("");
    setImprovedSteps([]);
  };
  
  // Düzenlenen senaryoyu kaydet
  const saveEditedScenario = () => {
    if (!editingScenario) return;
    
    // Güncellenecek verileri hazırla
    const updateData = {
      title: editTitle,
      preconditions: editPreconditions,
      expectedResults: editExpectedResults,
      steps: editSteps,
    };
    
    // Güncelleme işlemini başlat
    updateScenarioMutation.mutate({ id: editingScenario.id, data: updateData });
  };
  
  // Adım ekle
  const addNewStep = () => {
    setEditSteps([...editSteps, ""]);
  };
  
  // Adım sil
  const removeStep = (index: number) => {
    const newSteps = [...editSteps];
    newSteps.splice(index, 1);
    setEditSteps(newSteps);
  };
  
  // Adım güncelle
  const updateStep = (index: number, value: string) => {
    const newSteps = [...editSteps];
    newSteps[index] = value;
    setEditSteps(newSteps);
  };
  
  // AI ile adımları iyileştir
  const improveSteps = (scenarioId: number) => {
    setIsImproving(true);
    improveStepsMutation.mutate(scenarioId);
  };
  
  // Otomasyon kodu oluşturma popup stateleri
  const [generatingCode, setGeneratingCode] = useState<boolean>(false);

  // AI ile otomasyon kodu üret
  const generateAutomationCode = async (scenario: TestScenario, format: string) => {
    // Önceden açık olan kodları temizle
    setShowCodeDialog(false);
    setGeneratedCode("");
    
    // Popup göster
    setGeneratingCode(true);
    
    toast({
      title: "AI Kod Oluşturuyor",
      description: `${format.toUpperCase()} formatında otomasyon kodu oluşturuluyor...`,
      variant: "default",
    });
    
    try {
      // Format için detaylı talimatlar hazırla
      let formatInstructions = "";
      if (format === "gherkin") {
        formatInstructions = `
# Gherkin formatında şu yapıda kod oluştur:
Feature: [Senaryo adı]
  Scenario: [Senaryo detayı]
    Given [Ön koşul]
    When [İlk adım]
    And [Sonraki adımlar]
    Then [Beklenen sonuç]
    And [Varsa ek beklentiler]

# Örnek:
Feature: Online Banking Login
  Scenario: Successful login with valid credentials
    Given the user is on the login page
    When the user enters valid username "testuser"
    And the user enters valid password "password123"
    And the user clicks the login button
    Then the user should be redirected to the dashboard
    And the user should see their account summary
`;
      } else if (format === "selenium") {
        formatInstructions = `
# Selenium Java formatında aşağıdaki yapıyı kullan:
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.testng.Assert;
import org.testng.annotations.*;

public class [TestSınıfAdı]Test {
    private WebDriver driver;
    
    @BeforeTest
    public void setUp() {
        // WebDriver kurulumu
        System.setProperty("webdriver.chrome.driver", "./chromedriver");
        driver = new ChromeDriver();
        driver.manage().window().maximize();
    }
    
    @Test
    public void testScenario() {
        // Test adımları
        // Her adım için ayrıntılı açıklama ve locator ekle
    }
    
    @AfterTest
    public void tearDown() {
        driver.quit();
    }
}
`;
      } else if (format === "cypressjs") {
        formatInstructions = `
# Cypress.js formatında aşağıdaki yapıyı kullan:
describe('[Test paketi adı]', () => {
  beforeEach(() => {
    // Test öncesi hazırlık
    cy.visit('/ilgili-sayfa');
  });
  
  it('[Test senaryo adı]', () => {
    // Test adımları
    // Her adım için açıklama ekle
    // cy.get(), cy.click(), cy.type() gibi metodları uygun şekilde kullan
    // Beklenen sonuç için doğrulama ekle
  });
});
`;
      } else if (format === "playwright") {
        formatInstructions = `
# Playwright formatında aşağıdaki yapıyı kullan:
import { test, expect } from '@playwright/test';

test('[Test senaryo adı]', async ({ page }) => {
  // Test adımları
  await page.goto('/ilgili-sayfa');
  
  // Elementleri bul ve etkileşime geç
  // Tüm adımları await ile işaretle
  
  // Beklenen sonuç için doğrulama ekle
  await expect(page.locator('[seçici]')).toContainText('beklenen metin');
});
`;
      } else if (format === "junit") {
        formatInstructions = `
# JUnit formatında aşağıdaki yapıyı kullan:
import org.junit.Before;
import org.junit.Test;
import org.junit.After;
import static org.junit.Assert.*;

public class [TestSınıfAdı]Test {
    
    @Before
    public void setUp() {
        // Test öncesi hazırlık
    }
    
    @Test
    public void test[TestMetoduAdı]() {
        // Her adım için açıklama ekle
        // Test adımları
        // Beklenen sonuç için assert kullan
    }
    
    @After
    public void tearDown() {
        // Temizlik işlemleri
    }
}
`;
      }
      
      // Adımları doğru formata dönüştür
      let stepsFormatted = "Belirtilmemiş";
      let preconditionsFormatted = "Belirtilmemiş";
      
      // Adımları işle - geliştirilmiş hata yakalama
      if (Array.isArray(scenario.steps)) {
        stepsFormatted = scenario.steps.map((step, i) => `${i+1}. ${step}`).join('\n');
      } else if (typeof scenario.steps === 'string') {
        try {
          if (scenario.steps.trim().startsWith('[')) {
            try {
              // JSON.parse hatasını önlemek için temizleme işlemi ekle
              let input = scenario.steps.trim();
              
              // Escape karakterleri temizle
              input = input.replace(/\\"/g, '"')
                          .replace(/\\'/g, "'")
                          .replace(/\\&/g, "&")
                          .replace(/\\r/g, "\r")
                          .replace(/\\n/g, "\n")
                          .replace(/\\t/g, "\t")
                          .replace(/\\b/g, "\b")
                          .replace(/\\f/g, "\f");
              
              const parsedSteps = JSON.parse(input);
              
              if (Array.isArray(parsedSteps)) {
                // Null ve undefined değerleri filtrele
                const validSteps = parsedSteps.filter(step => step !== null && step !== undefined);
                stepsFormatted = validSteps.map((step: string, i: number) => `${i+1}. ${step}`).join('\n');
              } else {
                stepsFormatted = scenario.steps;
              }
            } catch (jsonErr) {
              console.warn("Adım JSON parse hatası - düz metin olarak işleniyor:", jsonErr);
              // JSON formatı olmadığı durumda satır satır böl (eğer satır atlaması varsa)
              if (scenario.steps.includes('\n')) {
                const lines = scenario.steps.split('\n').filter(line => line.trim());
                stepsFormatted = lines.map((line, i) => 
                  line.trim().startsWith((i+1) + '.') ? line.trim() : `${i+1}. ${line.trim()}`
                ).join('\n');
              } else {
                stepsFormatted = scenario.steps;
              }
            }
          } else {
            // JSON formatı değil, düz metin
            stepsFormatted = scenario.steps;
          }
        } catch (e) {
          console.error("Adımları ayrıştırma hatası:", e);
          // Herhangi bir durumda güvenli dönüş
          stepsFormatted = scenario.steps || '';
        }
      } else {
        // steps değeri beklenmeyen bir tipteyse
        stepsFormatted = '';
      }
      
      // Ön koşulları işle
      if (typeof scenario.preconditions === 'string') {
        try {
          // String boş veya sadece whitespace içeriyorsa direkt boş string döndür
          if (!scenario.preconditions || scenario.preconditions.trim() === '') {
            preconditionsFormatted = 'Belirtilmemiş';
          } 
          // JSON olarak parse etmeyi dene, ancak başta ve sonda boşluk olabilir
          else if (scenario.preconditions.trim().startsWith('{') || scenario.preconditions.trim().startsWith('[')) {
            try {
              // JSON.parse hatasını önlemek için daha gelişmiş bir temizleme işlemi ekle
              let input = scenario.preconditions.trim();
              
              // Başlangıç ve bitiş köşeli parantezleri koruyarak içeriği düzenliyoruz
              const isArray = input.startsWith('[');
              const firstChar = isArray ? '[' : '{';
              const lastChar = isArray ? ']' : '}';
              
              // İçeriği sade metne dönüştürelim (ilk ve son karakteri atarak)
              let content = input.substring(1, input.length - 1);
              
              // Escape karakterleri temizle
              content = content.replace(/\\"/g, '"')
                        .replace(/\\'/g, "'")
                        .replace(/\\&/g, "&")
                        .replace(/\\r/g, "\r")
                        .replace(/\\n/g, "\n")
                        .replace(/\\t/g, "\t")
                        .replace(/\\b/g, "\b")
                        .replace(/\\f/g, "\f");
              
              // Geçersiz JSON formatını düzelt
              content = content.replace(/(['"])?([a-zA-Z0-9_]+)(['"])?:/g, '"$2":');
              
              // Tek tırnak yerine çift tırnak kullan
              content = content.replace(/(\w+):'([^']*)'/g, '"$1":"$2"'); // 'key':'value' -> "key":"value"
              content = content.replace(/'/g, '"');
              
              // JSON içinde özel karakterleri düzelt
              content = content.replace(/\\/g, '\\\\');
              
              // Değer/anahtar ayırıcılarını düzelt
              content = content.replace(/,(\s*[}\]])/g, '$1'); // Sondaki virgülü kaldır [1,2,] -> [1,2]
              
              // Önceden hatalı biçimde dönüştürülmüş olabilecek JSON'ı düzelt
              content = content.replace(/"{/g, '{'); // "{ -> {
              content = content.replace(/}"/g, '}'); // }" -> }
              content = content.replace(/"\[/g, '['); // "[ -> [
              content = content.replace(/\]"/g, ']'); // ]" -> ]
              
              // Yeniden oluştur
              let cleanedInput = firstChar + content + lastChar;
              
              // JSON karakterlerini standartlaştırma
              cleanedInput = cleanedInput.replace(/\u2028/g, '\\n');
              cleanedInput = cleanedInput.replace(/\u2029/g, '\\n');
              
              try {
                // En güvenli yöntem, JSON olarak değil string array olarak işlemek
                if (isArray) {
                  // Köşeli ayraç içindeki elemanları ayrı ayrı parse etmeden ayır
                  const items = content.split('","').map(item => 
                    item.replace(/(^"|"$)/g, '') // Tırnak işaretlerini kaldır
                      .replace(/^\s+|\s+$/g, '') // Boşlukları kırp
                  );
                  
                  // Tüm elemanları birleştir
                  if (items.length === 0) {
                    preconditionsFormatted = 'Belirtilmemiş';
                  } else {
                    preconditionsFormatted = items
                      .filter(item => item && item.length > 0)
                      .map(item => `- ${item}`)
                      .join('\n');
                  }
                } else {
                  // Köşeli ayraç değil ise, anahtar:değer çiftlerini manuel olarak ayırıyoruz
                  const pairs = content.split('","')
                    .map(pair => pair.replace(/(^"|"$)/g, '').split('":'))
                    .filter(pair => pair.length === 2);
                    
                  if (pairs.length === 0) {
                    // Farklı bir yaklaşım dene - satırlara böl
                    const lines = content.split(/[,;]\s*/).map(line => 
                      line.replace(/^["']|["']$/g, '') // Başlangıç ve bitişteki tırnak işaretlerini kaldır
                         .replace(/^\s+|\s+$/g, '') // Boşlukları kırp
                    );
                    
                    if (lines.length === 0) {
                      preconditionsFormatted = 'Belirtilmemiş';
                    } else {
                      preconditionsFormatted = lines
                        .filter(line => line && line.length > 0)
                        .map(line => `- ${line}`)
                        .join('\n');
                    }
                  } else {
                    preconditionsFormatted = pairs
                      .map(([_, value]) => `- ${value.trim()}`)
                      .join('\n');
                  }
                }
              } catch (jsonErr) {
                console.warn("JSON parçalama hatası - alternatif yöntem kullanılıyor:", jsonErr);
                
                // Alternatif: satırlara böl
                if (scenario.preconditions.includes('\n')) {
                  const lines = scenario.preconditions.split('\n')
                    .filter(line => line.trim().length > 0)
                    .map(line => line.trim().startsWith('-') ? line.trim() : `- ${line.trim()}`);
                  
                  preconditionsFormatted = lines.join('\n');
                } else {
                  // Son çare: doğrudan içeriği göster
                  preconditionsFormatted = scenario.preconditions
                    .replace(/{|}|\[|\]/g, '') // Köşeli ve süslü parantezleri kaldır
                    .replace(/"/g, '') // Tırnak işaretlerini kaldır
                    .replace(/,/g, '\n- ') // Virgülleri madde işaretine çevir
                    .replace(/^\s*/, '- '); // Başına madde işareti ekle
                }
              }
            } catch (preprocessErr) {
              console.error("Ön işleme hatası:", preprocessErr);
              
              // Basit bir madde işaretli liste formatına dönüştür
              preconditionsFormatted = scenario.preconditions
                .replace(/{|}|\[|\]/g, '') // Parantezleri kaldır
                .replace(/"/g, '') // Tırnak işaretlerini kaldır
                .replace(/,/g, '\n- ') // Virgülleri madde işaretine çevir
                .replace(/^\s*/, '- '); // Başına madde işareti ekle
            }
          } else {
            preconditionsFormatted = scenario.preconditions;
          }
        } catch (e) {
          console.error("Ön koşulları ayrıştırma hatası:", e);
          // Herhangi bir hata durumunda orijinal string'i kullan
          preconditionsFormatted = scenario.preconditions || 'Belirtilmemiş';
        }
      }
      
      const response = await fetch('/api/ai-assistant', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: `Aşağıdaki test senaryosunu SADECE ${format} formatında otomasyon koduna dönüştür. Asla başka format açıklama veya kod ekleme.
                 ${formatInstructions}
                 
                 Senaryo Başlığı: ${scenario.title}
                 Gereksinim Kodu: ${scenario.requirementCode || 'Belirtilmemiş'}
                 
                 Ön Koşullar:
                 ${preconditionsFormatted}
                 
                 Test Adımları:
                 ${stepsFormatted}
                 
                 Beklenen Sonuç:
                 ${scenario.expectedResults || 'Belirtilmemiş'}
                 
                 Lütfen kodda şunlara dikkat et:
                 1. Uygun element bulma stratejileri kullan (id, class, css, xpath)
                 2. Hata yakalama ve işleme ekle
                 3. Adım adım açıklamalar ekle
                 4. Beklenen sonuçları doğrula
                 5. Kod kalitesine dikkat et (okunabilirlik, bakım kolaylığı)
                 6. Sadece kod üret, açıklama ekleme
                 7. YALNIZca ${format} formatında kod üret, diğer formatlar hakkında bilgi verme!`,
          context: [
            { role: "system", content: `Sen deneyimli bir test otomasyonu uzmanısın. Verilen test senaryolarını SADECE ${format} formatında gerçek ortamda çalışabilir otomatik test kodlarına dönüştürmekle görevlisin. Asla başka format gösterme veya öneri sunma. En güncel ve iyi uygulamaları kullan. Gerçekçi elementler ve seçiciler ekle. Kod bloğu dışında açıklama yapma, sadece çalışabilir kod üret.` }
          ],
          format: format // Format bilgisini API'ye gönder
        }),
      });
      
      const data = await response.json();
      
      if (data && data.answer) {
        // Kod bloğunu çıkar
        const codePattern = /```(?:[\w-]*)\s*([\s\S]*?)```|<code>([\s\S]*?)<\/code>/;
        const match = data.answer.match(codePattern);
        const code = match ? (match[1] || match[2]).trim() : data.answer.trim();
        
        toast({
          title: `${format.toUpperCase()} Kodu Oluşturuldu`,
          description: "Otomasyon kodu başarıyla oluşturuldu",
          variant: "default",
        });
        
        // Kodu bir dialog içinde gösterelim
        setGeneratedCode(code);
        setCodeFormat(format);
        setShowCodeDialog(true);
        // Popup'ı kapat
        setGeneratingCode(false);
      } else {
        throw new Error("AI yanıtı alınamadı");
      }
    } catch (error) {
      console.error("AI kod üretme hatası:", error);
      toast({
        title: "Kod Oluşturma Hatası",
        description: "Otomasyon kodu oluşturulurken bir hata oluştu. Basit format kullanılıyor.",
        variant: "destructive",
      });
      
      // Hata durumunda basit formata geri dön
      const fallbackCode = formatScenarioForExport(scenario, format);
      setGeneratedCode(fallbackCode);
      setCodeFormat(format);
      setShowCodeDialog(true);
      // Popup'ı kapat
      setGeneratingCode(false);
    }
  };
  
  // İyileştirilmiş adımları kabul et
  const acceptImprovedSteps = () => {
    if (!editingScenario || improvedSteps.length === 0) return;
    
    setEditSteps([...improvedSteps]);
    setImprovedSteps([]);
    
    toast({
      title: "İyileştirilmiş Adımlar Uygulandı",
      description: "İyileştirilmiş adımlar başarıyla uygulandı. Değişiklikleri kaydetmek için 'Kaydet' butonuna tıklayın.",
      variant: "default",
    });
  };

  const verifyRequirementCoverage = () => {
    if (!documentId) {
      toast({
        title: "Hata",
        description: "Doküman seçilmemiş. Lütfen önce bir doküman seçin.",
        variant: "destructive",
      });
      return;
    }
    
    toast({
      title: "Kapsama Analizi",
      description: "Doküman kapsama analizi yapılıyor...",
      variant: "default",
    });
    
    validateDocumentCoverage(documentId)
      .then((res) => res.json())
      .then((data) => {
        // AI tabanlı doğrulama sonuçlarını göster
        toast({
          title: `Gereksinim kapsama oranı: %${Math.round(data.overallCoverage * 100)}`,
          description: data.feedbackSummary,
          variant: data.overallCoverage > 0.7 ? "default" : "destructive",
        });
      })
      .catch((error) => {
        // Kaç gereksinim test senaryosu ile kapsanmış hesapla (yerel hesaplama)
        const coveredRequirements = new Set();
        testScenarios.forEach(scenario => {
          if (scenario.requirementCode) {
            coveredRequirements.add(scenario.requirementCode);
          }
        });
        
        // Kapsanmayan gereksinimleri bul
        const uncoveredRequirements = requirements.filter(req => 
          !coveredRequirements.has(req.code)
        );
        
        // Kapsama oranını hesapla
        const coverageRate = requirements.length > 0
          ? Math.round((coveredRequirements.size / requirements.length) * 100)
          : 0;
        
        toast({
          title: `Gereksinim kapsama oranı: %${coverageRate}`,
          description: uncoveredRequirements.length > 0
            ? `Kapsanmayan ${uncoveredRequirements.length} gereksinim var: ${uncoveredRequirements.map(r => r.code).join(', ')}`
            : "Tüm gereksinimler test senaryoları ile kapsanmış!",
          variant: uncoveredRequirements.length > 0 ? "destructive" : "default",
        });
        
        console.error("Doğrulama hatası:", error);
      });
  };

  return (
    <div className="flex-1 p-4 bg-white h-[calc(100vh-12rem)] overflow-y-auto">
      {/* Kod Dialog */}
      <Dialog open={showCodeDialog} onOpenChange={setShowCodeDialog}>
        <DialogContent className="max-w-7xl w-[95vw] max-h-[95vh] overflow-hidden flex flex-col">
          <DialogHeader className="pb-2 border-b">
            <DialogTitle className="text-xl">
              <i className={`${getFormatIcon(codeFormat)} mr-2 text-primary`}></i>
              {codeFormat.toUpperCase()} Formatında Otomasyon Kodu
            </DialogTitle>
            <DialogDescription>
              Bu kod AI tarafından otomatik olarak oluşturulmuştur. İhtiyaca göre özelleştirilebilir.
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex-1 bg-neutral-950 rounded-md overflow-hidden my-4">
            <div className="p-4 flex items-center justify-between bg-neutral-900 border-b border-neutral-800">
              <div className="flex items-center">
                <span className="w-3 h-3 rounded-full bg-red-500 mx-1"></span>
                <span className="w-3 h-3 rounded-full bg-yellow-500 mx-1"></span>
                <span className="w-3 h-3 rounded-full bg-green-500 mx-1"></span>
                <span className="ml-3 text-xs text-neutral-400 font-mono">{codeFormat === 'gherkin' ? 'feature' : codeFormat === 'selenium' || codeFormat === 'junit' ? 'java' : codeFormat === 'cypressjs' ? 'javascript' : codeFormat}.{codeFormat === 'gherkin' ? 'feature' : codeFormat === 'selenium' || codeFormat === 'junit' ? 'java' : 'js'}</span>
              </div>
              <div className="flex items-center gap-2">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-neutral-400 hover:text-white hover:bg-neutral-800"
                  onClick={() => {
                    try {
                      navigator.clipboard.writeText(generatedCode);
                      toast({
                        title: "Kopyalandı",
                        description: "Otomasyon kodu başarıyla panoya kopyalandı.",
                        variant: "default",
                      });
                    } catch (error) {
                      toast({
                        title: "Kopyalama Hatası",
                        description: "Tarayıcınız kopyalamaya izin vermiyor. Lütfen manuel olarak kopyalayın.",
                        variant: "destructive",
                      });
                    }
                  }}
                >
                  <i className="fas fa-copy mr-2"></i>
                  Kopyala
                </Button>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-neutral-400 hover:text-white hover:bg-neutral-800"
                  onClick={() => {
                    const element = document.createElement('a');
                    const file = new Blob([generatedCode], {type: 'text/plain'});
                    element.href = URL.createObjectURL(file);
                    element.download = `test_${codeFormat === 'gherkin' ? 'feature' : codeFormat === 'selenium' || codeFormat === 'junit' ? 'java' : 'js'}`;
                    document.body.appendChild(element);
                    element.click();
                    document.body.removeChild(element);
                    toast({
                      title: "Dosya İndirildi",
                      description: "Otomasyon kodu dosyası başarıyla indirildi.",
                      variant: "default",
                    });
                  }}
                >
                  <i className="fas fa-download mr-2"></i>
                  İndir
                </Button>
              </div>
            </div>
            <div className="p-6 overflow-auto max-h-[70vh]">
              <pre className="text-sm font-mono leading-relaxed whitespace-pre-wrap">
                <code className="text-neutral-100">{generatedCode}</code>
              </pre>
            </div>
          </div>
          
          <DialogFooter className="flex items-center justify-between pt-2 border-t">
            <div className="flex items-center text-xs text-neutral-500">
              <i className="fas fa-info-circle mr-2"></i>
              AI tabanlı otomasyon kodları gerçek uygulamada çalışmadan önce test edilmelidir.
            </div>
            <div>
              <Button 
                variant="default" 
                size="sm"
                className="bg-primary hover:bg-primary/80 text-white" 
                onClick={() => setShowCodeDialog(false)}
              >
                Kapat
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Test Senaryoları Oluşturuluyor Popup */}
      {generatingPopupOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 mb-4 flex items-center justify-center">
                <svg className="animate-spin h-12 w-12 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-neutral-800 mb-2">Test Senaryoları Oluşturuluyor</h3>
              <p className="text-sm text-neutral-600 text-center mb-4">
                Dokümanınızdaki gereksinimler için kapsamlı test senaryoları oluşturuluyor. 
                Bu işlem 1-3 dakika sürebilir. Lütfen bekleyin...
              </p>
              <div className="w-full bg-neutral-200 rounded-full h-2.5">
                <div className="bg-primary h-2.5 rounded-full animate-pulse w-full"></div>
              </div>
              <p className="text-xs text-neutral-500 mt-4 italic">
                AI modelleri kapsamlı analiz yapmaktadır. Bu süre zarfında başka bir işlem yapmayabilirsiniz.
              </p>
            </div>
          </div>
        </div>
      )}
      
      {/* Otomasyon Kodu Oluşturuluyor Popup */}
      {generatingCode && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 mb-4 flex items-center justify-center">
                <svg className="animate-spin h-12 w-12 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-neutral-800 mb-2">Otomasyon Kodu Oluşturuluyor</h3>
              <p className="text-sm text-neutral-600 text-center mb-4">
                Seçtiğiniz test senaryosu için otomasyon kodu hazırlanıyor. 
                Lütfen bekleyin...
              </p>
              <div className="w-full bg-neutral-200 rounded-full h-2.5">
                <div className="bg-primary h-2.5 rounded-full animate-pulse w-full"></div>
              </div>
              <p className="text-xs text-neutral-500 mt-4 italic">
                AI çalışır kod üretmek için test adımlarını analiz ediyor.
              </p>
            </div>
          </div>
        </div>
      )}
      
      <div className="max-w-6xl mx-auto">
        <div className="flex flex-wrap justify-between items-center mb-6 gap-4">
          <h2 className="text-xl font-semibold text-neutral-800">Test Senaryoları</h2>
          
          <div className="flex gap-2">
            {testScenarios.length > 0 && (
              <>
                <Dialog open={exportModalOpen} onOpenChange={setExportModalOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm" className="border-blue-500 text-blue-700 hover:bg-blue-50 font-medium">
                      <i className="fas fa-file-export mr-2 text-blue-600"></i>
                      Dışa Aktar
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Test Senaryolarını Dışa Aktar</DialogTitle>
                      <DialogDescription>
                        Filtrelenmiş {filteredScenarios.length} test senaryosunu hangi formatta dışa aktarmak istiyorsunuz?
                      </DialogDescription>
                    </DialogHeader>
                    
                    <div className="mt-2 mb-4">
                      <h3 className="text-base font-medium text-neutral-700 mb-2">Test Araçları</h3>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <Button variant="outline" onClick={() => exportScenarios("jira")}>
                          <i className="fab fa-jira mr-2 text-blue-600"></i>
                          Jira
                        </Button>
                        <Button variant="outline" onClick={() => exportScenarios("alm")}>
                          <i className="fas fa-project-diagram mr-2 text-purple-600"></i>
                          ALM
                        </Button>
                        <Button variant="outline" onClick={() => exportScenarios("testrail")}>
                          <i className="fas fa-tasks mr-2 text-green-600"></i>
                          TestRail
                        </Button>
                        <Button variant="outline" onClick={() => exportScenarios("visium")}>
                          <i className="fas fa-check-circle mr-2 text-amber-600"></i>
                          Visium Manage
                        </Button>
                      </div>
                    </div>
                    
                    <div className="mb-2">
                      <h3 className="text-base font-medium text-neutral-700 mb-2">Doküman</h3>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <Button variant="outline" onClick={() => exportScenarios("excel")}>
                          <i className="fas fa-file-excel mr-2 text-green-600"></i>
                          Excel
                        </Button>
                        <Button variant="outline" onClick={() => exportScenarios("pdf")}>
                          <i className="fas fa-file-pdf mr-2 text-red-600"></i>
                          PDF
                        </Button>
                        <Button variant="outline" onClick={() => exportScenarios("csv")}>
                          <i className="fas fa-file-csv mr-2 text-blue-600"></i>
                          CSV
                        </Button>
                        <Button variant="outline" onClick={() => exportScenarios("json")}>
                          <i className="fas fa-file-code mr-2 text-amber-600"></i>
                          JSON
                        </Button>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="secondary" onClick={() => setExportModalOpen(false)}>İptal</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </>
            )}
            
            <div className="flex gap-2 items-center">

              
              <Button
                onClick={() => {
                  // Eğer test senaryoları varsa, önce silme işlemi yapılmalı
                  if (testScenarios.length > 0) {
                    // Önce mevcut senaryoları sil
                    deleteAllScenariosMutation.mutate();
                    // Silme başarılı olduğunda generatemutation.mutate() otomatik olarak çağrılacak
                  } else {
                    // Doğrudan oluştur
                    generateMutation.mutate();
                  }
                }}
                disabled={generateMutation.isPending || deleteAllScenariosMutation.isPending || !documentId}
                className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-white font-medium shadow-md"
                size="lg"
              >
                {generateMutation.isPending || deleteAllScenariosMutation.isPending ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>{deleteAllScenariosMutation.isPending ? "Senaryolar Siliniyor..." : "Oluşturuluyor..."}</span>
                  </>
                ) : (
                  <>
                    <i className="fas fa-wand-magic-sparkles mr-2 text-lg"></i>
                    <span>{testScenarios.length > 0 ? "Test Senaryolarını Yeniden Oluştur" : "Test Senaryoları Oluştur"}</span>
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        {!documentId && (
          <div className="text-center p-10 border-2 border-dashed border-neutral-300 rounded-lg">
            <i className="fas fa-file-alt text-4xl text-neutral-400 mb-3"></i>
            <p className="text-neutral-600">Lütfen test senaryoları oluşturmak için bir doküman seçin</p>
          </div>
        )}

        {documentId && requirements.length === 0 && (
          <div className="text-center p-10 border-2 border-dashed border-neutral-300 rounded-lg">
            <i className="fas fa-exclamation-circle text-4xl text-amber-500 mb-3"></i>
            <p className="text-neutral-600">Bu doküman için henüz gereksinim tespit edilmemiş</p>
            <p className="text-sm text-neutral-500 mt-2">Test senaryolarının oluşturulabilmesi için önce dokümanın analiz edilmesi gerekiyor</p>
          </div>
        )}

        {documentId && requirements.length > 0 && testScenarios.length === 0 && (
          <div className="text-center p-10 border-2 border-dashed border-neutral-300 rounded-lg">
            <i className="fas fa-tasks text-4xl text-neutral-400 mb-3"></i>
            <p className="text-neutral-600">Henüz test senaryosu oluşturulmamış</p>
            <p className="text-sm text-neutral-500 mt-2">Test senaryoları oluşturmak için yukarıdaki butona tıklayın</p>
          </div>
        )}

        {documentId && testScenarios.length > 0 && (
          <>
            <div className="mb-6 flex flex-wrap gap-4">
              <div className="flex-1 min-w-[250px]">
                <Select value={selectedFormat} onValueChange={setSelectedFormat}>
                  <SelectTrigger>
                    <SelectValue placeholder="Format seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tüm Formatlar</SelectItem>
                    {Object.keys(formatGroups).map(format => (
                      <SelectItem key={format} value={format}>
                        {format === "default" ? "Standart" : format.charAt(0).toUpperCase() + format.slice(1)}
                        {" "}({formatGroups[format].length})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex-1 min-w-[250px]">
                <Select value={selectedRequirement} onValueChange={setSelectedRequirement}>
                  <SelectTrigger>
                    <SelectValue placeholder="Gereksinim seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tüm Gereksinimler</SelectItem>
                    {Object.keys(requirementGroups).map(reqCode => (
                      <SelectItem key={reqCode} value={reqCode}>
                        {reqCode === "uncategorized" ? "Kategorilendirilmemiş" : reqCode}
                        {" "}({requirementGroups[reqCode].length})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <Tabs defaultValue="list" className="mb-6">
              <TabsList>
                <TabsTrigger value="list">
                  <i className="fas fa-list-ul mr-2"></i>
                  Liste Görünümü
                </TabsTrigger>
                <TabsTrigger value="cards">
                  <i className="fas fa-th-large mr-2"></i>
                  Kart Görünümü
                </TabsTrigger>

              </TabsList>
              
              <TabsContent value="list" className="mt-4">
                <div className="space-y-4">
                  {filteredScenarios.length === 0 ? (
                    <div className="text-center p-6 bg-neutral-50 rounded-lg">
                      <i className="fas fa-filter text-neutral-400"></i>
                      <p className="text-neutral-500 mt-2">Seçili filtrelere uygun senaryo bulunamadı</p>
                    </div>
                  ) : (
                    filteredScenarios.map((scenario) => (
                      <div 
                        key={scenario.id} 
                        className="border border-neutral-200 rounded-lg overflow-hidden"
                      >
                        <div 
                          className="p-4 bg-white hover:bg-neutral-50 cursor-pointer flex justify-between items-center"
                          onClick={() => toggleScenario(scenario.id)}
                        >
                          <div className="flex items-center">
                            <i className={`${getFormatIcon(scenario.format || 'gherkin')} text-primary mr-3`}></i>
                            <div>
                              <h3 className="font-medium">{scenario.title}</h3>
                              <div className="flex flex-wrap gap-2 mt-1">
                                {scenario.requirementCode && (
                                  <Badge variant="outline" className="text-xs">
                                    {scenario.requirementCode}
                                  </Badge>
                                )}
                                {scenario.format && (
                                  <Badge variant="outline" className={`text-xs ${getFormatBadgeColor(scenario.format)}`}>
                                    {scenario.format}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                          <i className={`fas fa-chevron-${expandedScenario === scenario.id ? 'up' : 'down'} text-neutral-400`}></i>
                        </div>
                        
                        {expandedScenario === scenario.id && (
                          <div className="p-4 border-t border-neutral-200 bg-neutral-50">
                            <div className="mb-4">
                              <div className="flex justify-between items-center mb-2">
                                <h4 className="text-sm font-medium text-neutral-700">
                                  <i className={`${getFormatIcon(scenario.format || 'gherkin')} mr-2`}></i>
                                  {scenario.format ? `${scenario.format.charAt(0).toUpperCase() + scenario.format.slice(1)} Formatında Görünüm` : 'Test Senaryosu'}
                                </h4>
                                <div className="flex gap-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      startEditingScenario(scenario);
                                    }}
                                    className="text-xs border-blue-500 text-blue-700 hover:bg-blue-50 font-medium"
                                  >
                                    <i className="fas fa-edit text-blue-600 mr-1"></i>
                                    Düzenle
                                  </Button>
                                  <Button 
                                    variant="ghost" 
                                    size="sm"
                                    onClick={() => {
                                      navigator.clipboard.writeText(formatScenarioForExport(scenario, scenario.format || 'default'));
                                      toast({
                                        title: "Kopyalandı",
                                        description: `Test senaryosu ${scenario.format || 'standart'} formatında panoya kopyalandı`,
                                        variant: "default",
                                      });
                                    }}
                                  >
                                    <i className="fas fa-copy text-xs"></i>
                                  </Button>
                                </div>
                              </div>
                              

                            </div>
                            
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div className="bg-white rounded-md p-3 border border-neutral-200 shadow-sm">
                                <h4 className="text-sm font-medium text-neutral-700 border-b pb-2 mb-2 flex items-center">
                                  <i className="fas fa-clipboard-check mr-2 text-primary"></i>
                                  Ön Koşullar
                                </h4>
                                <p className="text-sm">
                                  {typeof scenario.preconditions === 'string' 
                                    ? scenario.preconditions.startsWith('[') 
                                      ? JSON.parse(scenario.preconditions).join(', ') 
                                      : scenario.preconditions.startsWith('{') 
                                        ? (() => {
                                            try {
                                              // JSON formatını koruyoruz, sadece formatlanmış halini gösteriyoruz
                                              const parsed = JSON.parse(scenario.preconditions);
                                              // Formatlanmış JSON'ı pre etiketinde gösteriyoruz
                                              return <pre className="whitespace-pre-wrap bg-white/50 p-1 rounded-sm text-xs overflow-auto">
                                                {JSON.stringify(parsed, null, 2)}
                                              </pre>;
                                            } catch (e) {
                                              return scenario.preconditions;
                                            }
                                          })()
                                        : scenario.preconditions
                                    : Array.isArray(scenario.preconditions) 
                                      ? scenario.preconditions.join(', ') 
                                      : 'Belirtilmemiş'}
                                </p>
                              </div>

                              <div className="bg-white rounded-md p-3 border border-neutral-200 shadow-sm md:col-span-2">
                                <h4 className="text-sm font-medium text-neutral-700 border-b pb-2 mb-2 flex items-center">
                                  <i className="fas fa-list-ol mr-2 text-primary"></i>
                                  Test Adımları
                                </h4>
                                {Array.isArray(scenario.steps) && scenario.steps.length > 0 ? (
                                  <ol className="list-decimal list-inside text-sm space-y-1 pl-2">
                                    {scenario.steps.map((step, index) => (
                                      <li key={index} className="py-1">{step}</li>
                                    ))}
                                  </ol>
                                ) : typeof scenario.steps === 'string' && scenario.steps.startsWith('[') ? (
                                  <ol className="list-decimal list-inside text-sm space-y-1 pl-2">
                                    {(() => {
                                      try {
                                        const input = typeof scenario.steps === 'string' ? scenario.steps.trim() : JSON.stringify(scenario.steps);
                                        
                                        // Escape karakterleri temizle
                                        const cleanedInput = input
                                          .replace(/\\"/g, '"')
                                          .replace(/\\'/g, "'")
                                          .replace(/\\&/g, "&")
                                          .replace(/\\r/g, "\r")
                                          .replace(/\\n/g, "\n")
                                          .replace(/\\t/g, "\t")
                                          .replace(/\\b/g, "\b")
                                          .replace(/\\f/g, "\f");
                                        
                                        const parsedSteps = JSON.parse(cleanedInput);
                                        
                                        if (!Array.isArray(parsedSteps)) {
                                          return ["Adımlar geçerli bir formatta değil"];
                                        }
                                        
                                        return parsedSteps.filter(step => step !== null && step !== undefined);
                                      } catch (e) {
                                        console.error("Adım ayrıştırma hatası:", e);
                                        return ["Adımlar ayrıştırılamadı"];
                                      }
                                    })().map((step: string, index: number) => (
                                      <li key={index} className="py-1">{step}</li>
                                    ))}
                                  </ol>
                                ) : (
                                  <p className="text-sm text-neutral-500 italic">Adım bilgisi bulunamadı</p>
                                )}
                              </div>
                            </div>
                            
                            <div className="mt-4 bg-green-50 border border-green-200 rounded-md p-3 shadow-sm">
                              <h4 className="text-sm font-medium text-green-800 border-b border-green-200 pb-2 mb-2 flex items-center">
                                <i className="fas fa-check-circle mr-2 text-green-600"></i>
                                Beklenen Sonuç
                              </h4>
                              <div className="text-sm text-green-800">
                                {typeof scenario.expectedResults === 'string' 
                                  ? (scenario.expectedResults.trim().startsWith('{') || scenario.expectedResults.trim().startsWith('['))
                                    ? <pre className="whitespace-pre-wrap overflow-auto max-h-40 text-xs bg-green-900/10 p-2 rounded">
                                        {(() => {
                                          try {
                                            // JSON.parse hatasını önlemek için temizleme işlemi ekle
                                            let input = scenario.expectedResults.trim();
                                            
                                            // Escape karakterleri temizle
                                            input = input.replace(/\\"/g, '"')
                                                      .replace(/\\'/g, "'")
                                                      .replace(/\\&/g, "&")
                                                      .replace(/\\r/g, "\r")
                                                      .replace(/\\n/g, "\n")
                                                      .replace(/\\t/g, "\t")
                                                      .replace(/\\b/g, "\b")
                                                      .replace(/\\f/g, "\f");
                                                      
                                            // Geçersiz JSON formatını düzelt
                                            input = input.replace(/(['"])?([a-zA-Z0-9_]+)(['"])?:/g, '"$2":');
                                            input = input.replace(/'/g, '"');
                                            
                                            const parsed = JSON.parse(input);
                                            return JSON.stringify(parsed, null, 2);
                                          } catch (e) {
                                            return scenario.expectedResults;
                                          }
                                        })()}
                                      </pre>
                                    : scenario.expectedResults
                                  : 'Belirtilmemiş'}
                              </div>
                            </div>
                            
                            <div className="flex flex-wrap gap-2 mt-4 justify-end">
                              <div className="flex-1">
                                <h4 className="text-sm font-medium text-neutral-700 mb-2 flex items-center">
                                  <i className="fas fa-robot mr-2 text-primary"></i>
                                  AI ile Otomasyon Kodu Oluşturma
                                </h4>
                                <p className="text-xs text-neutral-500 mb-2">
                                  Senaryo bilgilerini kullanarak aşağıdaki otomasyon formatlarında AI ile otomatik kod oluşturun:
                                </p>
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                                  <Button variant="outline" size="sm" onClick={() => generateAutomationCode(scenario, "gherkin")} className="bg-gradient-to-r from-green-50 to-green-100 border-green-200 hover:bg-green-100">
                                    <i className="fas fa-leaf mr-1 text-green-600"></i>
                                    Gherkin
                                  </Button>
                                  
                                  <Button variant="outline" size="sm" onClick={() => generateAutomationCode(scenario, "selenium")} className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200 hover:bg-blue-100">
                                    <i className="fas fa-robot mr-1 text-blue-600"></i>
                                    Selenium
                                  </Button>
                                  
                                  <Button variant="outline" size="sm" onClick={() => generateAutomationCode(scenario, "cucumber")} className="bg-gradient-to-r from-green-50 to-green-100 border-green-200 hover:bg-green-100">
                                    <i className="fas fa-seedling mr-1 text-green-700"></i>
                                    Cucumber
                                  </Button>
                                  
                                  <Button variant="outline" size="sm" onClick={() => generateAutomationCode(scenario, "visiumgo")} className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200 hover:bg-purple-100">
                                    <i className="fas fa-vial mr-1 text-purple-600"></i>
                                    Visium Go
                                  </Button>
                                </div>
                              </div>
                              
                              <div>
                                {/* Doğrula butonu kaldırıldı */}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    ))
                  )}
                </div>
              </TabsContent>
              
              <TabsContent value="cards" className="mt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredScenarios.length === 0 ? (
                    <div className="text-center p-6 bg-neutral-50 rounded-lg col-span-full">
                      <i className="fas fa-filter text-neutral-400"></i>
                      <p className="text-neutral-500 mt-2">Seçili filtrelere uygun senaryo bulunamadı</p>
                    </div>
                  ) : (
                    filteredScenarios.map((scenario) => (
                      <Card key={scenario.id}>
                        <CardHeader className="pb-2">
                          <div className="flex justify-between items-start">
                            <CardTitle className="text-base">{scenario.title}</CardTitle>
                            <i className={`${getFormatIcon(scenario.format || 'gherkin')} text-primary`}></i>
                          </div>
                          <CardDescription className="flex flex-wrap gap-2 mt-1">
                            {scenario.requirementCode && (
                              <Badge variant="outline" className="text-xs">
                                {scenario.requirementCode}
                              </Badge>
                            )}
                            {scenario.format && (
                              <Badge variant="outline" className={`text-xs ${getFormatBadgeColor(scenario.format)}`}>
                                {scenario.format}
                              </Badge>
                            )}
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="pb-2">
                          <div className="mb-2 overflow-hidden rounded border border-neutral-100 bg-neutral-50">
                            <div className="bg-neutral-800 text-neutral-200 px-2 py-1 text-[10px] flex justify-between items-center">
                              <span>{scenario.format || 'default'}.{scenario.format === 'gherkin' ? 'feature' : scenario.format === 'selenium' ? 'java' : 'js'}</span>
                              <button 
                                className="hover:text-white" 
                                onClick={(e) => {
                                  e.stopPropagation();
                                  navigator.clipboard.writeText(formatScenarioForExport(scenario, scenario.format || 'gherkin'));
                                  toast({ 
                                    title: "Kopyalandı",
                                    description: `Kod formatında kopyalandı`,
                                    variant: "default",
                                  });
                                }}
                              >
                                <i className="fas fa-copy text-[10px]"></i>
                              </button>
                            </div>
                            <pre className="p-2 text-[10px] overflow-hidden max-h-16 line-clamp-3">
                              <code>
                                {formatScenarioForExport(scenario, scenario.format || 'gherkin').split('\n').slice(0, 3).join('\n')}
                                {formatScenarioForExport(scenario, scenario.format || 'gherkin').split('\n').length > 3 ? '...' : ''}
                              </code>
                            </pre>
                          </div>
                          
                          <div className="flex gap-2 text-xs">
                            <div className="flex-1 bg-neutral-50 rounded p-1 border border-neutral-100">
                              <div className="flex items-center text-xs font-medium text-neutral-600 mb-1">
                                <i className="fas fa-list-ol mr-1 text-primary text-[10px]"></i>
                                Adımlar
                              </div>
                              <p className="text-[10px] text-neutral-500">
                                {Array.isArray(scenario.steps) && scenario.steps.length > 0
                                  ? `${scenario.steps.length} adım`
                                  : typeof scenario.steps === 'string' && scenario.steps.trim().startsWith('[')
                                    ? (() => {
                                        try {
                                          const input = scenario.steps.trim();
                                          
                                          // Escape karakterleri temizle
                                          const cleanedInput = input
                                            .replace(/\\"/g, '"')
                                            .replace(/\\'/g, "'")
                                            .replace(/\\&/g, "&")
                                            .replace(/\\r/g, "\r")
                                            .replace(/\\n/g, "\n")
                                            .replace(/\\t/g, "\t")
                                            .replace(/\\b/g, "\b")
                                            .replace(/\\f/g, "\f");
                                          
                                          const parsedSteps = JSON.parse(cleanedInput);
                                          return Array.isArray(parsedSteps) ? `${parsedSteps.length} adım` : "Adımlar";
                                        } catch (e) {
                                          return "Belirtilmemiş";
                                        }
                                      })()
                                    : "Belirtilmemiş"}
                              </p>
                            </div>
                            
                            <div className="flex-1 bg-green-50 rounded p-1 border border-green-100">
                              <div className="flex items-center text-xs font-medium text-green-600 mb-1">
                                <i className="fas fa-check-circle mr-1 text-[10px]"></i>
                                Sonuç
                              </div>
                              <div className="text-[10px] text-green-600 line-clamp-1">
                                {typeof scenario.expectedResults === 'string' 
                                  ? scenario.expectedResults.startsWith('{') || scenario.expectedResults.startsWith('[')
                                    ? "JSON formatında sonuç içeriyor"
                                    : scenario.expectedResults
                                  : "Belirtilmemiş"}
                              </div>
                            </div>
                          </div>
                        </CardContent>
                        <CardFooter className="pt-2 flex justify-between">
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm" onClick={() => toggleScenario(scenario.id)}>
                              <i className="fas fa-expand-alt mr-1 text-xs"></i>
                              Detaylar
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm"
                              className="border-blue-500 text-blue-700 hover:bg-blue-50"
                              onClick={() => startEditingScenario(scenario)}
                            >
                              <i className="fas fa-edit mr-1 text-xs"></i>
                              Düzenle
                            </Button>

                          </div>
                          
                          <div className="flex gap-1">
                            <Button variant="outline" className="h-8 px-2" size="sm" onClick={() => {
                              navigator.clipboard.writeText(formatScenarioForExport(scenario, "gherkin"));
                              toast({
                                title: "Kopyalandı",
                                description: `Gherkin formatında panoya kopyalandı`,
                                variant: "default",
                              });
                            }}>
                              <i className="fas fa-leaf text-xs text-green-600"></i>
                            </Button>
                            <Button variant="outline" className="h-8 px-2" size="sm" onClick={() => {
                              navigator.clipboard.writeText(formatScenarioForExport(scenario, "selenium"));
                              toast({
                                title: "Kopyalandı",
                                description: `Selenium formatında panoya kopyalandı`,
                                variant: "default",
                              });
                            }}>
                              <i className="fas fa-robot text-xs text-blue-600"></i>
                            </Button>
                            <Button variant="outline" className="h-8 px-2" size="sm" onClick={() => {
                              navigator.clipboard.writeText(formatScenarioForExport(scenario, "cucumber"));
                              toast({
                                title: "Kopyalandı",
                                description: `Cucumber formatında panoya kopyalandı`,
                                variant: "default",
                              });
                            }}>
                              <i className="fas fa-seedling text-xs text-green-700"></i>
                            </Button>
                            <Button variant="outline" className="h-8 px-2" size="sm" onClick={() => {
                              navigator.clipboard.writeText(formatScenarioForExport(scenario, "visiumgo"));
                              toast({
                                title: "Kopyalandı",
                                description: `Visium Go formatında panoya kopyalandı`,
                                variant: "default",
                              });
                            }}>
                              <i className="fas fa-vial text-xs text-purple-600"></i>
                            </Button>
                          </div>
                        </CardFooter>
                      </Card>
                    ))
                  )}
                </div>
              </TabsContent>
              
              <TabsContent value="stats" className="mt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Format Dağılımı</CardTitle>
                      <CardDescription>Test senaryolarının format türlerine göre dağılımı</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {statistics.byFormat.map(({ format, count, percentage }) => (
                          <div key={format} className="space-y-1">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <i className={`${getFormatIcon(format)} text-primary mr-2`}></i>
                                <span className="text-sm font-medium">
                                  {format === "default" ? "Standart" : format.charAt(0).toUpperCase() + format.slice(1)}
                                </span>
                              </div>
                              <span className="text-sm text-neutral-500">{count} senaryo</span>
                            </div>
                            <div className="h-2 bg-neutral-100 rounded-full overflow-hidden">
                              <div 
                                className="h-full bg-primary" 
                                style={{ width: `${percentage}%` }}
                              ></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Gereksinim Kapsama</CardTitle>
                      <CardDescription>Test senaryolarının gereksinimlere göre dağılımı</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {statistics.byRequirement.map(({ requirementCode, count, percentage }) => (
                          <div key={requirementCode} className="space-y-1">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium">
                                {requirementCode === "uncategorized" ? "Kategorilendirilmemiş" : requirementCode}
                              </span>
                              <span className="text-sm text-neutral-500">{count} senaryo</span>
                            </div>
                            <div className="h-2 bg-neutral-100 rounded-full overflow-hidden">
                              <div 
                                className="h-full bg-primary" 
                                style={{ width: `${percentage}%` }}
                              ></div>
                            </div>
                          </div>
                        ))}
                      </div>
                      
                      <div className="mt-6">
                        <p className="text-sm text-neutral-700">
                          <span className="font-medium">Gereksinim Kapsama Oranı:</span>{" "}
                          {requirements.length > 0
                            ? `%${Math.round((Object.keys(requirementGroups).filter(r => r !== "uncategorized").length / requirements.length) * 100)}`
                            : "Hesaplanamadı"}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </>
        )}
      </div>

      {isEditing && editingScenario && (
        <Dialog open={isEditing} onOpenChange={(open) => !open && cancelEditing()}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {isImproving ? "Test Senaryosu Adımlarını İyileştir" : "Test Senaryosu Düzenle"}
              </DialogTitle>
              <DialogDescription>
                {isImproving ? "AI test senaryosu adımlarını iyileştiriyor..." : "Test senaryosu içeriğini düzenleyebilirsiniz."}
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-1 gap-2">
                <label htmlFor="title" className="text-sm font-medium text-neutral-700">Başlık</label>
                <input
                  id="title"
                  className="flex h-10 w-full rounded-md border border-neutral-200 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-neutral-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={editTitle}
                  onChange={(e) => setEditTitle(e.target.value)}
                  placeholder="Senaryo başlığı"
                  disabled={isImproving}
                />
              </div>

              <div className="grid grid-cols-1 gap-2">
                <label htmlFor="preconditions" className="text-sm font-medium text-neutral-700">Ön Koşullar</label>
                <textarea
                  id="preconditions"
                  className="flex min-h-[80px] w-full rounded-md border border-neutral-200 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-neutral-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="Ön koşulları girin"
                  value={editPreconditions}
                  onChange={(e) => setEditPreconditions(e.target.value)}
                  disabled={isImproving}
                />
              </div>

              <div className="grid grid-cols-1 gap-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium text-neutral-700">Test Adımları</label>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={addNewStep}
                    className="text-xs"
                    disabled={isImproving}
                  >
                    <i className="fas fa-plus mr-1"></i>
                    Adım Ekle
                  </Button>
                </div>
                
                {isImproving ? (
                  <div className="border border-purple-200 rounded-md p-4 bg-purple-50">
                    <div className="flex items-center mb-3">
                      <div className="animate-spin mr-2">
                        <i className="fas fa-spinner text-purple-500"></i>
                      </div>
                      <p className="text-purple-700 font-medium">AI adımları iyileştiriyor...</p>
                    </div>
                    <p className="text-sm text-purple-600">
                      Yapay zeka, test senaryosu adımlarını analiz ediyor ve iyileştirme önerileri hazırlıyor. 
                      Bu işlem birkaç saniye sürebilir.
                    </p>
                  </div>
                ) : (
                  <>
                    {editSteps.length === 0 ? (
                      <div className="text-center p-6 border border-dashed border-neutral-300 rounded-md">
                        <p className="text-neutral-500">Adım eklemek için "Adım Ekle" butonuna tıklayın</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {editSteps.map((step, index) => (
                          <div key={index} className="flex items-start gap-2">
                            <div className="rounded-md flex items-center justify-center bg-neutral-100 text-neutral-800 h-8 w-8 flex-shrink-0 mt-1">
                              {index + 1}
                            </div>
                            <textarea
                              className="flex min-h-[60px] w-full rounded-md border border-neutral-200 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-neutral-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                              placeholder={`Adım ${index + 1}`}
                              value={step}
                              onChange={(e) => updateStep(index, e.target.value)}
                            />
                            <Button 
                              variant="ghost" 
                              size="icon"
                              onClick={() => removeStep(index)}
                              className="h-8 w-8 mt-1 text-red-500 hover:text-red-700 hover:bg-red-50"
                            >
                              <i className="fas fa-trash text-xs"></i>
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </>
                )}
                
                {improvedSteps.length > 0 && (
                  <div className="border border-green-200 rounded-md p-4 bg-green-50 mt-2">
                    <div className="flex items-center justify-between mb-3">
                      <p className="text-green-700 font-medium">İyileştirilmiş Adımlar</p>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={acceptImprovedSteps}
                        className="text-xs border-green-500 text-green-700 hover:bg-green-100"
                      >
                        <i className="fas fa-check mr-1"></i>
                        Adımları Kabul Et
                      </Button>
                    </div>
                    <ol className="list-decimal list-inside space-y-1 pl-2 text-green-800">
                      {improvedSteps.map((step, index) => (
                        <li key={index} className="py-1">{step}</li>
                      ))}
                    </ol>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 gap-2">
                <label htmlFor="expectedResults" className="text-sm font-medium text-neutral-700">Beklenen Sonuçlar</label>
                <textarea
                  id="expectedResults"
                  className="flex min-h-[80px] w-full rounded-md border border-neutral-200 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-neutral-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="Beklenen sonuçları girin"
                  value={editExpectedResults}
                  onChange={(e) => setEditExpectedResults(e.target.value)}
                  disabled={isImproving}
                />
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={cancelEditing}>İptal</Button>
              <Button 
                type="submit" 
                onClick={saveEditedScenario}
                disabled={updateScenarioMutation.isPending || isImproving}
                className="bg-primary text-white"
              >
                {updateScenarioMutation.isPending ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Kaydediliyor...
                  </>
                ) : "Kaydet"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default TestScenarios;