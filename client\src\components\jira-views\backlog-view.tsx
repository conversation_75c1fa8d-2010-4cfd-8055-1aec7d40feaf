import React, { use<PERSON>emo, useState } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Plus,
  User,
  MessageSquare,
  Calendar,
  ChevronDown,
  ChevronRight,
  Target,
  Zap,
  BookOpen,
  Bug,
  CheckSquare,
  MoreHorizontal,
  GripVertical
} from 'lucide-react';

interface JiraIssue {
  id: string;
  key: string;
  summary: string;
  description: string;
  issueType: string;
  status: string;
  priority: string;
  assignee?: string;
  reporter?: string;
  created: string;
  updated: string;
  comments: Array<{
    id: string;
    author: string;
    body: string;
    created: string;
  }>;
}

interface BacklogViewProps {
  issues: JiraIssue[];
  onIssueClick: (issue: JiraIssue) => void;
  getPriorityBadge: (priority: string) => React.ReactNode;
  getStatusBadge: (status: string) => React.ReactNode;
  getTypeBadge: (type: string) => React.ReactNode;
}

interface BacklogGroup {
  id: string;
  title: string;
  issues: JiraIssue[];
  isExpanded: boolean;
  color: string;
  icon: React.ReactNode;
}

export default function BacklogView({
  issues,
  onIssueClick,
  getPriorityBadge,
  getStatusBadge,
  getTypeBadge
}: BacklogViewProps) {
  
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set(['backlog', 'current-sprint']));
  const [newSprintName, setNewSprintName] = useState('');
  const [isCreatingSprint, setIsCreatingSprint] = useState(false);

  // Issues'ları gruplara ayır
  const backlogGroups = useMemo((): BacklogGroup[] => {
    const groups: BacklogGroup[] = [];

    // Aktif Sprint (In Progress ve In Review durumundaki issue'lar)
    const activeSprintIssues = issues.filter(issue => 
      issue.status === 'In Progress' || issue.status === 'In Review'
    );
    
    if (activeSprintIssues.length > 0) {
      groups.push({
        id: 'current-sprint',
        title: 'Aktif Sprint',
        issues: activeSprintIssues,
        isExpanded: expandedGroups.has('current-sprint'),
        color: 'bg-blue-50 border-blue-200',
        icon: <Zap size={16} className="text-blue-600" />
      });
    }

    // Epic'ler (Epic tipindeki issue'lar)
    const epics = issues.filter(issue => issue.issueType === 'Epic');
    epics.forEach(epic => {
      // Epic'e bağlı issue'ları bul (basit implementasyon - gerçekte epic link'leri kullanılır)
      const epicIssues = issues.filter(issue => 
        issue.issueType !== 'Epic' && 
        issue.summary.toLowerCase().includes(epic.summary.toLowerCase().split(' ')[0])
      );
      
      if (epicIssues.length > 0) {
        groups.push({
          id: `epic-${epic.id}`,
          title: `Epic: ${epic.summary}`,
          issues: epicIssues,
          isExpanded: expandedGroups.has(`epic-${epic.id}`),
          color: 'bg-purple-50 border-purple-200',
          icon: <Target size={16} className="text-purple-600" />
        });
      }
    });

    // Backlog (diğer tüm issue'lar)
    const backlogIssues = issues.filter(issue => 
      issue.status === 'To Do' && 
      issue.issueType !== 'Epic' &&
      !activeSprintIssues.includes(issue) &&
      !groups.some(group => group.issues.includes(issue))
    );
    
    if (backlogIssues.length > 0) {
      groups.push({
        id: 'backlog',
        title: 'Product Backlog',
        issues: backlogIssues,
        isExpanded: expandedGroups.has('backlog'),
        color: 'bg-gray-50 border-gray-200',
        icon: <BookOpen size={16} className="text-gray-600" />
      });
    }

    // Tamamlanan Issue'lar
    const doneIssues = issues.filter(issue => issue.status === 'Done');
    if (doneIssues.length > 0) {
      groups.push({
        id: 'done',
        title: 'Tamamlanan',
        issues: doneIssues,
        isExpanded: expandedGroups.has('done'),
        color: 'bg-green-50 border-green-200',
        icon: <CheckSquare size={16} className="text-green-600" />
      });
    }

    return groups;
  }, [issues, expandedGroups]);

  const toggleGroup = (groupId: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupId)) {
      newExpanded.delete(groupId);
    } else {
      newExpanded.add(groupId);
    }
    setExpandedGroups(newExpanded);
  };

  const getIssueTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'story':
        return <BookOpen size={16} className="text-green-600" />;
      case 'bug':
        return <Bug size={16} className="text-red-600" />;
      case 'task':
        return <CheckSquare size={16} className="text-blue-600" />;
      case 'epic':
        return <Target size={16} className="text-purple-600" />;
      default:
        return <CheckSquare size={16} className="text-gray-600" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit'
    });
  };

  const IssueRow = ({ issue }: { issue: JiraIssue }) => (
    <div
      className="flex items-center gap-3 p-3 hover:bg-gray-50 border-b border-gray-100 cursor-pointer group"
      onClick={() => onIssueClick(issue)}
    >
      {/* Drag Handle */}
      <div className="opacity-0 group-hover:opacity-100 transition-opacity">
        <GripVertical size={16} className="text-gray-400" />
      </div>

      {/* Issue Type Icon */}
      <div className="flex-shrink-0">
        {getIssueTypeIcon(issue.issueType)}
      </div>

      {/* Issue Key */}
      <div className="flex-shrink-0 w-20">
        <span className="text-sm font-medium text-blue-600">
          {issue.key}
        </span>
      </div>

      {/* Issue Summary */}
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 truncate">
          {issue.summary}
        </p>
        {issue.description && (
          <p className="text-xs text-gray-500 truncate mt-1">
            {issue.description.substring(0, 80)}
            {issue.description.length > 80 && '...'}
          </p>
        )}
      </div>

      {/* Badges */}
      <div className="flex items-center gap-2">
        {getTypeBadge(issue.issueType)}
        {getStatusBadge(issue.status)}
        {getPriorityBadge(issue.priority)}
      </div>

      {/* Assignee */}
      <div className="flex items-center gap-1 w-24">
        <User size={14} className="text-gray-400" />
        <span className="text-xs text-gray-600 truncate">
          {issue.assignee || 'Atanmamış'}
        </span>
      </div>

      {/* Comments */}
      {issue.comments.length > 0 && (
        <div className="flex items-center gap-1">
          <MessageSquare size={14} className="text-gray-400" />
          <span className="text-xs text-gray-600">
            {issue.comments.length}
          </span>
        </div>
      )}

      {/* Updated Date */}
      <div className="flex items-center gap-1 w-16">
        <Calendar size={14} className="text-gray-400" />
        <span className="text-xs text-gray-600">
          {formatDate(issue.updated)}
        </span>
      </div>

      {/* Actions */}
      <div className="opacity-0 group-hover:opacity-100 transition-opacity">
        <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
          <MoreHorizontal size={14} />
        </Button>
      </div>
    </div>
  );

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Product Backlog</h3>
          <div className="flex items-center gap-2">
            {!isCreatingSprint ? (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsCreatingSprint(true)}
              >
                <Plus size={16} className="mr-2" />
                Yeni Sprint
              </Button>
            ) : (
              <div className="flex items-center gap-2">
                <Input
                  type="text"
                  placeholder="Sprint adı..."
                  value={newSprintName}
                  onChange={(e) => setNewSprintName(e.target.value)}
                  className="w-40"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      // TODO: Create sprint
                      setIsCreatingSprint(false);
                      setNewSprintName('');
                    } else if (e.key === 'Escape') {
                      setIsCreatingSprint(false);
                      setNewSprintName('');
                    }
                  }}
                  autoFocus
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setIsCreatingSprint(false);
                    setNewSprintName('');
                  }}
                >
                  İptal
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Backlog Groups */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {backlogGroups.map(group => (
            <div key={group.id} className={`border rounded-lg ${group.color}`}>
              {/* Group Header */}
              <div
                className="flex items-center justify-between p-4 cursor-pointer hover:bg-black/5"
                onClick={() => toggleGroup(group.id)}
              >
                <div className="flex items-center gap-3">
                  {group.isExpanded ? (
                    <ChevronDown size={16} className="text-gray-500" />
                  ) : (
                    <ChevronRight size={16} className="text-gray-500" />
                  )}
                  {group.icon}
                  <h4 className="font-medium text-gray-900">
                    {group.title}
                  </h4>
                  <Badge variant="secondary" className="bg-white/80">
                    {group.issues.length}
                  </Badge>
                </div>
                
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <Plus size={14} />
                </Button>
              </div>

              {/* Group Content */}
              {group.isExpanded && (
                <div className="border-t border-gray-200">
                  {group.issues.length === 0 ? (
                    <div className="p-8 text-center text-gray-500">
                      <p className="text-sm">Bu grupta issue yok</p>
                    </div>
                  ) : (
                    group.issues.map(issue => (
                      <IssueRow key={issue.id} issue={issue} />
                    ))
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="border-t border-gray-200 p-4 bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>Toplam {issues.length} issue</span>
          <div className="flex items-center gap-4">
            {backlogGroups.map(group => (
              <div key={group.id} className="flex items-center gap-2">
                {group.icon}
                <span>{group.title}: {group.issues.length}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
