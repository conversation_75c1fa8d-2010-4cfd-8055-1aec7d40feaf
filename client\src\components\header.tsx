import { useState } from "react";
import { Link, useLocation } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import { usePermission } from "@/hooks/use-permission";
import { SYSTEM_PERMISSIONS } from "@/hooks/use-permission";
import { But<PERSON> } from "@/components/ui/button";
import IntegrationsModal from "@/components/integrations-modal";
import { 
  DropdownMenu, 
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { 
  Home, 
  FileText, 
  Settings, 
  User, 
  LogOut, 
  ChevronDown, 
  FolderKanban, 
  FileEdit, 
  HelpCircle,
  Link as LinkIcon
} from "lucide-react";

const Header = () => {
  const { user, logoutMutation } = useAuth();
  const { isAdmin, hasPermission } = usePermission();
  const [, setLocation] = useLocation();
  const [integrationsModalOpen, setIntegrationsModalOpen] = useState(false);
  const [selectedIntegrationType, setSelectedIntegrationType] = useState<string>("jira");

  const getUserInitials = (): string => {
    if (!user) return "?";
    
    if (user.name) {
      const nameParts = user.name.split(" ");
      if (nameParts.length > 1) {
        return `${nameParts[0][0]}${nameParts[nameParts.length - 1][0]}`.toUpperCase();
      }
      return nameParts[0].substring(0, 2).toUpperCase();
    }
    
    return user.username.substring(0, 2).toUpperCase();
  };

  const handleLogout = () => {
    logoutMutation.mutate();
    setLocation("/auth");
  };

  const handleIntegrationClick = (integrationType: string) => {
    setSelectedIntegrationType(integrationType);
    setIntegrationsModalOpen(true);
  };

  return (
    <header className="bg-white border-b border-neutral-200 sticky top-0 z-50">
      <div className="container mx-auto flex items-center justify-between px-4 py-2">
        <div className="flex items-center">
          <Link href="/">
            <div className="flex items-center cursor-pointer">
              <svg className="h-8 w-8 text-primary" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
              </svg>
              <h1 className="ml-2 text-xl font-semibold text-neutral-900 hidden md:block">Netaş - Agentic Yazılım Test Asistanı</h1>
              <h1 className="ml-2 text-xl font-semibold text-neutral-900 md:hidden">KATSA</h1>
            </div>
          </Link>
        </div>
        
        <div className="flex items-center space-x-1 md:space-x-2">
          <Link href="/">
            <Button variant="ghost" size="sm" className="hidden md:flex items-center gap-1">
              <Home className="h-4 w-4" />
              <span>Ana Sayfa</span>
            </Button>
          </Link>
          
          <Link href="/projects">
            <Button variant="ghost" size="sm" className="hidden md:flex items-center gap-1">
              <FolderKanban className="h-4 w-4" />
              <span>Projeler</span>
            </Button>
          </Link>
          
          {/* Bağlantılar menüsü
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="hidden md:flex items-center gap-1">
                <LinkIcon className="h-4 w-4" />
                <span>Bağlantılar</span>
                <ChevronDown className="h-3 w-3 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="center" className="w-64">
              <div className="px-2 py-1.5 text-xs font-medium text-neutral-500 uppercase tracking-wider">
                Entegrasyon Kaynakları
              </div>
              <DropdownMenuSeparator />
              
              <DropdownMenuItem 
                className="cursor-pointer"
                onClick={() => handleIntegrationClick("jira")}
              >
                <div className="flex items-center gap-3 w-full">
                  <div className="h-8 w-8 bg-blue-100 rounded flex items-center justify-center">
                    <i className="fab fa-jira text-blue-600 text-sm"></i>
                  </div>
                  <div>
                    <div className="font-medium text-sm">Jira</div>
                    <div className="text-xs text-neutral-500">Issue ve task yönetimi</div>
                  </div>
                </div>
              </DropdownMenuItem>
              
              <DropdownMenuItem 
                className="cursor-pointer"
                onClick={() => handleIntegrationClick("alm")}
              >
                <div className="flex items-center gap-3 w-full">
                  <div className="h-8 w-8 bg-orange-100 rounded flex items-center justify-center">
                    <i className="fas fa-cubes text-orange-600 text-sm"></i>
                  </div>
                  <div>
                    <div className="font-medium text-sm">ALM</div>
                    <div className="text-xs text-neutral-500">HP ALM / Quality Center</div>
                  </div>
                </div>
              </DropdownMenuItem>
              
              <DropdownMenuItem 
                className="cursor-pointer"
                onClick={() => handleIntegrationClick("testrail")}
              >
                <div className="flex items-center gap-3 w-full">
                  <div className="h-8 w-8 bg-green-100 rounded flex items-center justify-center">
                    <i className="fas fa-vial text-green-600 text-sm"></i>
                  </div>
                  <div>
                    <div className="font-medium text-sm">TestRail</div>
                    <div className="text-xs text-neutral-500">Test case yönetimi</div>
                  </div>
                </div>
              </DropdownMenuItem>
              
              <DropdownMenuItem 
                className="cursor-pointer"
                onClick={() => handleIntegrationClick("confluence")}
              >
                <div className="flex items-center gap-3 w-full">
                  <div className="h-8 w-8 bg-blue-100 rounded flex items-center justify-center">
                    <i className="fab fa-confluence text-blue-600 text-sm"></i>
                  </div>
                  <div>
                    <div className="font-medium text-sm">Confluence</div>
                    <div className="text-xs text-neutral-500">Dokümantasyon</div>
                  </div>
                </div>
              </DropdownMenuItem>
              
              <DropdownMenuItem 
                className="cursor-pointer"
                onClick={() => handleIntegrationClick("visium")}
              >
                <div className="flex items-center gap-3 w-full">
                  <div className="h-8 w-8 bg-purple-100 rounded flex items-center justify-center">
                    <i className="fas fa-project-diagram text-purple-600 text-sm"></i>
                  </div>
                  <div>
                    <div className="font-medium text-sm">Visium Manage</div>
                    <div className="text-xs text-neutral-500">Test yönetimi</div>
                  </div>
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

           */}

          {isAdmin || hasPermission(SYSTEM_PERMISSIONS.SETTINGS_VIEW) ? (
            <Link href="/settings">
              <Button variant="ghost" size="sm" className="hidden md:flex items-center gap-1">
                <Settings className="h-4 w-4" />
                <span>Ayarlar</span>
              </Button>
            </Link>
          ) : null}
          
          {/* Daha çok menü öğesi */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="md:hidden">
                <ChevronDown className="h-4 w-4" />
                <span className="sr-only">Menü</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link href="/">
                  <div className="flex items-center gap-2 w-full">
                    <Home className="h-4 w-4" />
                    <span>Ana Sayfa</span>
                  </div>
                </Link>
              </DropdownMenuItem>
              
              <DropdownMenuItem asChild>
                <Link href="/projects">
                  <div className="flex items-center gap-2 w-full">
                    <FolderKanban className="h-4 w-4" />
                    <span>Projeler</span>
                  </div>
                </Link>
              </DropdownMenuItem>
              
              <DropdownMenuItem asChild>
                <Link href="/prompt-editor">
                  <div className="flex items-center gap-2 w-full">
                    <FileEdit className="h-4 w-4" />
                    <span>Prompt Editörü</span>
                  </div>
                </Link>
              </DropdownMenuItem>
              
              {isAdmin || hasPermission(SYSTEM_PERMISSIONS.SETTINGS_VIEW) ? (
                <DropdownMenuItem asChild>
                  <Link href="/settings">
                    <div className="flex items-center gap-2 w-full">
                      <Settings className="h-4 w-4" />
                      <span>Ayarlar</span>
                    </div>
                  </Link>
                </DropdownMenuItem>
              ) : null}
            </DropdownMenuContent>
          </DropdownMenu>
          
          {/* Kullanıcı menüsü */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="flex items-center gap-2">
                <div className="h-8 w-8 rounded-full bg-primary text-white flex items-center justify-center">
                  <span className="text-sm font-medium">{getUserInitials()}</span>
                </div>
                <span className="hidden md:inline-block">{user?.name || user?.username}</span>
                <ChevronDown className="h-4 w-4 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <div className="px-2 py-1.5 text-sm font-medium text-neutral-900">
                {user?.name || user?.username}
                <div className="text-xs font-normal text-neutral-500">{user?.email || ''}</div>
                <div className="text-xs font-normal text-neutral-500">
                  {user?.role === 'admin' ? 'Yönetici' : 'Kullanıcı'}
                </div>
              </div>
              <DropdownMenuSeparator />
              
              <DropdownMenuItem asChild>
                <Link href="/profile">
                  <div className="flex items-center gap-2 w-full">
                    <User className="h-4 w-4" />
                    <span>Profil</span>
                  </div>
                </Link>
              </DropdownMenuItem>
              
              <DropdownMenuItem asChild>
                <Link href="/settings">
                  <div className="flex items-center gap-2 w-full">
                    <Settings className="h-4 w-4" />
                    <span>Ayarlar</span>
                  </div>
                </Link>
              </DropdownMenuItem>
              
              <DropdownMenuItem asChild>
                <a href="#" className="flex items-center gap-2 w-full">
                  <HelpCircle className="h-4 w-4" />
                  <span>Yardım</span>
                </a>
              </DropdownMenuItem>
              
              <DropdownMenuSeparator />
              
              <DropdownMenuItem 
                onClick={handleLogout} 
                className="text-red-600 cursor-pointer"
              >
                <div className="flex items-center gap-2">
                  <LogOut className="h-4 w-4" />
                  <span>Çıkış Yap</span>
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      
      {/* Entegrasyon Modal */}
      <IntegrationsModal 
        isOpen={integrationsModalOpen}
        onClose={() => setIntegrationsModalOpen(false)}
        initialIntegrationType={selectedIntegrationType}
      />
    </header>
  );
};

export default Header;
