import React from 'react';
import { Scroll<PERSON><PERSON> } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye,
  Calendar,
  User,
  Flag,
  CheckCircle,
  Circle,
  Clock
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface JiraIssue {
  id: string;
  key: string;
  summary: string;
  description: string;
  issueType: string;
  status: string;
  priority: string;
  assignee?: string;
  reporter?: string;
  created: string;
  updated: string;
  comments: Array<{
    id: string;
    author: string;
    body: string;
    created: string;
  }>;
}

interface IssueListViewProps {
  issues: JiraIssue[];
  onIssueClick: (issue: JiraIssue) => void;
  onDeleteIssue: (issueKey: string) => void;
  getPriorityBadge: (priority: string) => React.ReactNode;
  getStatusBadge: (status: string) => React.ReactNode;
  getTypeBadge: (type: string) => React.ReactNode;
}

export default function IssueListView({
  issues,
  onIssueClick,
  onDeleteIssue,
  getPriorityBadge,
  getStatusBadge,
  getTypeBadge
}: IssueListViewProps) {
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'done':
        return <CheckCircle size={16} className="text-green-600" />;
      case 'in progress':
        return <Clock size={16} className="text-yellow-600" />;
      case 'blocked':
        return <Flag size={16} className="text-red-600" />;
      default:
        return <Circle size={16} className="text-gray-400" />;
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Table Header */}
      <div className="border-b border-gray-200 bg-gray-50">
        <div className="grid grid-cols-12 gap-4 p-4 text-sm font-medium text-gray-700">
          <div className="col-span-1">Tip</div>
          <div className="col-span-2">Anahtar</div>
          <div className="col-span-4">Özet</div>
          <div className="col-span-1">Durum</div>
          <div className="col-span-1">Öncelik</div>
          <div className="col-span-2">Atanan</div>
          <div className="col-span-1">İşlemler</div>
        </div>
      </div>

      {/* Table Body */}
      <ScrollArea className="flex-1">
        <div className="divide-y divide-gray-200">
          {issues.map((issue) => (
            <div
              key={issue.id}
              className="grid grid-cols-12 gap-4 p-4 hover:bg-gray-50 transition-colors cursor-pointer group"
              onClick={() => onIssueClick(issue)}
            >
              {/* Issue Type */}
              <div className="col-span-1 flex items-center">
                {getTypeBadge(issue.issueType)}
              </div>

              {/* Issue Key */}
              <div className="col-span-2 flex items-center">
                <div className="flex items-center gap-2">
                  {getStatusIcon(issue.status)}
                  <span className="font-medium text-blue-600 hover:text-blue-800">
                    {issue.key}
                  </span>
                </div>
              </div>

              {/* Summary */}
              <div className="col-span-4 flex items-center">
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {issue.summary}
                  </p>
                  {issue.description && (
                    <p className="text-xs text-gray-500 truncate mt-1">
                      {issue.description.substring(0, 100)}
                      {issue.description.length > 100 && '...'}
                    </p>
                  )}
                </div>
              </div>

              {/* Status */}
              <div className="col-span-1 flex items-center">
                {getStatusBadge(issue.status)}
              </div>

              {/* Priority */}
              <div className="col-span-1 flex items-center">
                {getPriorityBadge(issue.priority)}
              </div>

              {/* Assignee */}
              <div className="col-span-2 flex items-center">
                <div className="flex items-center gap-2">
                  <User size={16} className="text-gray-400" />
                  <span className="text-sm text-gray-600">
                    {issue.assignee || 'Atanmamış'}
                  </span>
                </div>
              </div>

              {/* Actions */}
              <div className="col-span-1 flex items-center justify-end">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <MoreHorizontal size={16} />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={(e) => {
                      e.stopPropagation();
                      onIssueClick(issue);
                    }}>
                      <Eye size={16} className="mr-2" />
                      Görüntüle
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={(e) => {
                      e.stopPropagation();
                      // TODO: Edit functionality
                    }}>
                      <Edit size={16} className="mr-2" />
                      Düzenle
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={(e) => {
                        e.stopPropagation();
                        if (confirm(`${issue.key} issue'sını silmek istediğinizden emin misiniz?`)) {
                          onDeleteIssue(issue.key);
                        }
                      }}
                      className="text-red-600 focus:text-red-600"
                    >
                      <Trash2 size={16} className="mr-2" />
                      Sil
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="border-t border-gray-200 p-4 bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>{issues.length} issue gösteriliyor</span>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Calendar size={16} />
              <span>Son güncelleme: {new Date().toLocaleDateString('tr-TR')}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
