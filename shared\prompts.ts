/**
 * OpenAI için sabit prompt metinleri
 * <PERSON><PERSON>, uygulama genelinde kullanılan AI prompt metinlerini içerir
 */

// Doküman analizi için prompt
export const DOCUMENT_ANALYSIS_PROMPT = `Lütfen aşağıdaki dokümanı inceleyerek içerisindeki yazılım gereksinimlerini, bileşenleri ve API uç noktaları ile ilgili tüm bilgileri çıkar:

    {{documentContent}}

    Analiz sonuçlarını aşağıdaki JSON formatında döndür:
    {
      "components": [
        {
          "name": "Komponent Adı",
          "description": "Komponentin detaylı açıklaması",
          "type": "UI Component|Service|Database|External System|Utility",
          "isNew": true|false
        }
      ],
      "requirements": [
        {
          "code": "Gereksinim Kodu",
          "description": "Gereksinimin açıklaması",
          "category": "Fonksiyonel|Performans|Güvenlik|Kullanılabilirlik|Uyumluluk"
        }
      ],
      "apiEndpoints": [
        {
          "url": "/api/resource/{id}",
          "method": "GET|POST|PUT|DELETE",
          "parameters": { "id": "string", "name": "string" },
          "requirementCode": "İlgili gereksinim kodu"
        }
      ],
      "observations": [
        {
          "text": "Gözlem metni",
          "type": "general|missing_info|suggestion|performance|integration|usability|functional",
          "importance": "low|medium|high",
          "recommendedAction": "Önerilen eylem"
        }
      ]
    }

    Talimatlar:
    - Komponentler için UI bileşenleri, servisler, veritabanları veya dış sistemler olabilir.
    - Her gereksinim için bir kod ata (REQ-001, REQ-002 gibi).
    - API uç noktalarını doküman içinden tespit et eğer bulunmuyorsa ekleme.
    - Yazılım geliştirme süreçlerinde kullanılacak olan analiz dokümanına göre eksikleri net bir şekilde belirle ona göre çıkar.
    - Dokümanı test gereksinimleri açısından analiz et, yeterli test kapsamı sağlamak için gereken gereksinimlere ve kritik noktalara dikkat et.
    - Dokümanda açıkça belirtilmemiş ancak test edilmesi gereken durumları da tespit et.
    - API uç noktalarını detaylı şekilde çıkararak test edilebilecek tüm parametreleri belirle.`;

// Test senaryoları oluşturma için prompt
export const TEST_SCENARIOS_GENERATION_PROMPT = `Lütfen aşağıdaki gereksinimleri ve doküman içeriğini kullanarak detaylı test senaryoları oluştur:

Doküman İçeriği:

{{documentContentSection}}

Test senaryolarını oluştururken aşağıdaki standartları tam olarak ve tutarlı şekilde uygula:

1. **DOKÜMAN DEĞERLERİNİ VE TERİMİNİ TAM OLARAK KULLAN:**
   - Dokümanda belirtilen tüm değerleri, örnek girdileri ve beklentileri aynen kullan.
   - Terminolojiyi tamamen dokümandan al, ek terim veya değer ekleme.
   - Dokümanda belirtilen tüm veri alanları, ekran alanları ve kontrolleri test adımlarında açıkça ve aynen belirt.

2. **ÖN KOŞULLARI VE BEKLENEN SONUÇLARI DETAYLI OLARAK BELIRT:**
   - Dokümanda belirtilmişse ön koşulları dahil et.
   - Her ön koşulun sağlanma adımlarını dokümana bağlı kalarak ayrıntılı açıkla.
   - Doğrulama adımlarını dokümandaki değerlerle ve yöntemlerle belirt (neyin, nasıl, hangi değerlerle doğrulanacağı).
   - Beklenen sonuçları sayısal değerler, metinler ve dokümanda belirtilen görsellerle detaylandır.

3. **TÜM SENARYO TÜRLERİNİ DAHIL ET:**
   - Tüm "Happy Path" senaryolarını açık ve net adımlarla belirt.
   - Negatif senaryoları ayrı olarak çıkar ve belirt.
   - Sınır durumlarını (min, max, boş girişler gibi) ayrı adımlar olarak belirt.
   - Rol ve yetki bazlı senaryoları mutlaka dahil et ve net şekilde ayır.
   - Exploratory Test yaklaşımına uygun senaryolar oluştur.
   - Edge case durumlarını açıkça ve ayrı test adımları olarak belirt.
   - Her senaryo açık, net, özet başlık içersin ve başlık benzersiz bir ID ile birlikte senaryonun genel amacını ifade etsin.
   - Dokümanda belirtilmemiş ancak mantıksal bütünlük ve standart test pratiğine uygun olan kontrolleri de ekleyebilirsin.

4. TEST ADIMLARINI AÇIK VE DÜZENLİ OLARAK YAZ:
   - Her test adımı dokümandan elde edilen detaylara göre, açık ve net olarak yazılmalı.
   - Her kontrol adımı için doğrulama kriterlerini açıkça belirt ve bu kriterleri dokümandan al veya mantıksal bütünlüğe uygun olarak yorumla.
`;

// Test senaryosu doğrulama için prompt
export const TEST_SCENARIO_VALIDATION_PROMPT = `
    Lütfen aşağıdaki test senaryosunun ilgili gereksinimi ne kadar iyi karşıladığını değerlendir:

    GEREKSİNİM:
    Kod: {{requirementCode}}
    Açıklama: {{requirementDescription}}
    Kategori: {{requirementCategory}}

    TEST SENARYOSU:
    Başlık: {{scenarioTitle}}
    Ön Koşullar: {{scenarioPreconditions}}
    Adımlar:
    {{scenarioSteps}}
    Beklenen Sonuçlar: {{scenarioExpectedResults}}
    Format: {{scenarioFormat}}

    Lütfen aşağıdaki formatta JSON yanıtı oluştur:
    {
      "score": 0-100 arası bir puan,
      "feedback": "Genel değerlendirme içeren bir metin",
      "improvements": [
        "İyileştirme önerisi 1",
        "İyileştirme önerisi 2",
        "..."
      ],
      "coverage": {
        "functional": 0-100 arası bir puan (işlevsel kapsam),
        "edge_cases": 0-100 arası bir puan (sınır durumları kapsama),
        "negative_tests": 0-100 arası bir puan (negatif test kapsama)
      }
    }

    Değerlendirme Kriterleri:
    - Adımlar gereksinimi test etmek için yeterince kapsamlı mı?
    - Beklenen sonuçlar net ve doğrulanabilir mi?
    - Test, gereksinimin farklı yönlerini kapsıyor mu?
    - Sınır değerleri ve hata durumları test ediliyor mu?
    - Test adımları açık ve anlaşılır mı? 
    `;

// Doküman kapsama doğrulama için prompt
export const DOCUMENT_COVERAGE_VALIDATION_PROMPT = `
    test Lütfen aşağıdaki test senaryolarının verilen gereksinimleri ne kadar iyi kapsadığını değerlendir:

    GEREKSİNİMLER:
    {{requirementsText}}

    TEST SENARYOLARI:
    {{scenariosText}}

    Lütfen aşağıdaki formatta JSON yanıtı oluştur:
    {
      "overallCoverage": 0-100 arası bir puan (genel kapsama derecesi),
      "feedbackSummary": "Genel değerlendirme özeti",
      "requirementCoverage": [
        {
          "requirementCode": "REQ-001",
          "coverageRate": 0-100 arası bir puan,
          "uncoveredAspects": ["Kapsanmayan yön 1", "Kapsanmayan yön 2"]
        },
        ...
      ],
      "recommendations": [
        "İyileştirme önerisi 1",
        "İyileştirme önerisi 2",
        ...
      ]
    }

    Değerlendirme Kriterleri:
    - Her gereksinim için yeterli sayıda test senaryosu var mı?
    - Her gereksinimin farklı yönleri test ediliyor mu?
    - Uygun negatif test senaryoları var mı?
    - Sınır değer testleri ve özel durum kontrolleri test ediliyor mu?
    - Kapsanmayan gereksinimler veya gereksinim yönleri nelerdir?
    `;

// AI Asistanı için prompt
export const AI_ASSISTANT_PROMPT = `
    Sen bir doküman analiz asistanısın. Teknik dokümanları analiz edip, gereksinimleri ve test senaryolarını belirleyebilirsin.

    {{memoryContext}}

    Aşağıdaki soruya Türkçe olarak cevap ver:

    {{userQuery}}
    `;

// Daha fazla prompt eklenebilir