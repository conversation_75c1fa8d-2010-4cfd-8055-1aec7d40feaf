import { createContext, ReactNode, useContext, useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/use-auth";
import { getQueryFn } from "@/lib/queryClient";

// Sistem izinleri sabitleri
export const SYSTEM_PERMISSIONS = {
  // Genel sistem izinleri
  SETTINGS_VIEW: "SETTINGS_VIEW",
  SETTINGS_EDIT: "SETTINGS_EDIT",
  USER_MANAGE: "USER_MANAGE",
  
  // Proje izinleri
  PROJECT_CREATE: "PROJECT_CREATE",
  PROJECT_EDIT: "PROJECT_EDIT",
  PROJECT_DELETE: "PROJECT_DELETE",
  PROJECT_VIEW_ALL: "PROJECT_VIEW_ALL",
  
  // Doküman izinleri
  DOCUMENT_CREATE: "DOCUMENT_CREATE",
  DOCUMENT_EDIT: "DOCUMENT_EDIT",
  DOCUMENT_DELETE: "document_delete", // <PERSON><PERSON><PERSON><PERSON> silme izni
  DOCUMENT_ANALYZE: "DOCUMENT_ANALYZE",
  
  // Test senaryosu izinleri
  TEST_SCENARIO_CREATE: "TEST_SCENARIO_CREATE",
  TEST_SCENARIO_EDIT: "TEST_SCENARIO_EDIT",
  TEST_SCENARIO_DELETE: "TEST_SCENARIO_DELETE",
  
  // AI izinleri
  AI_ACCESS: "AI_ACCESS",
  AI_PROMPT_EDIT: "AI_PROMPT_EDIT",
  PROMPT_MANAGEMENT: "prompt_management" // Prompt düzenleme izni
};

// Proje rolleri sabitleri
export const PROJECT_ROLES = {
  OWNER: "owner",    // Sahip: Tüm yetkiler
  EDITOR: "editor",  // Düzenleyici: Doküman ekleyebilir, düzenleyebilir, test senaryoları oluşturabilir
  VIEWER: "viewer"   // Görüntüleyici: Sadece okuma yetkisi
};

type PermissionContextType = {
  isAdmin: boolean;
  hasPermission: (permission: string) => boolean;
  hasProjectAccess: (projectId: number, minRole?: string) => boolean;
  projectRole: (projectId: number) => string | null;
  permissions: string[];
  userProjects: any[];
  loading: boolean;
};

const PermissionContext = createContext<PermissionContextType | null>(null);

export function PermissionProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  const [permissions, setPermissions] = useState<string[]>([]);
  const [userProjects, setUserProjects] = useState<any[]>([]);
  
  // İzinleri getir
  const { data: userPermissions, isLoading: permissionsLoading } = useQuery({
    queryKey: ["/api/permissions"],
    queryFn: getQueryFn({ on401: "returnNull" }),
    enabled: !!user,
  });
  
  // Kullanıcı projelerini getir
  const { data: userProjectsData, isLoading: projectsLoading } = useQuery({
    queryKey: ["/api/user-projects"],
    queryFn: getQueryFn({ on401: "returnNull" }),
    enabled: !!user,
  });
  
  // Kullanıcı admin mi?
  const isAdmin = user?.role === "admin";
  
  // İzinleri state'e aktar
  useEffect(() => {
    if (userPermissions) {
      // İzinleri string dizisine dönüştür
      const permissionList = Array.isArray(userPermissions) 
        ? userPermissions 
        : [];
      
      setPermissions(permissionList);
    }
  }, [userPermissions]);
  
  // Kullanıcı projelerini state'e aktar
  useEffect(() => {
    if (userProjectsData) {
      // Projeleri diziye dönüştür
      const projectsList = Array.isArray(userProjectsData) 
        ? userProjectsData 
        : [];
      
      setUserProjects(projectsList);
    }
  }, [userProjectsData]);
  
  // Kullanıcının belirli bir izne sahip olup olmadığını kontrol et
  const hasPermission = (permission: string): boolean => {
    // Admin tüm izinlere sahiptir
    if (isAdmin) return true;
    
    // İzin listesinde var mı kontrol et
    return permissions.includes(permission);
  };
  
  // Kullanıcının belirli bir projeye erişimi var mı kontrol et
  const hasProjectAccess = (projectId: number, minRole: string = PROJECT_ROLES.VIEWER): boolean => {
    // Admin tüm projelere erişebilir
    if (isAdmin) return true;
    
    // Tüm projeleri görüntüleme izni var mı kontrol et
    if (hasPermission(SYSTEM_PERMISSIONS.PROJECT_VIEW_ALL)) return true;
    
    // Kullanıcının projeyle ilişkisi var mı kontrol et
    const project = userProjects.find(p => p.projectId === projectId);
    if (!project) return false;
    
    // Minimum rol kontrolü
    switch(minRole) {
      case PROJECT_ROLES.OWNER:
        return project.role === PROJECT_ROLES.OWNER;
      
      case PROJECT_ROLES.EDITOR:
        return project.role === PROJECT_ROLES.OWNER || project.role === PROJECT_ROLES.EDITOR;
      
      case PROJECT_ROLES.VIEWER:
      default:
        return true; // Herhangi bir rol yeterli
    }
  };
  
  // Kullanıcının belirli bir projede hangi role sahip olduğunu döndür
  const projectRole = (projectId: number): string | null => {
    // Projeyi bul
    const project = userProjects.find(p => p.projectId === projectId);
    
    // Proje bulunamadıysa null döndür
    if (!project) return null;
    
    // Admin her zaman owner olarak kabul edilir
    if (isAdmin) return PROJECT_ROLES.OWNER;
    
    // Kullanıcının projedeki rolünü döndür
    return project.role;
  };
  
  return (
    <PermissionContext.Provider
      value={{
        isAdmin,
        hasPermission,
        hasProjectAccess,
        projectRole,
        permissions,
        userProjects,
        loading: permissionsLoading || projectsLoading
      }}
    >
      {children}
    </PermissionContext.Provider>
  );
}

export function usePermission() {
  const context = useContext(PermissionContext);
  if (!context) {
    throw new Error("usePermission must be used within a PermissionProvider");
  }
  return context;
}