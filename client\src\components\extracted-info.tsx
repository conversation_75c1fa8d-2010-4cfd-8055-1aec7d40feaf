import { type Component, type Requirement, type ApiEndpoint, type AiAnalysis } from "@shared/schema";
import { format } from "date-fns";
import { useState, useEffect } from "react";
import { Loader2 } from "lucide-react";

interface ExtractedInfoProps {
  components: Component[];
  requirements: Requirement[];
  apiEndpoints: ApiEndpoint[];
  aiAnalysis?: AiAnalysis | null;
}

const ExtractedInfo = ({ components, requirements, apiEndpoints, aiAnalysis }: ExtractedInfoProps) => {
  const [showAllRequirements, setShowAllRequirements] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  // Components, gereksinimler veya API endpoint'ler değiştiğinde yükleme göstergesini ayarla
  useEffect(() => {
    // Veri değiştiğinde yükleme durumunu kontrol et
    const hasData = components.length > 0 || requirements.length > 0 || apiEndpoints.length > 0;
    
    // Yeni içerik geldiğinde minimum gösterme süresi için yüklemeyi devam ettir
    setIsLoading(true);
    
    // Her değişiklikte timer'ı sıfırla
    const timer = setTimeout(() => {
      if (hasData) {
        console.log("Çıkarılan bilgiler min süre sonunda yüklendi");
      } else {
        console.log("Çıkarılan bilgi bulunamadı, min süre sonunda yükleme duruyor");
      }
      setIsLoading(false);
    }, 1500); // Minimum 1.5 saniye göster
    
    return () => clearTimeout(timer);
  }, [components, requirements, apiEndpoints]);
  
  return (
    <div className="w-1/2 flex flex-col h-full overflow-hidden">
      <div className="bg-neutral-100 px-4 py-2 border-b border-neutral-200 flex items-center justify-between sticky top-0 z-10 flex-shrink-0">
        <h2 className="font-medium">Çıkarılan Bilgiler</h2>
        <div>
          <button className="text-neutral-500 hover:text-neutral-700 p-1" title="Yenile">
            <i className="fas fa-redo-alt"></i>
          </button>
          <button className="text-neutral-500 hover:text-neutral-700 p-1 ml-1" title="Filtrele">
            <i className="fas fa-filter"></i>
          </button>
        </div>
      </div>
      
      <div className="flex-1 overflow-y-auto scroll-thin"
      style={{ height: "calc(100% - 40px)" }}>
      
        {/* Yükleme göstergesi */}
        {isLoading && (
          <div className="p-4">
            <div className="flex items-center justify-center p-8 border border-primary/10 rounded-lg bg-primary-50/50 shadow-sm">
              <Loader2 className="h-6 w-6 animate-spin text-primary mr-3" />
              <p className="text-primary-700 font-medium">Çıkarılan bilgiler yükleniyor...</p>
            </div>
          </div>
        )}
        {/* System Components */}
        <div className="p-4 border-b border-neutral-200">
          <h3 className="font-medium text-neutral-800 mb-2 flex items-center">
            <i className="fas fa-puzzle-piece mr-2 text-primary"></i>
            Sistem Bileşenleri
            <span className="ml-2 text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">
              {components.length} Tespit Edildi
            </span>
          </h3>
          
          <div className="space-y-2 ml-6">
            {components.length > 0 ? (
              components.map((component) => (
                <div key={component.id} className="bg-white border border-neutral-200 rounded p-3 hover:shadow-sm">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">{component.name}</h4>
                    <span className={`text-xs ${component.isNew ? 'bg-primary/10 text-primary' : 'bg-accent/10 text-accent'} px-2 py-0.5 rounded-full`}>
                      {component.isNew ? 'YENİ' : 'Temel'}
                    </span>
                  </div>
                  <p className="text-sm text-neutral-600 mt-1">{component.description}</p>
                </div>
              ))
            ) : (
              <div className="text-neutral-500 text-sm">Henüz bileşen tespit edilmedi</div>
            )}
          </div>
        </div>

        {/* Requirements */}
        <div className="p-4 border-b border-neutral-200">
          <h3 className="font-medium text-neutral-800 mb-2 flex items-center">
            <i className="fas fa-clipboard-list mr-2 text-primary"></i>
            Tespit Edilen Gereksinimler
            <span className="ml-2 text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">
              {requirements.length} Tespit Edildi
            </span>
          </h3>
          
          <div className="space-y-2">
            {requirements.length > 0 ? (
              <>
                {requirements.slice(0, showAllRequirements ? requirements.length : 3).map((requirement) => (
                  <div key={requirement.id} className="flex items-start p-3 bg-white border border-neutral-200 rounded hover:shadow-sm">
                    <div className="mt-0.5">
                      <i className="fas fa-check-circle text-accent"></i>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm">
                        <span className="font-medium">{requirement.code}:</span> {requirement.description}
                      </p>
                      <p className="text-xs text-neutral-500 mt-1">Kategori: {requirement.category || 'Genel'}</p>
                    </div>
                  </div>
                ))}
                
                {requirements.length > 3 && !showAllRequirements && (
                  <button 
                    className="w-full py-2 text-sm text-primary hover:bg-primary/5 rounded border border-dashed border-primary/30"
                    onClick={() => setShowAllRequirements(true)}
                  >
                    <i className="fas fa-plus mr-1"></i> Daha Fazla Göster ({requirements.length - 3})
                  </button>
                )}
                
                {showAllRequirements && requirements.length > 3 && (
                  <button 
                    className="w-full py-2 text-sm text-primary hover:bg-primary/5 rounded border border-dashed border-primary/30"
                    onClick={() => setShowAllRequirements(false)}
                  >
                    <i className="fas fa-minus mr-1"></i> Daha Az Göster
                  </button>
                )}
              </>
            ) : (
              <div className="text-neutral-500 text-sm">Henüz gereksinim tespit edilmedi</div>
            )}
          </div>
        </div>

        {/* API Endpoints */}
        <div className="p-4 border-b border-neutral-200">
          <h3 className="font-medium text-neutral-800 mb-2 flex items-center">
            <i className="fas fa-code mr-2 text-primary"></i>
            API Entegrasyon Noktaları
            <span className="ml-2 text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">
              {apiEndpoints.length} Tespit Edildi
            </span>
          </h3>
          
          {apiEndpoints.length > 0 ? (
            apiEndpoints.map((endpoint) => (
              <div key={endpoint.id} className="bg-white border border-neutral-200 rounded p-3 hover:shadow-sm">
                <div className="flex items-center">
                  <span className="px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                    {endpoint.method}
                  </span>
                  <span className="ml-2 font-mono text-sm truncate">
                    {endpoint.url}
                  </span>
                </div>
                {endpoint.parameters && typeof endpoint.parameters === 'object' && (
                  <div className="mt-2 flex flex-wrap gap-2">
                    {Object.entries(endpoint.parameters as Record<string, any>).map(([key, value]) => (
                      <span key={key} className="px-2 py-0.5 rounded-full text-xs bg-neutral-100 text-neutral-700">
                        {key}: {typeof value === 'object' ? JSON.stringify(value) : value}
                      </span>
                    ))}
                  </div>
                )}
                {endpoint.requirementCode && (
                  <div className="mt-2 text-xs text-neutral-600">
                    <p>İlgili Gereksinim: <span className="text-primary">{endpoint.requirementCode}</span></p>
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="text-neutral-500 text-sm">Henüz API entegrasyon noktası tespit edilmedi</div>
          )}
        </div>

        {/* AI Insights */}
        <div className="p-4 border-b border-neutral-200">
          <h3 className="font-medium text-neutral-800 mb-2 flex items-center">
            <i className="fas fa-robot mr-2 text-primary"></i>
            AI Değerlendirmesi ve Asistan
            <span className="ml-2 text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">
              GPT-4o
            </span>
          </h3>
          
          {aiAnalysis ? (
            <div className="bg-white border border-neutral-200 rounded shadow-sm">
              <div className="p-3 border-b border-neutral-200 bg-neutral-50">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-sm flex items-center">
                    <i className="fas fa-brain text-purple-600 mr-2"></i>
                    Doküman Analiz Özeti
                  </h4>
                  <span className="text-xs text-neutral-500">
                    {aiAnalysis.analyzedAt ? format(new Date(aiAnalysis.analyzedAt), "dd.MM.yyyy HH:mm") : 'yakın zamanda'}
                  </span>
                </div>
              </div>
              
              <div className="p-3">
                <p className="text-sm text-neutral-700 bg-neutral-50 p-3 rounded-md border border-neutral-200">
                  Bu dokümanda {requirements.length > 0 ? (
                    <>
                      <span className="font-medium">{requirements.length} gereksinim</span> ve 
                      <span className="font-medium"> {components.length} bileşen</span> tespit edilmiştir.
                      {requirements.filter(r => r.category === 'functional').length > 0 && (
                        <> Bunların <span className="font-medium">{requirements.filter(r => r.category === 'functional').length}</span> tanesi işlevsel gereksinimlerdir.</>
                      )}
                    </>
                  ) : (
                    'henüz bileşen ve gereksinim analizi tamamlanmamıştır.'
                  )}
                  {apiEndpoints.length > 0 && (
                    <> Ayrıca <span className="font-medium">{apiEndpoints.length} API entegrasyon noktası</span> belirlenmiştir.</>
                  )}
                </p>
                
                {aiAnalysis.observations && Array.isArray(aiAnalysis.observations) && aiAnalysis.observations.length > 0 && (
                  <div className="mt-4">
                    <h5 className="text-sm font-medium text-neutral-700 mb-2">AI Gözlemleri ve Tavsiyeleri:</h5>
                    <div className="space-y-2">
                      {aiAnalysis.observations.map((observation: any, index: number) => {
                        const typeIcon = observation.type === 'performance' 
                          ? 'fas fa-tachometer-alt text-green-600' 
                          : observation.type === 'integration' 
                          ? 'fas fa-plug text-blue-600'
                          : observation.type === 'usability'
                          ? 'fas fa-user text-purple-600'
                          : observation.type === 'functional'
                          ? 'fas fa-cogs text-orange-600'
                          : observation.type === 'missing_info'
                          ? 'fas fa-exclamation-triangle text-amber-600'
                          : observation.type === 'suggestion'
                          ? 'fas fa-lightbulb text-yellow-600'
                          : 'fas fa-info-circle text-blue-600';
                        
                        const typeName = observation.type === 'performance' 
                          ? 'Performans' 
                          : observation.type === 'integration' 
                          ? 'Entegrasyon'
                          : observation.type === 'usability'
                          ? 'Kullanılabilirlik'
                          : observation.type === 'functional'
                          ? 'İşlevsellik'
                          : observation.type === 'missing_info'
                          ? 'Eksik Bilgi'
                          : observation.type === 'suggestion'
                          ? 'Öneri'
                          : 'Genel';
                          
                        return (
                          <div key={index} className="flex bg-white p-3 rounded-md border border-neutral-200 hover:shadow-sm">
                            <i className={`${typeIcon} mt-1`}></i>
                            <div className="ml-3">
                              <div className="text-xs font-medium text-neutral-600 mb-1">{typeName}</div>
                              <p className="text-sm text-neutral-700">{typeof observation.text === 'object' ? JSON.stringify(observation.text) : observation.text}</p>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
                
                <div className="mt-4 flex gap-2 justify-end">
                  <button className="text-xs px-3 py-1.5 bg-neutral-100 text-neutral-700 hover:bg-neutral-200 rounded-md flex items-center">
                    <i className="fas fa-sync-alt mr-1.5"></i>
                    Yeniden Analiz Et
                  </button>
                  <button className="text-xs px-3 py-1.5 bg-primary/10 text-primary hover:bg-primary/20 rounded-md flex items-center">
                    <i className="fas fa-file-export mr-1.5"></i>
                    Rapor Oluştur
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white border border-neutral-200 rounded p-4 text-center">
              <i className="fas fa-robot text-3xl text-neutral-300 mb-2"></i>
              <p className="text-sm text-neutral-700">
                Bu doküman için henüz AI değerlendirmesi yapılmamış.
              </p>
              <button className="mt-3 px-3 py-1.5 bg-primary text-white text-sm rounded-md hover:bg-primary/90">
                <i className="fas fa-magic mr-1.5"></i>
                AI Analizi Başlat
              </button>
            </div>
          )}
        </div>
        

      </div>
    </div>
  );
};

export default ExtractedInfo;
