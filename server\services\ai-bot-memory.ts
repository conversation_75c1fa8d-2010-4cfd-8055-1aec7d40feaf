import { storage } from '../storage';
import type { AiBotMemory, InsertAiBotMemory } from '@shared/schema';

/**
 * AI Bot Memory Service - Proje bazlı akıllı hafıza sistemi
 * 
 * Bu servis bot'un proje hakkında öğrendiği bilgileri saklar ve yönetir:
 * - <PERSON><PERSON> bilgiler<PERSON> (tech stack, team info, business rules)
 * - Issue pattern'leri (sık kullanılan terimler, yapılar)
 * - <PERSON><PERSON><PERSON> detaylar (API'ler, veritabanı, mimariler)
 * - Takım bilgileri (roller, sorumluluklar)
 */

export interface MemoryData {
  // Proje genel bilgileri
  project_info?: {
    name: string;
    description: string;
    domain: string; // e-commerce, fintech, healthcare, etc.
    size: 'small' | 'medium' | 'large';
    stage: 'planning' | 'development' | 'testing' | 'production';
  };

  // Teknik stack bilgileri
  tech_stack?: {
    frontend: string[]; // React, Vue, Angular, etc.
    backend: string[]; // Node.js, Java, Python, etc.
    database: string[]; // PostgreSQL, MongoDB, etc.
    cloud: string[]; // AWS, Azure, GCP, etc.
    tools: string[]; // Docker, Kubernetes, etc.
  };

  // Issue pattern'leri
  issue_pattern?: {
    common_types: string[]; // Bug, Feature, Task, etc.
    common_priorities: string[]; // High, Medium, Low
    common_components: string[]; // Login, Payment, Dashboard, etc.
    naming_conventions: string[]; // Prefix patterns, etc.
  };

  // Takım bilgileri
  team_info?: {
    size: number;
    roles: string[]; // Developer, Tester, PM, etc.
    working_hours: string; // 9-17, 24/7, etc.
    timezone: string;
    communication_style: 'formal' | 'casual' | 'technical';
  };

  // İş kuralları
  business_rules?: {
    compliance: string[]; // GDPR, SOX, HIPAA, etc.
    security_level: 'low' | 'medium' | 'high' | 'critical';
    testing_requirements: string[]; // Unit, Integration, E2E, etc.
    deployment_process: string; // CI/CD, Manual, etc.
  };
}

export class AiBotMemoryService {

  /**
   * Proje hafızasını getir
   */
  async getProjectMemory(projectId: number): Promise<AiBotMemory[]> {
    return await storage.getAiBotMemoryByProject(projectId);
  }

  /**
   * Belirli tip hafızayı getir
   */
  async getMemoryByType(projectId: number, memoryType: string): Promise<AiBotMemory[]> {
    return await storage.getAiBotMemoryByType(projectId, memoryType);
  }

  /**
   * Belirli anahtar hafızayı getir
   */
  async getMemoryByKey(projectId: number, memoryType: string, memoryKey: string): Promise<AiBotMemory | undefined> {
    return await storage.getAiBotMemoryByKey(projectId, memoryType, memoryKey);
  }

  /**
   * Yeni hafıza oluştur veya mevcut olanı güncelle
   */
  async upsertMemory(
    projectId: number,
    memoryType: string,
    memoryKey: string,
    memoryValue: MemoryData,
    confidenceScore: number = 5
  ): Promise<AiBotMemory> {
    // Mevcut hafızayı kontrol et
    const existingMemory = await this.getMemoryByKey(projectId, memoryType, memoryKey);

    if (existingMemory) {
      // Mevcut hafızayı güncelle
      const updatedMemory = await storage.updateAiBotMemory(existingMemory.id, {
        memoryValue: memoryValue as any,
        confidenceScore: Math.max(existingMemory.confidenceScore, confidenceScore),
        usageCount: existingMemory.usageCount + 1
      });
      return updatedMemory!;
    } else {
      // Yeni hafıza oluştur
      return await storage.createAiBotMemory({
        projectId,
        memoryType,
        memoryKey,
        memoryValue: memoryValue as any,
        confidenceScore,
        usageCount: 1
      });
    }
  }

  /**
   * Issue'dan proje bilgilerini çıkar ve hafızaya kaydet
   */
  async learnFromIssue(projectId: number, issueData: any, attachmentContent?: string): Promise<void> {
    try {
      console.log(`🧠 AI Bot: Proje ${projectId} için issue'dan öğrenme başlatılıyor...`);

      // Issue type pattern'lerini öğren
      await this.learnIssuePatterns(projectId, issueData);

      // Teknik stack bilgilerini çıkar
      await this.learnTechStack(projectId, issueData, attachmentContent);

      // İş kurallarını öğren
      await this.learnBusinessRules(projectId, issueData, attachmentContent);

      console.log(`🧠 AI Bot: Proje ${projectId} için öğrenme tamamlandı`);
    } catch (error) {
      console.error(`🧠 AI Bot: Proje ${projectId} öğrenme hatası:`, error);
    }
  }

  /**
   * Issue pattern'lerini öğren
   */
  private async learnIssuePatterns(projectId: number, issueData: any): Promise<void> {
    console.log(`🧠 Learning issue patterns for project ${projectId}:`, {
      type: issueData.type,
      priority: issueData.priority,
      summary: issueData.summary?.substring(0, 100)
    });

    const patterns = await this.getMemoryByKey(projectId, 'issue_pattern', 'common_patterns') ||
      { memoryValue: { common_types: [], common_priorities: [], common_components: [] } };

    const currentPatterns = patterns.memoryValue as any;
    let hasChanges = false;

    // Issue type'ı ekle
    if (issueData.type && !currentPatterns.common_types?.includes(issueData.type)) {
      currentPatterns.common_types = [...(currentPatterns.common_types || []), issueData.type];
      hasChanges = true;
      console.log(`🧠 Added new issue type: ${issueData.type}`);
    }

    // Priority'yi ekle
    if (issueData.priority && !currentPatterns.common_priorities?.includes(issueData.priority)) {
      currentPatterns.common_priorities = [...(currentPatterns.common_priorities || []), issueData.priority];
      hasChanges = true;
      console.log(`🧠 Added new priority: ${issueData.priority}`);
    }

    // Component'leri çıkar (issue başlığından)
    const componentKeywords = ['login', 'payment', 'dashboard', 'api', 'database', 'ui', 'frontend', 'backend'];
    const foundComponents = componentKeywords.filter(keyword =>
      issueData.summary?.toLowerCase().includes(keyword) ||
      issueData.description?.toLowerCase().includes(keyword)
    );

    console.log(`🧠 Found components in issue: ${foundComponents.join(', ') || 'none'}`);

    for (const component of foundComponents) {
      if (!currentPatterns.common_components?.includes(component)) {
        currentPatterns.common_components = [...(currentPatterns.common_components || []), component];
        hasChanges = true;
        console.log(`🧠 Added new component: ${component}`);
      }
    }

    if (hasChanges) {
      await this.upsertMemory(projectId, 'issue_pattern', 'common_patterns', currentPatterns, 7);
      console.log(`🧠 Issue patterns updated for project ${projectId}`);
    } else {
      console.log(`🧠 No new issue patterns found for project ${projectId}`);
    }
  }

  /**
   * Teknik stack bilgilerini öğren
   */
  private async learnTechStack(projectId: number, issueData: any, attachmentContent?: string): Promise<void> {
    const content = `${issueData.summary || ''} ${issueData.description || ''} ${attachmentContent || ''}`.toLowerCase();
    console.log(`🧠 Learning tech stack for project ${projectId}. Content length: ${content.length} chars`);

    const techKeywords = {
      frontend: ['react', 'vue', 'angular', 'javascript', 'typescript', 'html', 'css'],
      backend: ['node.js', 'java', 'python', 'c#', 'php', 'ruby', 'go'],
      database: ['postgresql', 'mysql', 'mongodb', 'redis', 'elasticsearch'],
      cloud: ['aws', 'azure', 'gcp', 'docker', 'kubernetes'],
      tools: ['git', 'jenkins', 'jira', 'confluence', 'slack']
    };

    const techStack = await this.getMemoryByKey(projectId, 'tech_stack', 'main_stack') ||
      { memoryValue: { frontend: [], backend: [], database: [], cloud: [], tools: [] } };

    const currentStack = techStack.memoryValue as any;
    let hasChanges = false;
    const foundTech: string[] = [];

    for (const [category, keywords] of Object.entries(techKeywords)) {
      for (const keyword of keywords) {
        if (content.includes(keyword)) {
          foundTech.push(`${category}:${keyword}`);
          if (!currentStack[category]?.includes(keyword)) {
            currentStack[category] = [...(currentStack[category] || []), keyword];
            hasChanges = true;
            console.log(`🧠 Added new tech: ${category} -> ${keyword}`);
          }
        }
      }
    }

    console.log(`🧠 Found technologies: ${foundTech.join(', ') || 'none'}`);

    if (hasChanges) {
      await this.upsertMemory(projectId, 'tech_stack', 'main_stack', currentStack, 8);
      console.log(`🧠 Tech stack updated for project ${projectId}`);
    } else {
      console.log(`🧠 No new tech stack items found for project ${projectId}`);
    }
  }

  /**
   * İş kurallarını öğren
   */
  private async learnBusinessRules(projectId: number, issueData: any, attachmentContent?: string): Promise<void> {
    const content = `${issueData.summary || ''} ${issueData.description || ''} ${attachmentContent || ''}`.toLowerCase();

    const businessKeywords = {
      compliance: ['gdpr', 'sox', 'hipaa', 'pci', 'iso'],
      security: ['security', 'authentication', 'authorization', 'encryption', 'ssl'],
      testing: ['unit test', 'integration test', 'e2e', 'automation', 'selenium']
    };

    const businessRules = await this.getMemoryByKey(projectId, 'business_rules', 'main_rules') ||
      { memoryValue: { compliance: [], security_level: 'medium', testing_requirements: [] } };

    const currentRules = businessRules.memoryValue as any;
    let hasChanges = false;

    // Compliance gereksinimlerini tespit et
    for (const compliance of businessKeywords.compliance) {
      if (content.includes(compliance) && !currentRules.compliance?.includes(compliance.toUpperCase())) {
        currentRules.compliance = [...(currentRules.compliance || []), compliance.toUpperCase()];
        hasChanges = true;
      }
    }

    // Güvenlik seviyesini tespit et
    const securityMentions = businessKeywords.security.filter(keyword => content.includes(keyword)).length;
    if (securityMentions > 2 && currentRules.security_level !== 'high') {
      currentRules.security_level = 'high';
      hasChanges = true;
    }

    // Test gereksinimlerini tespit et
    for (const testType of businessKeywords.testing) {
      if (content.includes(testType) && !currentRules.testing_requirements?.includes(testType)) {
        currentRules.testing_requirements = [...(currentRules.testing_requirements || []), testType];
        hasChanges = true;
      }
    }

    if (hasChanges) {
      await this.upsertMemory(projectId, 'business_rules', 'main_rules', currentRules, 6);
    }
  }

  /**
   * Hafıza kullanımını artır
   */
  async incrementUsage(memoryId: number): Promise<void> {
    await storage.incrementAiBotMemoryUsage(memoryId);
  }

  /**
   * Eski hafızaları temizle
   */
  async cleanupOldMemories(projectId: number, maxAgeInDays: number = 30): Promise<number> {
    const maxAge = maxAgeInDays * 24 * 60 * 60 * 1000; // Convert to milliseconds
    return await storage.cleanupOldAiBotMemory(projectId, maxAge);
  }

  /**
   * Proje hafızasını özet olarak getir (bot'un kullanması için)
   */
  async getProjectMemorySummary(projectId: number): Promise<string> {
    const memories = await this.getProjectMemory(projectId);

    if (memories.length === 0) {
      return "Bu proje hakkında henüz öğrenilmiş bilgi bulunmuyor.";
    }

    let summary = "🧠 PROJE HAFIZASI:\n\n";

    // Issue patterns
    const issuePatterns = memories.find(m => m.memoryType === 'issue_pattern');
    if (issuePatterns) {
      const patterns = issuePatterns.memoryValue as any;
      summary += "📋 Issue Patterns:\n";
      if (patterns.common_types?.length) summary += `- Yaygın tipler: ${patterns.common_types.join(', ')}\n`;
      if (patterns.common_components?.length) summary += `- Yaygın bileşenler: ${patterns.common_components.join(', ')}\n`;
      summary += "\n";
    }

    // Tech stack
    const techStack = memories.find(m => m.memoryType === 'tech_stack');
    if (techStack) {
      const stack = techStack.memoryValue as any;
      summary += "🔧 Teknik Stack:\n";
      if (stack.frontend?.length) summary += `- Frontend: ${stack.frontend.join(', ')}\n`;
      if (stack.backend?.length) summary += `- Backend: ${stack.backend.join(', ')}\n`;
      if (stack.database?.length) summary += `- Database: ${stack.database.join(', ')}\n`;
      summary += "\n";
    }

    // Business rules
    const businessRules = memories.find(m => m.memoryType === 'business_rules');
    if (businessRules) {
      const rules = businessRules.memoryValue as any;
      summary += "📜 İş Kuralları:\n";
      if (rules.compliance?.length) summary += `- Compliance: ${rules.compliance.join(', ')}\n`;
      if (rules.security_level) summary += `- Güvenlik seviyesi: ${rules.security_level}\n`;
      if (rules.testing_requirements?.length) summary += `- Test gereksinimleri: ${rules.testing_requirements.join(', ')}\n`;
      summary += "\n";
    }

    return summary;
  }
}

export const aiBotMemoryService = new AiBotMemoryService();
