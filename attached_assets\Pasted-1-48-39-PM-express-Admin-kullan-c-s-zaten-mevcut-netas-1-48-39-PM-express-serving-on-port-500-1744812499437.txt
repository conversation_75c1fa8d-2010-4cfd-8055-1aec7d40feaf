1:48:39 PM [express] <PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zaten mevcut: netas
1:48:39 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 6 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
2:07:36 PM [express] GET /api/ai-models 304 in 45ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
2:07:37 PM [express] GET /api/documents 304 in 671ms :: [{"id":3,"name":"demo.docx","type":"docx","c…
2:07:38 PM [express] GET /api/documents/7/components 304 in 94ms :: [{"id":25,"documentId":7,"name":…
2:07:38 PM [express] GET /api/documents/7 304 in 208ms :: {"id":7,"name":"AD_TLP-2023-000342_Vergi_O…
2:07:38 PM [express] GET /api/documents/7/requirements 304 in 140ms :: [{"id":52,"documentId":7,"cod…
2:07:38 PM [express] GET /api/documents/7/api-endpoints 304 in 99ms :: [{"id":13,"documentId":7,"url…
2:07:39 PM [express] GET /api/documents/7/ai-analysis 304 in 139ms :: {"id":6,"documentId":7,"observ…
2:07:39 PM [express] GET /api/documents/7/test-scenarios 304 in 155ms :: []
2:07:40 PM [express] GET /api/documents/6/requirements 304 in 97ms :: [{"id":47,"documentId":6,"code…
2:07:40 PM [express] GET /api/documents/6/components 304 in 71ms :: [{"id":22,"documentId":6,"name":…
2:07:40 PM [express] GET /api/documents/6/ai-analysis 304 in 69ms :: {"id":5,"documentId":6,"observa…
2:07:40 PM [express] GET /api/documents/6 304 in 206ms :: {"id":6,"name":"AD_TLP-2023-000342_Vergi_O…
2:07:40 PM [express] GET /api/documents/6/api-endpoints 304 in 121ms :: [{"id":10,"documentId":6,"ur…
2:07:40 PM [express] GET /api/documents/6/test-scenarios 304 in 121ms :: [{"id":47,"documentId":6,"t…
Kapsam analizi getirme hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NeonPreparedQuery.execute (/home/<USER>/workspace/node_modules/src/neon-serverless/session.ts:102:18)
    at async PostgresStorage.getCoverageValidationByDocumentId (/home/<USER>/workspace/server/storage-postgres.ts:269:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:454:34) {
  length: 119,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '159',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:41 PM [express] GET /api/documents/6/coverage-validation 500 in 134ms :: {"error":"Kapsam anali…
Kapsam analizi getirme hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NeonPreparedQuery.execute (/home/<USER>/workspace/node_modules/src/neon-serverless/session.ts:102:18)
    at async PostgresStorage.getCoverageValidationByDocumentId (/home/<USER>/workspace/server/storage-postgres.ts:269:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:454:34) {
  length: 119,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '159',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:41 PM [express] GET /api/documents/6/coverage-validation 500 in 137ms :: {"error":"Kapsam anali…
Kapsam analizi getirme hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NeonPreparedQuery.execute (/home/<USER>/workspace/node_modules/src/neon-serverless/session.ts:102:18)
    at async PostgresStorage.getCoverageValidationByDocumentId (/home/<USER>/workspace/server/storage-postgres.ts:269:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:454:34) {
  length: 119,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '159',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:42 PM [express] GET /api/documents/6/coverage-validation 500 in 75ms :: {"error":"Kapsam analiz…
Kapsam analizi getirme hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NeonPreparedQuery.execute (/home/<USER>/workspace/node_modules/src/neon-serverless/session.ts:102:18)
    at async PostgresStorage.getCoverageValidationByDocumentId (/home/<USER>/workspace/server/storage-postgres.ts:269:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:454:34) {
  length: 119,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '159',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:44 PM [express] GET /api/documents/6/coverage-validation 500 in 69ms :: {"error":"Kapsam analiz…
Doküman kapsama doğrulama hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.deleteCoverageValidation (/home/<USER>/workspace/server/storage-postgres.ts:284:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:485:7) {
  length: 118,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '13',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:44 PM [express] POST /api/documents/6/validate-coverage 500 in 174ms :: {"error":"Doküman kapsa…
Kapsam analizi getirme hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NeonPreparedQuery.execute (/home/<USER>/workspace/node_modules/src/neon-serverless/session.ts:102:18)
    at async PostgresStorage.getCoverageValidationByDocumentId (/home/<USER>/workspace/server/storage-postgres.ts:269:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:454:34) {
  length: 119,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '159',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:45 PM [express] GET /api/documents/6/coverage-validation 500 in 75ms :: {"error":"Kapsam analiz…
Doküman kapsama doğrulama hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.deleteCoverageValidation (/home/<USER>/workspace/server/storage-postgres.ts:284:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:485:7) {
  length: 118,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '13',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:45 PM [express] POST /api/documents/6/validate-coverage 500 in 274ms :: {"error":"Doküman kapsa…
Kapsam analizi getirme hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NeonPreparedQuery.execute (/home/<USER>/workspace/node_modules/src/neon-serverless/session.ts:102:18)
    at async PostgresStorage.getCoverageValidationByDocumentId (/home/<USER>/workspace/server/storage-postgres.ts:269:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:454:34) {
  length: 119,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '159',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:46 PM [express] GET /api/documents/6/coverage-validation 500 in 124ms :: {"error":"Kapsam anali…
Doküman kapsama doğrulama hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.deleteCoverageValidation (/home/<USER>/workspace/server/storage-postgres.ts:284:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:485:7) {
  length: 118,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '13',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:46 PM [express] POST /api/documents/6/validate-coverage 500 in 135ms :: {"error":"Doküman kapsa…
Kapsam analizi getirme hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NeonPreparedQuery.execute (/home/<USER>/workspace/node_modules/src/neon-serverless/session.ts:102:18)
    at async PostgresStorage.getCoverageValidationByDocumentId (/home/<USER>/workspace/server/storage-postgres.ts:269:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:454:34) {
  length: 119,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '159',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:47 PM [express] GET /api/documents/6/coverage-validation 500 in 70ms :: {"error":"Kapsam analiz…
Doküman kapsama doğrulama hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.deleteCoverageValidation (/home/<USER>/workspace/server/storage-postgres.ts:284:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:485:7) {
  length: 118,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '13',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:48 PM [express] POST /api/documents/6/validate-coverage 500 in 146ms :: {"error":"Doküman kapsa…
Doküman kapsama doğrulama hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.deleteCoverageValidation (/home/<USER>/workspace/server/storage-postgres.ts:284:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:485:7) {
  length: 118,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '13',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:50 PM [express] POST /api/documents/6/validate-coverage 500 in 243ms :: {"error":"Doküman kapsa…
Kapsam analizi getirme hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NeonPreparedQuery.execute (/home/<USER>/workspace/node_modules/src/neon-serverless/session.ts:102:18)
    at async PostgresStorage.getCoverageValidationByDocumentId (/home/<USER>/workspace/server/storage-postgres.ts:269:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:454:34) {
  length: 119,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '159',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:50 PM [express] GET /api/documents/6/coverage-validation 500 in 121ms :: {"error":"Kapsam anali…
Doküman kapsama doğrulama hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.deleteCoverageValidation (/home/<USER>/workspace/server/storage-postgres.ts:284:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:485:7) {
  length: 118,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '13',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:51 PM [express] POST /api/documents/6/validate-coverage 500 in 253ms :: {"error":"Doküman kapsa…
Kapsam analizi getirme hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NeonPreparedQuery.execute (/home/<USER>/workspace/node_modules/src/neon-serverless/session.ts:102:18)
    at async PostgresStorage.getCoverageValidationByDocumentId (/home/<USER>/workspace/server/storage-postgres.ts:269:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:454:34) {
  length: 119,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '159',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:52 PM [express] GET /api/documents/6/coverage-validation 500 in 125ms :: {"error":"Kapsam anali…
Doküman kapsama doğrulama hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.deleteCoverageValidation (/home/<USER>/workspace/server/storage-postgres.ts:284:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:485:7) {
  length: 118,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '13',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:52 PM [express] POST /api/documents/6/validate-coverage 500 in 185ms :: {"error":"Doküman kapsa…
Kapsam analizi getirme hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NeonPreparedQuery.execute (/home/<USER>/workspace/node_modules/src/neon-serverless/session.ts:102:18)
    at async PostgresStorage.getCoverageValidationByDocumentId (/home/<USER>/workspace/server/storage-postgres.ts:269:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:454:34) {
  length: 119,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '159',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:53 PM [express] GET /api/documents/6/coverage-validation 500 in 86ms :: {"error":"Kapsam analiz…
Kapsam analizi getirme hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NeonPreparedQuery.execute (/home/<USER>/workspace/node_modules/src/neon-serverless/session.ts:102:18)
    at async PostgresStorage.getCoverageValidationByDocumentId (/home/<USER>/workspace/server/storage-postgres.ts:269:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:454:34) {
  length: 119,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '159',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:54 PM [express] GET /api/documents/6/coverage-validation 500 in 85ms :: {"error":"Kapsam analiz…
Doküman kapsama doğrulama hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.deleteCoverageValidation (/home/<USER>/workspace/server/storage-postgres.ts:284:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:485:7) {
  length: 118,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '13',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:55 PM [express] POST /api/documents/6/validate-coverage 500 in 176ms :: {"error":"Doküman kapsa…
Kapsam analizi getirme hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NeonPreparedQuery.execute (/home/<USER>/workspace/node_modules/src/neon-serverless/session.ts:102:18)
    at async PostgresStorage.getCoverageValidationByDocumentId (/home/<USER>/workspace/server/storage-postgres.ts:269:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:454:34) {
  length: 119,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '159',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:57 PM [express] GET /api/documents/6/coverage-validation 500 in 89ms :: {"error":"Kapsam analiz…
Doküman kapsama doğrulama hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.deleteCoverageValidation (/home/<USER>/workspace/server/storage-postgres.ts:284:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:485:7) {
  length: 118,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '13',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:57 PM [express] POST /api/documents/6/validate-coverage 500 in 145ms :: {"error":"Doküman kapsa…
Doküman kapsama doğrulama hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.deleteCoverageValidation (/home/<USER>/workspace/server/storage-postgres.ts:284:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:485:7) {
  length: 118,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '13',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:07:58 PM [express] POST /api/documents/6/validate-coverage 500 in 244ms :: {"error":"Doküman kapsa…
Doküman kapsama doğrulama hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.deleteCoverageValidation (/home/<USER>/workspace/server/storage-postgres.ts:284:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:485:7) {
  length: 118,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '13',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:08:00 PM [express] POST /api/documents/6/validate-coverage 500 in 215ms :: {"error":"Doküman kapsa…
Kapsam analizi getirme hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NeonPreparedQuery.execute (/home/<USER>/workspace/node_modules/src/neon-serverless/session.ts:102:18)
    at async PostgresStorage.getCoverageValidationByDocumentId (/home/<USER>/workspace/server/storage-postgres.ts:269:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:454:34) {
  length: 119,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '159',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:08:01 PM [express] GET /api/documents/6/coverage-validation 500 in 103ms :: {"error":"Kapsam anali…
Kapsam analizi getirme hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NeonPreparedQuery.execute (/home/<USER>/workspace/node_modules/src/neon-serverless/session.ts:102:18)
    at async PostgresStorage.getCoverageValidationByDocumentId (/home/<USER>/workspace/server/storage-postgres.ts:269:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:454:34) {
  length: 119,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '159',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:08:02 PM [express] GET /api/documents/6/coverage-validation 500 in 91ms :: {"error":"Kapsam analiz…
Doküman kapsama doğrulama hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.deleteCoverageValidation (/home/<USER>/workspace/server/storage-postgres.ts:284:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:485:7) {
  length: 118,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '13',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:08:02 PM [express] POST /api/documents/6/validate-coverage 500 in 176ms :: {"error":"Doküman kapsa…
Kapsam analizi getirme hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NeonPreparedQuery.execute (/home/<USER>/workspace/node_modules/src/neon-serverless/session.ts:102:18)
    at async PostgresStorage.getCoverageValidationByDocumentId (/home/<USER>/workspace/server/storage-postgres.ts:269:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:454:34) {
  length: 119,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '159',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:08:03 PM [express] GET /api/documents/6/coverage-validation 500 in 73ms :: {"error":"Kapsam analiz…
Kapsam analizi getirme hatası: error: relation "coverage_validation" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NeonPreparedQuery.execute (/home/<USER>/workspace/node_modules/src/neon-serverless/session.ts:102:18)
    at async PostgresStorage.getCoverageValidationByDocumentId (/home/<USER>/workspace/server/storage-postgres.ts:269:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:454:34) {
  length: 119,
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '159',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable'
}
2:08:05 PM [express] GET /api/documents/6/coverage-validation 500 in 91ms :: {"error":"Kapsam analiz…
