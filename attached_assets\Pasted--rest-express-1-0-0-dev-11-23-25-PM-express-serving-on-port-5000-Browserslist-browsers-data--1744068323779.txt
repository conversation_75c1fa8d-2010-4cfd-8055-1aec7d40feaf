
> rest-express@1.0.0 dev
11:23:25 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 6 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
11:23:28 PM [express] GET /api/ai-models 304 in 3ms :: {"models":{"o1":{"displayName":"GPT-o1","deplo…
11:23:28 PM [express] GET /api/documents 200 in 6ms :: [{"id":26,"name":"demo.docx","type":"docx","co…
11:23:29 PM [express] GET /api/ai-models 200 in 1ms :: {"models":{"o1":{"displayName":"GPT-o1","deplo…
11:23:29 PM [express] GET /api/documents 304 in 2ms :: [{"id":26,"name":"demo.docx","type":"docx","co…
11:23:30 PM [express] GET /api/ai-models 304 in 1ms :: {"models":{"o1":{"displayName":"GPT-o1","deplo…
11:23:30 PM [express] GET /api/documents 304 in 2ms :: [{"id":26,"name":"demo.docx","type":"docx","co…
OpenAI ile doküman analizi hatası: NotFoundError: 404 The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
    at Function.generate (/home/<USER>/workspace/node_modules/openai/src/error.ts:84:14)
    at OpenAI.makeStatusError (/home/<USER>/workspace/node_modules/openai/src/core.ts:462:21)
    at OpenAI.makeRequest (/home/<USER>/workspace/node_modules/openai/src/core.ts:526:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Object.analyzeDocument (/home/<USER>/workspace/server/services/openai.ts:190:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:87:34) {
  status: 404,
  headers: {
    'apim-request-id': '86a4cf48-b961-4f04-9726-db4ea31ae45b',
    'content-length': '197',
    'content-type': 'application/json',
    date: 'Mon, 07 Apr 2025 23:24:11 GMT',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    'x-content-type-options': 'nosniff',
    'x-ms-region': 'East US'
  },
  request_id: undefined,
  error: {
    code: 'DeploymentNotFound',
    message: 'The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.'
  },
  code: 'DeploymentNotFound',
  param: undefined,
  type: undefined
}
Doküman analiz hatası: Error: Doküman analizi başarısız oldu: 404 The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
    at Object.analyzeDocument (/home/<USER>/workspace/server/services/openai.ts:200:11)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:87:34)
11:24:11 PM [express] POST /api/documents/upload 201 in 3225ms :: {"document":{"id":27,"name":"demo.d…
11:24:11 PM [express] GET /api/documents 200 in 2ms :: [{"id":27,"name":"demo.docx","type":"docx","co…
11:24:15 PM [express] GET /api/documents/27 200 in 1ms :: {"id":27,"name":"demo.docx","type":"docx","…
11:24:15 PM [express] GET /api/documents/27/components 200 in 0ms :: []
11:24:15 PM [express] GET /api/documents/27/ai-analysis 404 in 1ms :: {"error":"Bu doküman için AI an…
11:24:15 PM [express] GET /api/documents/27/requirements 200 in 0ms :: []
11:24:15 PM [express] GET /api/documents/27/api-endpoints 200 in 1ms :: []
11:24:19 PM [express] DELETE /api/documents/27 200 in 101ms :: {"success":true,"message":"Doküman baş…
11:24:20 PM [express] GET /api/documents 200 in 2ms :: [{"id":26,"name":"demo.docx","type":"docx","co…
11:24:21 PM [express] DELETE /api/documents/27 404 in 0ms :: {"error":"Doküman bulunamadı"}
11:24:23 PM [express] DELETE /api/documents/26 200 in 28ms :: {"success":true,"message":"Doküman başa…
11:24:23 PM [express] GET /api/documents 200 in 2ms :: [{"id":25,"name":"demo.docx","type":"docx","co…
11:24:25 PM [express] DELETE /api/documents/25 200 in 72ms :: {"success":true,"message":"Doküman başa…
11:24:25 PM [express] GET /api/documents 200 in 4ms :: [{"id":24,"name":"demo.docx","type":"docx","co…
11:24:26 PM [express] GET /api/documents 200 in 2ms :: [{"id":24,"name":"demo.docx","type":"docx","co…
11:24:26 PM [express] GET /api/ai-models 200 in 0ms :: {"models":{"o1":{"displayName":"GPT-o1","deplo…
11:24:27 PM [express] GET /api/documents/24 200 in 1ms :: {"id":24,"name":"demo.docx","type":"docx","…
11:24:27 PM [express] GET /api/documents/24/components 200 in 1ms :: []
11:24:27 PM [express] GET /api/documents/24/requirements 200 in 1ms :: []
11:24:27 PM [express] GET /api/documents/24/api-endpoints 200 in 1ms :: []
11:24:27 PM [express] GET /api/documents/24/ai-analysis 404 in 0ms :: {"error":"Bu doküman için AI an…
11:24:28 PM [express] DELETE /api/documents/24 200 in 47ms :: {"success":true,"message":"Doküman başa…
11:24:28 PM [express] GET /api/documents 200 in 2ms :: [{"id":23,"name":"demo.docx","type":"docx","co…
11:24:29 PM [express] DELETE /api/documents/24 404 in 1ms :: {"error":"Doküman bulunamadı"}
11:24:32 PM [express] DELETE /api/documents/22 200 in 846ms :: {"success":true,"message":"Doküman baş…
11:24:33 PM [express] GET /api/documents 200 in 2ms :: [{"id":23,"name":"demo.docx","type":"docx","co…
11:24:33 PM [express] DELETE /api/documents/21 200 in 26ms :: {"success":true,"message":"Doküman başa…
11:24:34 PM [express] GET /api/documents 200 in 2ms :: [{"id":23,"name":"demo.docx","type":"docx","co…
11:24:35 PM [express] DELETE /api/documents/20 200 in 22ms :: {"success":true,"message":"Doküman başa…
11:24:35 PM [express] GET /api/documents 200 in 1ms :: [{"id":23,"name":"demo.docx","type":"docx","co…
11:24:37 PM [express] DELETE /api/documents/23 200 in 60ms :: {"success":true,"message":"Doküman başa…
11:24:37 PM [express] GET /api/documents 200 in 0ms :: []
11:24:45 PM [express] POST /api/ai-models/set 200 in 0ms :: {"success":true,"message":"AI modeli 'o4'…
OpenAI ile doküman analizi hatası: NotFoundError: 404 The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
    at Function.generate (/home/<USER>/workspace/node_modules/openai/src/error.ts:84:14)
    at OpenAI.makeStatusError (/home/<USER>/workspace/node_modules/openai/src/core.ts:462:21)
    at OpenAI.makeRequest (/home/<USER>/workspace/node_modules/openai/src/core.ts:526:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Object.analyzeDocument (/home/<USER>/workspace/server/services/openai.ts:190:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:87:34) {
  status: 404,
  headers: {
    'apim-request-id': '3e80ab4c-c23e-4865-94bc-f1e2d60773de',
    'content-length': '197',
    'content-type': 'application/json',
    date: 'Mon, 07 Apr 2025 23:24:50 GMT',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    'x-content-type-options': 'nosniff',
    'x-ms-region': 'East US'
  },
  request_id: undefined,
  error: {
    code: 'DeploymentNotFound',
    message: 'The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.'
  },
  code: 'DeploymentNotFound',
  param: undefined,
  type: undefined
}
Doküman analiz hatası: Error: Doküman analizi başarısız oldu: 404 The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
    at Object.analyzeDocument (/home/<USER>/workspace/server/services/openai.ts:200:11)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:87:34)
11:24:51 PM [express] POST /api/documents/upload 201 in 1670ms :: {"document":{"id":28,"name":"demo.d…
11:24:51 PM [express] GET /api/documents 200 in 1ms :: [{"id":28,"name":"demo.docx","type":"docx","co…
11:24:54 PM [express] GET /api/documents/28 200 in 1ms :: {"id":28,"name":"demo.docx","type":"docx","…
11:24:54 PM [express] GET /api/documents/28/requirements 200 in 1ms :: []
11:24:54 PM [express] GET /api/documents/28/components 200 in 1ms :: []
11:24:54 PM [express] GET /api/documents/28/ai-analysis 404 in 0ms :: {"error":"Bu doküman için AI an…
11:24:54 PM [express] GET /api/documents/28/api-endpoints 200 in 1ms :: []