Görsel içerik analizi başlatılıyor. Doküman metni analiz ediliyor...
o1/o3 modelleri için Azure OpenAI API anahtarı: ******GrPml
Görsel içerik analizi için kullanılan model: o1
Görsel analizi için aranan görsel tipleri: şekil, tablo, diagram, resim
OpenAI ile görsel içerik analizi hatası: BadRequestError: 400 Unsupported parameter: 'temperature' is not supported with this model.
    at Function.generate (/home/<USER>/workspace/node_modules/openai/src/error.ts:72:14)
    at OpenAI.makeStatusError (/home/<USER>/workspace/node_modules/openai/src/core.ts:462:21)
    at OpenAI.makeRequest (/home/<USER>/workspace/node_modules/openai/src/core.ts:526:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async analyzeVisualContent (/home/<USER>/workspace/server/services/openai.ts:273:22)
    at async Object.processDocument (/home/<USER>/workspace/server/services/document-processor.ts:252:30)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:831:31) {
  status: 400,
  headers: {
    'apim-request-id': '5634781c-2a1b-47c0-94d5-258a0719529d',
    'azureml-model-session': 'v20250411-1-167922813',
    'content-length': '210',
    'content-type': 'application/json',
    date: 'Wed, 23 Apr 2025 05:01:28 GMT',
    'ms-azureml-model-error-reason': 'model_error',
    'ms-azureml-model-error-statuscode': '400',
    'ms-azureml-model-time': '22',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    'x-aml-cluster': 'hyena-northcentralus-02',
    'x-content-type-options': 'nosniff',
    'x-envoy-upstream-service-time': '23',
    'x-ms-client-request-id': '5634781c-2a1b-47c0-94d5-258a0719529d',
    'x-ms-deployment-name': 'netas-ai-o1',
    'x-ms-rai-invoked': 'true',
    'x-ms-region': 'East US',
    'x-ratelimit-limit-requests': '100',
    'x-ratelimit-limit-tokens': '600000',
    'x-ratelimit-remaining-requests': '99',
    'x-ratelimit-remaining-tokens': '595904',
    'x-request-id': '0fdbbb1b-4e1e-466f-87a5-7d93d752cead'
  },
  request_id: '0fdbbb1b-4e1e-466f-87a5-7d93d752cead',
  error: {
    message: "Unsupported parameter: 'temperature' is not supported with this model.",
    type: 'invalid_request_error',
    param: 'temperature',
    code: 'unsupported_parameter'
  },
  code: 'unsupported_parameter',
  param: 'temperature',
  type: 'invalid_request_error'
}
o1/o3 modelleri için Azure OpenAI API anahtarı: ******GrPml
