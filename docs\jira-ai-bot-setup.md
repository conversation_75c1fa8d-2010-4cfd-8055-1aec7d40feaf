# 🤖 Jira AI Bot Webhook Kurulumu

## 📋 Genel Bakış

Jira AI Bot, kullanıcılar<PERSON>n doğrudan Jira issue'larında comment yazarak AI analizleri tetiklemesini sağlar. Bu sistem webhook'lar kullanarak otomatik olarak çalışır.

## 🚀 Kurulum Adımları

### 1. Webhook URL'sini Alın

AI System Integrator arayüzünden:
- **Kaynak <PERSON>kle** → **Jira AI Bot** seçin
- Jira bağlantınızı ve projenizi seçin
- **Otomatik Bot Tetikleme** bölümünden webhook URL'sini kopyalayın

**Webhook URL:** `https://your-domain.com/api/integrations/jira/webhook`

### 2. Jira'da Webhook Oluşturun

#### Jira Cloud için:
1. Jira'da **Settings** → **System** → **WebHooks** gidin
2. **Create a WebHook** butonuna tıklayın
3. Aşağıdaki bilgileri girin:
   - **Name:** `AI Bot Webhook`
   - **Status:** `Enabled`
   - **URL:** Kopyaladığınız webhook URL'si
   - **Events:** `Issue commented` seçin

#### Jira Server için:
1. **Administration** → **System** → **WebHooks** gidin
2. **Create a WebHook** tıklayın
3. Yukarıdaki bilgileri girin

### 3. Webhook'u Test Edin

Test URL'sine GET request atarak webhook'un çalıştığını doğrulayın:
```
GET https://your-domain.com/api/integrations/jira/webhook/test
```

## 🎯 Kullanım

### Bot Komutları

Jira issue'larında comment olarak aşağıdaki komutları yazabilirsiniz:

#### 🧪 Test Analizi
```
@ai-bot scenario
```
- Pozitif/Negatif test senaryoları
- Sınır değer testleri
- Performans testleri
- Test verileri önerileri

#### 📊 Kapsam Analizi
```
@ai-bot coverage
```
- Fonksiyonel kapsam analizi
- Risk değerlendirmesi
- Test kapsamı önerileri
- Eksik test alanları

#### 🏗️ Kaynak Analizi
```
@ai-bot resource
```
- İnsan kaynağı planlaması
- Teknik kaynak gereksinimleri
- Zaman tahmini
- Maliyet analizi

#### 🔌 API Analizi
```
@ai-bot api
```
- Endpoint spesifikasyonları
- Request/Response formatları
- Güvenlik gereksinimleri
- Performans kriterleri

#### 🔒 Güvenlik Analizi
```
@ai-bot security
```
- Risk analizi
- Güvenlik kontrolleri
- Penetration test önerileri
- Compliance gereksinimleri

#### ⚡ Performans Analizi
```
@ai-bot performance
```
- Performans metrikleri
- Optimizasyon önerileri
- Load testing stratejisi
- Monitoring önerileri

#### 🏗️ Mimari Analiz
```
@ai-bot architecture
```
- Sistem mimarisi önerileri
- Design pattern'ler
- Deployment stratejisi
- Scalability planı

#### 📋 Genel Analiz
```
@ai-bot general
```
- Kapsamlı iş analizi
- Teknik gereksinimler
- Risk faktörleri
- Kabul kriterleri

## 🔄 Çalışma Prensibi

1. **Comment Oluşturma:** Kullanıcı Jira'da `@ai-bot [komut]` yazarak comment oluşturur
2. **Webhook Tetikleme:** Jira webhook'u AI System Integrator'a gönderir
3. **Komut Algılama:** Sistem comment'i analiz eder ve bot komutunu tespit eder
4. **AI Analizi:** Seçilen analiz türüne göre AI analizi çalıştırılır
5. **Yanıt Ekleme:** Analiz sonucu otomatik olarak Jira'ya comment olarak eklenir

## 🛠️ Teknik Detaylar

### Webhook Event Formatı

```json
{
  "webhookEvent": "comment_created",
  "comment": {
    "body": "@ai-bot scenario",
    "author": {
      "displayName": "John Doe"
    }
  },
  "issue": {
    "key": "PROJ-123",
    "fields": {
      "summary": "Issue başlığı",
      "description": "Issue açıklaması",
      "issuetype": {
        "name": "Story"
      },
      "priority": {
        "name": "High"
      }
    }
  }
}
```

### Bot Response Formatı

```markdown
🤖 **AI Bot Analizi - SCENARIO**

## 🧪 Test Senaryoları

### ✅ Pozitif Test Senaryoları
**Senaryo 1: Başarılı Login**
- **Amaç:** Geçerli kullanıcı girişini test et
- **Ön Koşullar:** Aktif kullanıcı hesabı
...

---
*Bu analiz AI tarafından otomatik olarak oluşturulmuştur.*
*Komut: `@ai-bot scenario`*
*Zaman: 15.01.2024 14:30:25*
```

## 🔧 Sorun Giderme

### Webhook Çalışmıyor
1. Webhook URL'sinin doğru olduğunu kontrol edin
2. Jira'dan webhook test edin
3. Server loglarını kontrol edin
4. Network bağlantısını doğrulayın

### Bot Yanıt Vermiyor
1. Comment'te bot komutunun doğru yazıldığını kontrol edin
2. Jira bağlantı bilgilerinin güncel olduğunu doğrulayın
3. AI service'inin çalıştığını kontrol edin

### Yetki Hataları
1. Jira API token'ının geçerli olduğunu kontrol edin
2. Bot'un comment ekleme yetkisi olduğunu doğrulayın
3. Webhook URL'sine erişim izni olduğunu kontrol edin

## 📞 Destek

Sorun yaşadığınızda:
1. Server loglarını kontrol edin
2. Webhook test endpoint'ini çağırın
3. Jira webhook ayarlarını doğrulayın
4. AI System Integrator destek ekibiyle iletişime geçin

## 🎉 Sonuç

Jira AI Bot kurulumu tamamlandıktan sonra:
- ✅ Kullanıcılar doğrudan Jira'da bot komutları yazabilir
- ✅ AI analizleri otomatik olarak çalışır
- ✅ Sonuçlar Jira'ya otomatik eklenir
- ✅ Takım verimliliği artar
- ✅ Analiz kalitesi yükselir

**Artık Jira AI Bot'unuz hazır! 🚀**
