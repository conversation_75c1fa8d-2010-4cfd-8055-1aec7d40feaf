12:58:10 PM [express] GET /api/ai-models 304 in 63ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
12:58:11 PM [express] GET /api/documents 304 in 451ms :: [{"id":3,"name":"demo.docx","type":"docx","c…
12:58:15 PM [express] GET /api/documents/6/components 304 in 123ms :: [{"id":22,"documentId":6,"name"…
12:58:16 PM [express] GET /api/documents/6/requirements 304 in 99ms :: [{"id":47,"documentId":6,"code…
12:58:16 PM [express] GET /api/documents/6 304 in 255ms :: {"id":6,"name":"AD_TLP-2023-000342_Vergi_O…
12:58:16 PM [express] GET /api/documents/6/ai-analysis 304 in 97ms :: {"id":5,"documentId":6,"observa…
12:58:16 PM [express] GET /api/documents/6/api-endpoints 304 in 96ms :: [{"id":10,"documentId":6,"url…
12:58:16 PM [express] GET /api/documents/6/test-scenarios 304 in 95ms :: [{"id":47,"documentId":6,"ti…
AI Assistant isteği: {
  message: 'Aşağıdaki test senaryosunu selenium formatında otomasyon koduna dönüştür. \n' +
    '                 Sadece k...'
}
o1/o3 modelleri için Azure OpenAI API anahtarı: ******GrPml
o1 modeli için API isteği hazırlanıyor...
API parametreleri: {"model":"netas-ai-o1","messages":[{"role":"user","content":"\n    Sen bir doküman analiz asistanısın. Teknik dokümanları analiz edip, gereksinimleri ve test senaryolarını belirleyebilirsin.\n    \n    \n    \n    Aşağıdaki soruya Türkçe olarak cevap ver:\n    \n    Aşağıdaki test senaryosunu selenium formatında otomasyon koduna dönüştür. \n                 Sadece kodu döndür, açıklama yapma. \n                 \n                 Senaryo Başlığı: ******* - Harç Ödeme Yetkisi Olmayan Vergi Türü Seçilmesi Durumunda Tüm Vergi Dairelerinin Listelenmesi\n                 Gereksinim: *******\n                 Ön Koşullar: {\"Kullanıcının BİŞ/KİŞ/KKGİŞ kanalına başarıyla giriş yapmış olması ve Vergi Öde fonksiyonuna erişmesi sağlanmış olmalıdır.\",\"RSA_TVERGI_TUR tablosunda RSA_VTR_YETKI_VRGDAIRE_ODEME parametresi ’H’ olarak işaretli en az bir vergi türü bulunmalıdır.\"}\n                 Adımlar: 1. Kullanıcı, internet şube üzerinden 'Ödemeler -> Vergi / Para Cezası -> Vergi Öde' akışını izleyerek Vergi Öde sayfasını açar.\n2. Bilgi Girişi ekranında 'Vergi Türü Seçerek' seçeneğini seçip Ana Vergi Kodu/Türü alanındaki combobox'ı açar.\n3. Combobox'tan harç ödeme yetkisi olmayan bir vergi türü (örneğin 40 - DAMGA VERGİSİ) seçilir.\n4. Kullanıcı Vergi Dairesi seçimi alanına geldiğinde, sistem RSA_VTR_YETKI_VRGDAIRE_ODEME parametresi ‘H’ olduğu için hiçbir kısıtlama olmadan tüm vergi dairelerini listelemelidir.\n5. Kullanıcı listede tüm vergi dairelerini görebildiğini doğrular.\n                 Beklenen Sonuç: Sistem, harç ödeme yetkisi olmayan vergi türü seçildiğinde tüm vergi dairelerini kısıtlama yapmadan listelemelidir. Kullanıcı ekranda doğru listeyi görmeli ve sonrasındaki ödeme adımlarını tamamlayabilmelidir.\n    "}],"max_tokens":2000}
OpenAI yanıt hatası: BadRequestError: 400 Unsupported parameter: 'max_tokens' is not supported with this model. Use 'max_completion_tokens' instead.
    at Function.generate (/home/<USER>/workspace/node_modules/openai/src/error.ts:72:14)
    at OpenAI.makeStatusError (/home/<USER>/workspace/node_modules/openai/src/core.ts:462:21)
    at OpenAI.makeRequest (/home/<USER>/workspace/node_modules/openai/src/core.ts:526:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Object.getAIAssistantResponse (/home/<USER>/workspace/server/services/openai.ts:724:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:709:24) {
  status: 400,
  headers: {
    'apim-request-id': '7ee316c0-47ad-4b46-bda2-583fe2711fa8',
    'azureml-model-session': 'v20250411-1-167922813',
    'content-length': '245',
    'content-type': 'application/json',
    date: 'Wed, 16 Apr 2025 13:07:21 GMT',
    'ms-azureml-model-error-reason': 'model_error',
    'ms-azureml-model-error-statuscode': '400',
    'ms-azureml-model-time': '26',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    'x-aml-cluster': 'hyena-northcentralus-01',
    'x-content-type-options': 'nosniff',
    'x-envoy-upstream-service-time': '29',
    'x-ms-client-request-id': '7ee316c0-47ad-4b46-bda2-583fe2711fa8',
    'x-ms-deployment-name': 'netas-ai-o1',
    'x-ms-rai-invoked': 'true',
    'x-ms-region': 'East US',
    'x-ratelimit-limit-requests': '100',
    'x-ratelimit-limit-tokens': '600000',
    'x-ratelimit-remaining-requests': '99',
    'x-ratelimit-remaining-tokens': '597585',
    'x-request-id': '202b1746-a9bb-4e6e-bd44-75f77c993956'
  },
  request_id: '202b1746-a9bb-4e6e-bd44-75f77c993956',
  error: {
    message: "Unsupported parameter: 'max_tokens' is not supported with this model. Use 'max_completion_tokens' instead.",
    type: 'invalid_request_error',
    param: 'max_tokens',
    code: 'unsupported_parameter'
  },
  code: 'unsupported_parameter',
  param: 'max_tokens',
  type: 'invalid_request_error'
}
1:07:22 PM [express] POST /api/ai-assistant 200 in 522ms :: {"answer":"Otomasyon kodu oluşturulurken…
