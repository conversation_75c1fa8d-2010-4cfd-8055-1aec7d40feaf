Browserslist: browsers data (caniuse-lite) is 6 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
12:45:04 PM [vite] ✨ new dependencies optimized: @radix-ui/react-switch
12:45:04 PM [vite] ✨ optimized dependencies changed. reloading
Kullanıcı projeleri getirme hatası: TypeError: storage.getUserProjects is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:494:42)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
12:45:47 PM [express] GET /api/user-projects 500 in 34ms :: {"error":"Kullanıcı projeleri getirilirke…
İzinleri getirme hatası: TypeError: storage.getUserPermissions is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:120:45)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
12:45:47 PM [express] GET /api/permissions 500 in 24ms :: {"error":"İzinler getirilirken bir hata olu…
Kullanıcı projeleri getirme hatası: TypeError: storage.getUserProjects is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:494:42)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
12:45:48 PM [express] GET /api/user-projects 500 in 24ms :: {"error":"Kullanıcı projeleri getirilirke…
İzinleri getirme hatası: TypeError: storage.getUserPermissions is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:120:45)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
12:45:48 PM [express] GET /api/permissions 500 in 24ms :: {"error":"İzinler getirilirken bir hata olu…
12:45:49 PM [express] GET /api/ai-models 304 in 22ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
12:45:49 PM [express] GET /api/documents 200 in 256ms :: [{"id":21,"name":"TrendKart_Analiz_Dokumani.…
Kullanıcı projeleri getirme hatası: TypeError: storage.getUserProjects is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:494:42)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
12:45:51 PM [express] GET /api/user-projects 500 in 24ms :: {"error":"Kullanıcı projeleri getirilirke…
İzinleri getirme hatası: TypeError: storage.getUserPermissions is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:120:45)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
12:45:51 PM [express] GET /api/permissions 500 in 24ms :: {"error":"İzinler getirilirken bir hata olu…
Kullanıcı projeleri getirme hatası: TypeError: storage.getUserProjects is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:494:42)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
12:45:55 PM [express] GET /api/user-projects 500 in 23ms :: {"error":"Kullanıcı projeleri getirilirke…
12:45:55 PM [express] GET /api/prompts 304 in 23ms :: [{"key":"DOCUMENT_ANALYSIS_PROMPT","value":"Lüt…
İzinleri getirme hatası: TypeError: storage.getUserPermissions is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:120:45)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
12:45:55 PM [express] GET /api/permissions 500 in 23ms :: {"error":"İzinler getirilirken bir hata olu…
12:45:56 PM [express] GET /api/ai-models 304 in 22ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
İzinleri getirme hatası: TypeError: storage.getUserPermissions is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:120:45)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
12:46:14 PM [express] GET /api/permissions 500 in 23ms :: {"error":"İzinler getirilirken bir hata olu…
Kullanıcı projeleri getirme hatası: TypeError: storage.getUserProjects is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:494:42)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
12:46:14 PM [express] GET /api/user-projects 500 in 24ms :: {"error":"Kullanıcı projeleri getirilirke…
12:46:14 PM [express] GET /api/ai-models 304 in 28ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
12:46:14 PM [express] GET /api/documents 304 in 195ms :: [{"id":21,"name":"TrendKart_Analiz_Dokumani.…
Kullanıcı projeleri getirme hatası: TypeError: storage.getUserProjects is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:494:42)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
12:46:15 PM [express] GET /api/user-projects 500 in 23ms :: {"error":"Kullanıcı projeleri getirilirke…
İzinleri getirme hatası: TypeError: storage.getUserPermissions is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:120:45)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
12:46:15 PM [express] GET /api/permissions 500 in 29ms :: {"error":"İzinler getirilirken bir hata olu…
Kullanıcı projeleri getirme hatası: TypeError: storage.getUserProjects is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:494:42)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
12:46:17 PM [express] GET /api/user-projects 500 in 24ms :: {"error":"Kullanıcı projeleri getirilirke…
İzinleri getirme hatası: TypeError: storage.getUserPermissions is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:120:45)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
12:46:17 PM [express] GET /api/permissions 500 in 32ms :: {"error":"İzinler getirilirken bir hata olu…
Kullanıcı projeleri getirme hatası: TypeError: storage.getUserProjects is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:494:42)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
12:46:22 PM [express] GET /api/user-projects 500 in 24ms :: {"error":"Kullanıcı projeleri getirilirke…
İzinleri getirme hatası: TypeError: storage.getUserPermissions is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:120:45)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
12:46:22 PM [express] GET /api/permissions 500 in 23ms :: {"error":"İzinler getirilirken bir hata olu…
