import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { teachAiAboutRequirement, getCoverageValidation } from "@/lib/api";
import { Loader2 } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { type Requirement } from "@shared/schema";

// Form şeması
const teachingFormSchema = z.object({
  missingInformation: z.string().min(10, {
    message: "Eksik bilgiler en az 10 karakter olmalıdır",
  }),
  solution: z.string().min(20, {
    message: "Çözüm önerisi en az 20 karakter olmalıdır",
  }),
});

// Form tipi
type TeachingFormValues = z.infer<typeof teachingFormSchema>;

interface AiLearningDialogProps {
  isOpen: boolean;
  onClose: () => void;
  requirementCode: string;
  requirementDescription: string;
  documentId: number;
  missingRequirements?: string[]; // Kapsanmayan gereksinimler listesi
  allRequirements?: Requirement[]; // Tüm gereksinimler
}

interface AutoGeneratedSuggestion {
  missingInformation: string;
  solution: string;
  requirementCode: string;
  confidence: number;
}

export function AiLearningDialog({
  isOpen,
  onClose,
  requirementCode,
  requirementDescription,
  documentId,
  missingRequirements = [],
  allRequirements = []
}: AiLearningDialogProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("single");
  const [bulkMode, setBulkMode] = useState<boolean>(false);
  const [autoSuggestions, setAutoSuggestions] = useState<AutoGeneratedSuggestion[]>([]);
  const [isGeneratingSuggestions, setIsGeneratingSuggestions] = useState<boolean>(false);
  const [selectedSuggestions, setSelectedSuggestions] = useState<string[]>([]);

  // Form yapılandırması
  const form = useForm<TeachingFormValues>({
    resolver: zodResolver(teachingFormSchema),
    defaultValues: {
      missingInformation: "",
      solution: "",
    },
  });
  
  // Tek gereksinim için AI önerisi yükle
  useEffect(() => {
    if (!bulkMode && isOpen && requirementCode) {
      generateSingleSuggestion();
    }
  }, [isOpen, requirementCode, bulkMode]);
  
  // Tek gereksinim için AI önerisi oluştur
  const generateSingleSuggestion = async () => {
    try {
      // Normalde burada API çağrısı yapılacak ve gerçek AI önerisi alınacak
      // Şimdilik gereksinim içeriğine göre otomatik öneri oluşturalım
      
      // Gereksinim açıklamasını analiz ederek akıllı öneriler oluştur
      const sugg = {
        missingInformation: `"${requirementDescription}" için test senaryosu oluşturulması gerekiyor. Bu gereksinim, ${
          requirementDescription.toLowerCase().includes("giriş") || requirementDescription.toLowerCase().includes("login") ? "kullanıcı yetkilendirme ve kimlik doğrulama işlemleri" :
          requirementDescription.toLowerCase().includes("rapor") || requirementDescription.toLowerCase().includes("liste") ? "veri görüntüleme, raporlama ve listeleme fonksiyonları" :
          requirementDescription.toLowerCase().includes("kaydet") || requirementDescription.toLowerCase().includes("veritabanı") ? "veritabanı işlemleri ve veri saklama fonksiyonları" :
          requirementDescription.toLowerCase().includes("api") || requirementDescription.toLowerCase().includes("entegrasyon") ? "harici API entegrasyonları ve veri alışverişi" :
          requirementDescription.toLowerCase().includes("güvenlik") || requirementDescription.toLowerCase().includes("yetki") ? "güvenlik kontrolleri ve yetkilendirme mekanizmaları" :
          requirementDescription.toLowerCase().includes("performans") ? "performans gereksinimleri ve optimizasyon kriterleri" :
          requirementDescription.toLowerCase().includes("arayüz") || requirementDescription.toLowerCase().includes("ui") ? "kullanıcı arayüzü özellikleri ve etkileşimleri" :
          "temel iş mantığı ve fonksiyonel özellikler"
        } ile ilgilidir ve kapsamlı test edilmelidir.`,
        solution: `"${requirementCode}" için önerilen test senaryosu:

**Test Senaryosu Başlığı:** ${requirementCode} - ${requirementDescription.length > 30 ? requirementDescription.substring(0, 30) + "..." : requirementDescription} Fonksiyonellik Testi

**Ön Koşullar:**
- Yetkili kullanıcı hesabıyla sisteme giriş yapılmış olmalı
${requirementDescription.toLowerCase().includes("rapor") || requirementDescription.toLowerCase().includes("liste") ? "- Test için gerekli örnek veriler sistemde mevcut olmalı" : ""}
${requirementDescription.toLowerCase().includes("api") ? "- API bağlantıları ve entegrasyonlar çalışır durumda olmalı" : ""}

**Test Adımları:**
1. ${requirementDescription.toLowerCase().includes("menü") || requirementDescription.toLowerCase().includes("sayfa") ? 
    "İlgili menü üzerinden gerekli sayfaya erişim sağlanır" : 
    "Sistemin ilgili modülüne/ekranına giriş yapılır"}
2. ${requirementDescription.toLowerCase().includes("form") ? 
    "Test verisiyle form alanları doldurulur" : 
    requirementDescription.toLowerCase().includes("liste") ? 
    "Listeleme kriterleri ve filtreler belirlenir" : 
    requirementDescription.toLowerCase().includes("ara") ? 
    "Arama kriterleri belirtilir ve arama yapılır" : 
    "Gereksinim için gereken işlem parametreleri ve veriler hazırlanır"}
3. ${requirementDescription.toLowerCase().includes("düğme") || requirementDescription.toLowerCase().includes("buton") ? 
    "İlgili işlem butonu/düğmesi tıklanır" : 
    "İşlemi başlatacak aksiyon gerçekleştirilir"}
4. ${requirementDescription.toLowerCase().includes("onay") ? 
    "Gerekli onay işlemi yapılır ve sonuçlar gözlemlenir" : 
    "Sistemin yanıtı ve davranışı gözlemlenir"}
5. Sonuçlar doğrulanır ve gereksinime uygunluğu kontrol edilir

**Beklenen Sonuçlar:**
1. ${requirementDescription.toLowerCase().includes("kaydet") ? 
    "Veri başarıyla kaydedilmeli ve veritabanında görülebilmelidir" : 
    requirementDescription.toLowerCase().includes("liste") || requirementDescription.toLowerCase().includes("rapor") ? 
    "Veriler doğru şekilde listelenmeli ve filtreleme kriterleri çalışmalıdır" : 
    requirementDescription.toLowerCase().includes("sil") ? 
    "İlgili veri başarıyla silinmeli ve artık erişilebilir olmamalıdır" : 
    "Sistem, gereksinimdeki işlevi başarıyla gerçekleştirmelidir"}
2. Kullanıcıya işlemin sonucu hakkında uygun geri bildirim verilmelidir
3. İşlem kayıtları (log) doğru şekilde tutulmalıdır

**Test Verileri:**
- Test için gerekli örnek veri setleri oluşturulmalıdır
- Sınır değerler ve geçersiz girişler test edilmelidir`
      };
      
      // Formu otomatik olarak doldur
      form.setValue("missingInformation", sugg.missingInformation);
      form.setValue("solution", sugg.solution);
    } catch (error) {
      console.error("Öneri oluşturma hatası:", error);
      toast({
        title: "Öneri oluşturulamadı",
        description: "Gereksinim için otomatik öneri oluşturulurken bir hata oluştu",
        variant: "destructive",
      });
    }
  };
  
  // Otomatik öneri için AI modelinin oluşturduğu tahminler
  useEffect(() => {
    if (bulkMode && isOpen && missingRequirements && missingRequirements.length > 0) {
      generateAutoSuggestions();
    }
  }, [bulkMode, isOpen, missingRequirements]);
  
  // Toplu öneri oluşturma
  const generateAutoSuggestions = async () => {
    setIsGeneratingSuggestions(true);
    
    try {
      // Normalde burada AI tabanlı öneriler oluşturulacak
      // Şimdilik gereksinimlere özel akıllı içerik oluşturalım
      const mockSuggestions: AutoGeneratedSuggestion[] = missingRequirements.map(reqCode => {
        const req = allRequirements.find(r => r.code === reqCode);
        const reqDesc = req?.description || reqCode;
        
        // Gereksinim tipini belirle
        const isAuthRelated = reqDesc.toLowerCase().includes("giriş") || reqDesc.toLowerCase().includes("login") || reqDesc.toLowerCase().includes("yetki");
        const isListingRelated = reqDesc.toLowerCase().includes("liste") || reqDesc.toLowerCase().includes("rapor") || reqDesc.toLowerCase().includes("görüntüle");
        const isFormRelated = reqDesc.toLowerCase().includes("form") || reqDesc.toLowerCase().includes("kaydet") || reqDesc.toLowerCase().includes("güncelle");
        const isApiRelated = reqDesc.toLowerCase().includes("api") || reqDesc.toLowerCase().includes("entegrasyon") || reqDesc.toLowerCase().includes("bağlantı");
        const isSearchRelated = reqDesc.toLowerCase().includes("ara") || reqDesc.toLowerCase().includes("filtre") || reqDesc.toLowerCase().includes("sorgula");
        
        // Gereksinimlere özel çözüm oluştur
        let solution = '';
        let confidence = 0.75 + (Math.random() * 0.2); // 0.75-0.95 arası rastgele güven skoru
        
        if (isAuthRelated) {
          solution = `**${reqCode} - Kullanıcı Yetkilendirme Testi**
          
**Ön Koşul:** 
- Test için geçerli ve geçersiz kullanıcı bilgileri hazırlanmış olmalı

**Test Adımları:**
1. Giriş sayfasına erişim sağlanır
2. Kullanıcı kimlik bilgileri girilir
3. Giriş/doğrulama işlemi başlatılır
4. Sistem yanıtı ve yönlendirmesi kontrol edilir
5. Yetkisiz işlemler test edilir

**Beklenen Sonuç:**
1. Geçerli kimlik bilgileriyle giriş başarılı olmalı
2. Geçersiz kimlik bilgileriyle uygun hata mesajı gösterilmeli
3. Kullanıcı yetkileri doğru şekilde uygulanmalı`;
          confidence = 0.9;
        } else if (isListingRelated) {
          solution = `**${reqCode} - Listeleme/Raporlama Testi**
          
**Ön Koşul:** 
- Test verilerinin sistemde oluşturulmuş olması
- Kullanıcının raporlama yetkilerine sahip olması

**Test Adımları:**
1. İlgili listeleme/raporlama ekranına erişilir
2. Filtreleme kriterleri uygulanır
3. Sıralama seçenekleri test edilir
4. Sayfalama fonksiyonu kontrol edilir
5. Rapor oluşturma/dışa aktarma seçenekleri test edilir

**Beklenen Sonuç:**
1. Veriler doğru şekilde filtrelenmeli ve gösterilmeli
2. Sıralama doğru çalışmalı
3. Sayfalama navigasyonu beklendiği gibi çalışmalı
4. Rapor çıktıları doğru formatta oluşturulmalı`;
        } else if (isFormRelated) {
          solution = `**${reqCode} - Form İşleme Testi**
          
**Ön Koşul:** 
- Kullanıcının form işlemleri için gereken yetkilere sahip olması

**Test Adımları:**
1. İlgili form ekranına erişilir
2. Form alanları geçerli verilerle doldurulur
3. Kaydet/Gönder işlemi yapılır
4. Doğrulama kuralları test edilir (geçersiz veri girişleri ile)
5. Form sonrası yönlendirme kontrol edilir

**Beklenen Sonuç:**
1. Geçerli veriler başarıyla kaydedilmeli
2. Geçersiz veriler için uygun hata mesajları gösterilmeli
3. Zorunlu alanlar doğru şekilde kontrol edilmeli
4. İşlem sonrası kullanıcıya doğru geri bildirim verilmeli`;
        } else if (isApiRelated) {
          solution = `**${reqCode} - API Entegrasyonu Testi**
          
**Ön Koşul:** 
- API bağlantıları yapılandırılmış olmalı
- Test ortamında API erişimi sağlanmış olmalı

**Test Adımları:**
1. API bağlantısı başlatılır
2. İstek parametreleri hazırlanır ve gönderilir
3. Yanıt alınır ve doğruluğu kontrol edilir
4. Hata durumları test edilir
5. Performans ve zaman aşımı durumları kontrol edilir

**Beklenen Sonuç:**
1. API başarılı yanıt vermeli
2. Veri formatı ve yapısı beklendiği gibi olmalı
3. Hata durumları uygun şekilde yönetilmeli
4. İstek/yanıt süresi kabul edilebilir aralıkta olmalı`;
          confidence = 0.95;
        } else if (isSearchRelated) {
          solution = `**${reqCode} - Arama/Filtreleme Testi**
          
**Ön Koşul:** 
- Test için aranabilir veriler sistemde mevcut olmalı

**Test Adımları:**
1. Arama ekranına erişilir
2. Arama kriterleri girilir
3. Arama işlemi başlatılır
4. Sonuçlar kontrol edilir
5. Gelişmiş filtreleme seçenekleri test edilir

**Beklenen Sonuç:**
1. Arama sonuçları doğru kriterlere göre filtrelenmeli
2. Sonuç yoksa uygun mesaj gösterilmeli
3. Filtreleme seçenekleri doğru çalışmalı
4. Performans kabul edilebilir seviyede olmalı`;
        } else {
          // Genel amaçlı test senaryosu
          solution = `**${reqCode} - Fonksiyonel Test**
          
**Ön Koşul:** 
- Kullanıcının gerekli yetkilere sahip olması
- Test için gerekli verilerin hazırlanmış olması

**Test Adımları:**
1. İlgili işlem ekranına erişilir
2. Gerekli parametreler ve veriler hazırlanır
3. İşlem başlatılır
4. Sistem yanıtı ve sonuçları gözlemlenir
5. Hata durumları test edilir

**Beklenen Sonuç:**
1. İşlem başarıyla tamamlanmalı
2. Veriler doğru şekilde işlenmeli
3. Kullanıcıya uygun geri bildirim verilmeli
4. Sistem tutarlı davranmalı`;
          confidence = 0.8;
        }
        
        return {
          requirementCode: reqCode,
          missingInformation: `"${reqDesc}" gereksinimi test senaryolarında yeterince kapsanmamış görünüyor. ${
            isAuthRelated ? "Yetkilendirme ve kullanıcı girişiyle ilgili test senaryoları eksik." :
            isListingRelated ? "Listeleme ve raporlama fonksiyonları için test kapsamı yetersiz." :
            isFormRelated ? "Form işlemleri ve veri doğrulama testleri eksik." :
            isApiRelated ? "API entegrasyonu ve dış sistem bağlantıları test edilmemiş." :
            isSearchRelated ? "Arama ve filtreleme fonksiyonları için test senaryoları yetersiz." :
            "Bu gereksinim için uygun test senaryoları oluşturulmamış."
          }`,
          solution,
          confidence
        };
      });
      
      // 1 saniye gecikme ekleyelim - gerçek API entegrasyonunda silinecek
      setTimeout(() => {
        setAutoSuggestions(mockSuggestions);
        setIsGeneratingSuggestions(false);
      }, 1000);
      
    } catch (error) {
      console.error("Öneri oluşturma hatası:", error);
      toast({
        title: "Öneri oluşturulamadı",
        description: "Otomatik öneriler oluşturulurken bir hata oluştu",
        variant: "destructive",
      });
      setIsGeneratingSuggestions(false);
    }
  };

  // Form gönderme işlemi
  async function onSubmit(data: TeachingFormValues) {
    setIsSubmitting(true);
    
    try {
      const response = await teachAiAboutRequirement({
        documentId,
        requirementCode,
        requirementDescription,
        missingInformation: data.missingInformation,
        solution: data.solution,
      });

      if (response.ok) {
        toast({
          title: "Başarılı",
          description: "AI'a gereksinim hakkında bilgi başarıyla öğretildi",
          variant: "default",
        });
        onClose();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || "Bilinmeyen bir hata oluştu");
      }
    } catch (error) {
      toast({
        title: "Hata",
        description: error instanceof Error ? error.message : "Bir hata oluştu",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  // Toplu öneri gönderme
  const submitBulkSuggestions = async () => {
    if (selectedSuggestions.length === 0) {
      toast({
        title: "Seçim yapılmadı",
        description: "Lütfen göndermek istediğiniz en az bir öneriyi seçin",
        variant: "destructive",
      });
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Seçilen önerileri gönder
      const selectedItems = autoSuggestions.filter(sugg => 
        selectedSuggestions.includes(sugg.requirementCode)
      );
      
      // Her bir öneri için API çağrısı yap
      const promises = selectedItems.map(item => 
        teachAiAboutRequirement({
          documentId,
          requirementCode: item.requirementCode,
          requirementDescription: allRequirements.find(r => r.code === item.requirementCode)?.description || "",
          missingInformation: item.missingInformation,
          solution: item.solution
        })
      );
      
      await Promise.all(promises);
      
      toast({
        title: "Başarılı",
        description: `${selectedItems.length} gereksinim için bilgiler AI'a başarıyla öğretildi`,
        variant: "default",
      });
      
      onClose();
    } catch (error) {
      toast({
        title: "Hata",
        description: "Öneriler gönderilirken bir hata oluştu",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Öneri seçim durumunu değiştir
  const toggleSuggestionSelection = (reqCode: string) => {
    setSelectedSuggestions(prev => {
      if (prev.includes(reqCode)) {
        return prev.filter(code => code !== reqCode);
      } else {
        return [...prev, reqCode];
      }
    });
  };
  
  // Tüm önerileri seç/kaldır
  const toggleSelectAll = () => {
    if (selectedSuggestions.length === autoSuggestions.length) {
      setSelectedSuggestions([]);
    } else {
      setSelectedSuggestions(autoSuggestions.map(s => s.requirementCode));
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className={bulkMode ? "max-w-4xl" : "sm:max-w-[550px]"}>
        <DialogHeader>
          <div className="flex justify-between items-center">
            <DialogTitle>AI'a Gereksinim Bilgisi Öğret</DialogTitle>
            
            {/* Mod değiştirme butonu */}
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setBulkMode(!bulkMode)}
              className="text-xs"
            >
              {bulkMode ? (
                <>
                  <i className="fas fa-user mr-1"></i>
                  Tekli Mod
                </>
              ) : (
                <>
                  <i className="fas fa-users mr-1"></i>
                  Toplu Mod
                </>
              )}
            </Button>
          </div>
          <DialogDescription>
            {bulkMode 
              ? "Kapsanmayan tüm gereksinimler için AI önerileri oluşturup toplu olarak öğretebilirsiniz."
              : "Bu gereksinimin test senaryolarında nasıl kapsanması gerektiğini AI'a öğretmek için aşağıdaki bilgileri doldurun."
            }
          </DialogDescription>
        </DialogHeader>

        {bulkMode ? (
          // TOPLU MOD
          <div className="space-y-4">
            {/* Kapsanmayan gereksinimlerin listesi */}
            <div className="border rounded-lg overflow-hidden">
              <div className="bg-neutral-100 px-4 py-2 font-medium text-sm flex justify-between items-center">
                <span>Kapsanmayan Gereksinimler ({missingRequirements.length})</span>
                
                {autoSuggestions.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-xs h-7"
                    onClick={toggleSelectAll}
                  >
                    {selectedSuggestions.length === autoSuggestions.length 
                      ? "Tümünü Kaldır" 
                      : "Tümünü Seç"}
                  </Button>
                )}
              </div>
              
              <div className="max-h-[400px] overflow-y-auto p-2">
                {isGeneratingSuggestions ? (
                  <div className="flex items-center justify-center p-8 text-center">
                    <div className="space-y-3">
                      <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
                      <p className="text-sm text-neutral-600">AI öneri oluşturuyor...</p>
                    </div>
                  </div>
                ) : autoSuggestions.length > 0 ? (
                  <div className="space-y-4">
                    {autoSuggestions.map((sugg, idx) => (
                      <div 
                        key={sugg.requirementCode} 
                        className={`border rounded-lg p-3 ${
                          selectedSuggestions.includes(sugg.requirementCode) 
                            ? 'border-primary bg-primary/5' 
                            : 'border-neutral-200'
                        }`}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center">
                            <input 
                              type="checkbox" 
                              id={`sugg-${idx}`}
                              checked={selectedSuggestions.includes(sugg.requirementCode)}
                              onChange={() => toggleSuggestionSelection(sugg.requirementCode)}
                              className="w-4 h-4 mr-2 accent-primary"
                            />
                            <label htmlFor={`sugg-${idx}`} className="font-medium">
                              {sugg.requirementCode}
                            </label>
                          </div>
                          <div className="text-xs bg-neutral-100 px-2 py-1 rounded">
                            Güvenilirlik: {Math.round(sugg.confidence * 100)}%
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
                          <div className="text-xs border rounded p-2 bg-neutral-50">
                            <div className="font-medium mb-1 text-neutral-700">Problem:</div>
                            <div>{sugg.missingInformation}</div>
                          </div>
                          <div className="text-xs border rounded p-2 bg-neutral-50">
                            <div className="font-medium mb-1 text-neutral-700">Çözüm Önerisi:</div>
                            <div>{sugg.solution}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-sm text-neutral-500">
                    <i className="fas fa-robot text-2xl mb-2 block"></i>
                    Öneriler oluşturmak için "Otomatik Öneri Oluştur" butonuna tıklayın
                  </div>
                )}
              </div>
            </div>
            
            <DialogFooter className="flex justify-between gap-2">
              <div>
                {autoSuggestions.length === 0 && (
                  <Button 
                    type="button" 
                    onClick={generateAutoSuggestions}
                    disabled={isGeneratingSuggestions || isSubmitting}
                    variant="outline"
                  >
                    {isGeneratingSuggestions ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        AI Çalışıyor...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-magic mr-2"></i>
                        Otomatik Öneri Oluştur
                      </>
                    )}
                  </Button>
                )}
              </div>
              <div className="flex gap-2">
                <Button variant="outline" type="button" onClick={onClose} disabled={isSubmitting}>
                  İptal
                </Button>
                <Button 
                  type="button" 
                  onClick={submitBulkSuggestions} 
                  disabled={isSubmitting || selectedSuggestions.length === 0}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" /> 
                      Kaydediliyor...
                    </>
                  ) : (
                    <>
                      <i className="fas fa-graduation-cap mr-2"></i>
                      Seçili Önerileri Gönder ({selectedSuggestions.length})
                    </>
                  )}
                </Button>
              </div>
            </DialogFooter>
          </div>
        ) : (
          // TEKLİ MOD
          <>
            <div className="border rounded-md p-3 bg-neutral-50 mb-4">
              <div className="mb-2">
                <span className="text-xs font-semibold text-neutral-500">Gereksinim Kodu:</span>
                <span className="ml-2 text-sm font-medium">{requirementCode}</span>
              </div>
              <div>
                <span className="text-xs font-semibold text-neutral-500">Gereksinim Açıklaması:</span>
                <div className="text-sm mt-1 text-neutral-700">{requirementDescription}</div>
              </div>
            </div>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="missingInformation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bu gereksinim neden kapsanmıyor?</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Bu gereksinimle ilgili kapsanmama nedenini veya eksik bilgileri yazın"
                          className="h-24"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="solution"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Önerilen Çözüm</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Bu gereksinimin nasıl doğru şekilde kapsanabileceğine dair çözüm önerinizi yazın"
                          className="h-32"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button variant="outline" type="button" onClick={onClose} disabled={isSubmitting}>
                    İptal
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" /> 
                        Kaydediliyor...
                      </>
                    ) : (
                      "AI'a Öğret"
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}