import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useAuth } from "@/hooks/use-auth";
import { usePermission } from "@/hooks/use-permission";
import { useParams, useLocation } from "wouter";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { getQueryFn, apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import Header from "@/components/header";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, Dialog<PERSON>itle, DialogTrigger } from "@/components/ui/dialog";
import { Loader2, Calendar, Book, FileType, User, Users, UserPlus, Settings, ArrowLeft, CheckCircle2, XCircle, FileSymlink, CheckCircle, 
  PlusCircle, FileDown, Save, Trash2, Upload, Download, Globe, Database } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { PROJECT_ROLES } from "@/hooks/use-permission";

// API Bağlantısı için form şeması
const apiConnectionSchema = z.object({
  name: z.string().min(1, "Bağlantı adı gereklidir"),
  type: z.enum(["jira", "testrail", "alm", "visium"], {
    required_error: "Bağlantı türü seçilmelidir",
  }),
  baseUrl: z.string().url("Geçerli bir URL girilmelidir"),
  apiKey: z.string().optional(),
  username: z.string().optional(),
  token: z.string().optional(),
  settings: z.string().optional().transform(val => {
    try {
      return val ? JSON.parse(val) : {};
    } catch {
      return {};
    }
  }),
});

type ApiConnectionFormValues = z.infer<typeof apiConnectionSchema>;

export default function ProjectDetails() {
  const { id } = useParams<{ id: string }>();
  const projectId = parseInt(id);
  const { user } = useAuth();
  const { hasProjectAccess } = usePermission();
  const { toast } = useToast();
  const [, navigate] = useLocation();
  const [activeTab, setActiveTab] = useState("overview");
  const [addUserDialogOpen, setAddUserDialogOpen] = useState(false);
  const [addConnectionDialogOpen, setAddConnectionDialogOpen] = useState(false);
  const [addSourceDialogOpen, setAddSourceDialogOpen] = useState(false);
  const [sourceTab, setSourceTab] = useState("upload"); // "upload" veya "integration"
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [selectedIntegration, setSelectedIntegration] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [newUser, setNewUser] = useState({
    username: "",
    role: PROJECT_ROLES.VIEWER
  });
  
  // API Bağlantısı form
  const connectionForm = useForm<ApiConnectionFormValues>({
    resolver: zodResolver(apiConnectionSchema),
    defaultValues: {
      name: "",
      type: "jira",
      baseUrl: "",
      apiKey: "",
      username: "",
      token: "",
      settings: ""
    }
  });

  // Proje detaylarını getir
  const { data: project, isLoading: projectLoading } = useQuery({
    queryKey: [`/api/projects/${projectId}`],
    queryFn: getQueryFn({ on401: "throw" }),
  });

  // Proje kaynaklarını getir
  const { data: documents = [], isLoading: documentsLoading } = useQuery({
    queryKey: [`/api/projects/${projectId}/documents`],
    queryFn: getQueryFn({ on401: "throw" }),
    enabled: !!projectId,
  });

  // Proje kullanıcılarını getir
  const { data: projectUsers = [], isLoading: usersLoading } = useQuery({
    queryKey: [`/api/projects/${projectId}/users`],
    queryFn: getQueryFn({ on401: "throw" }),
    enabled: !!projectId,
  });
  
  // Proje API bağlantılarını getir
  const { data: apiConnections = [], isLoading: connectionsLoading } = useQuery({
    queryKey: [`/api/projects/${projectId}/connections`],
    queryFn: getQueryFn({ on401: "throw", on404: "returnEmptyArray" }),
    enabled: !!projectId,
  });

  // Kullanıcı ekle
  const addUserMutation = useMutation({
    mutationFn: async (userData: typeof newUser) => {
      const res = await apiRequest("POST", `/api/projects/${projectId}/users`, userData);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/users`] });
      toast({
        title: "Kullanıcı eklendi",
        description: "Kullanıcı projeye başarıyla eklendi.",
      });
      setAddUserDialogOpen(false);
      setNewUser({ username: "", role: PROJECT_ROLES.VIEWER });
    },
    onError: (error: Error) => {
      toast({
        title: "Kullanıcı eklenemedi",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Kullanıcı rolünü değiştir
  const updateUserRoleMutation = useMutation({
    mutationFn: async ({ userId, role }: { userId: number; role: string }) => {
      const res = await apiRequest("PUT", `/api/projects/${projectId}/users/${userId}`, { role });
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/users`] });
      toast({
        title: "Rol güncellendi",
        description: "Kullanıcı rolü başarıyla güncellendi.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Rol güncellenemedi",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Kullanıcı kaldır
  const removeUserMutation = useMutation({
    mutationFn: async (userId: number) => {
      await apiRequest("DELETE", `/api/projects/${projectId}/users/${userId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/users`] });
      toast({
        title: "Kullanıcı kaldırıldı",
        description: "Kullanıcı projeden başarıyla kaldırıldı.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Kullanıcı kaldırılamadı",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleAddUser = (e: React.FormEvent) => {
    e.preventDefault();
    addUserMutation.mutate(newUser);
  };

  const handleChangeUserRole = (userId: number, role: string) => {
    updateUserRoleMutation.mutate({ userId, role });
  };

  const handleRemoveUser = (userId: number) => {
    if (window.confirm("Bu kullanıcıyı projeden kaldırmak istediğinizden emin misiniz?")) {
      removeUserMutation.mutate(userId);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewUser(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Dosya yükleme işlemini gerçekleştir
  const uploadDocumentMutation = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('projectId', projectId.toString());
      
      const res = await fetch('/api/documents/upload', {
        method: 'POST',
        body: formData,
      });
      
      if (!res.ok) {
        throw new Error(`Dosya yüklenemedi: ${res.statusText}`);
      }
      
      return await res.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/documents`] });
      toast({
        title: "Dosya yüklendi",
        description: "Kaynak başarıyla yüklendi ve analize hazır.",
      });
      
      // Otomatik analiz başlat
      analyzeDocumentMutation.mutate(data.document.id);
    },
    onError: (error: Error) => {
      toast({
        title: "Dosya yüklenemedi",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Entegrasyondan kaynak ekle
  const fetchFromIntegrationMutation = useMutation({
    mutationFn: async (connectionId: number) => {
      const res = await apiRequest("POST", `/api/projects/${projectId}/connections/${connectionId}/fetch`, {});
      return await res.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/documents`] });
      toast({
        title: "Veriler alındı",
        description: "Entegrasyondan kaynaklar başarıyla alındı.",
      });
      
      // Otomatik analiz başlat (birden fazla kaynak için)
      if (data.documents && data.documents.length > 0) {
        data.documents.forEach((doc: any) => {
          analyzeDocumentMutation.mutate(doc.id);
        });
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Veriler alınamadı",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Doküman analizi
  const analyzeDocumentMutation = useMutation({
    mutationFn: async (documentId: number) => {
      const res = await apiRequest("POST", `/api/documents/${documentId}/analyze`, {});
      return await res.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/documents`] });
      toast({
        title: "Analiz tamamlandı",
        description: "Kaynak başarıyla analiz edildi.",
      });
      setIsAnalyzing(false);
    },
    onError: (error: Error) => {
      toast({
        title: "Analiz yapılamadı",
        description: error.message,
        variant: "destructive",
      });
      setIsAnalyzing(false);
    },
  });
  
  // Test senaryosu oluştur
  const generateTestScenariosMutation = useMutation({
    mutationFn: async (documentId: number) => {
      const res = await apiRequest("POST", `/api/documents/${documentId}/generate-test-scenarios`, {});
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/documents`] });
      toast({
        title: "Test senaryoları oluşturuldu",
        description: "Kaynak için test senaryoları başarıyla oluşturuldu.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Test senaryoları oluşturulamadı",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Dosya seçimi
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFiles(Array.from(e.target.files));
    }
  };
  
  // Dosya yükleme
  const handleUploadFiles = () => {
    if (selectedFiles.length === 0) {
      toast({
        title: "Dosya seçilmedi",
        description: "Lütfen önce yüklenecek dosya(ları) seçin.",
        variant: "destructive",
      });
      return;
    }
    
    setIsAnalyzing(true);
    
    // Her dosyayı sırayla yükle
    selectedFiles.forEach(file => {
      uploadDocumentMutation.mutate(file);
    });
    
    // Yükleme tamamlandıktan sonra modal'ı kapat
    setAddSourceDialogOpen(false);
    setSelectedFiles([]);
  };
  
  // Entegrasyondan veri çekme
  const handleFetchFromIntegration = () => {
    if (!selectedIntegration) {
      toast({
        title: "Entegrasyon seçilmedi",
        description: "Lütfen önce bir entegrasyon kaynağı seçin.",
        variant: "destructive",
      });
      return;
    }
    
    setIsAnalyzing(true);
    fetchFromIntegrationMutation.mutate(parseInt(selectedIntegration));
    setAddSourceDialogOpen(false);
    setSelectedIntegration(null);
  };

  // API Bağlantısı ekle
  const addConnectionMutation = useMutation({
    mutationFn: async (connectionData: ApiConnectionFormValues) => {
      const res = await apiRequest("POST", `/api/projects/${projectId}/connections`, {
        ...connectionData,
        projectId,
        createdBy: user?.id
      });
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/connections`] });
      toast({
        title: "Bağlantı eklendi",
        description: "API Bağlantısı başarıyla eklendi.",
      });
      setAddConnectionDialogOpen(false);
      connectionForm.reset();
    },
    onError: (error: Error) => {
      toast({
        title: "Bağlantı eklenemedi",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // API Bağlantı formunu gönder
  const onSubmitConnection = (data: ApiConnectionFormValues) => {
    addConnectionMutation.mutate(data);
  };

  // Kullanıcının bu projeye erişim yetkisi yoksa
  const isLoading = projectLoading || documentsLoading || usersLoading || connectionsLoading;
  const canManageUsers = project && hasProjectAccess(projectId, PROJECT_ROLES.OWNER);
  const canAddDocuments = project && hasProjectAccess(projectId, PROJECT_ROLES.EDITOR);
  const canManageIntegrations = project && hasProjectAccess(projectId, PROJECT_ROLES.OWNER);

  if (project?.error) {
    return (
      <div className="min-h-screen bg-neutral-50">
        <Header />
        <div className="container mx-auto p-4 pt-8 text-center">
          <h1 className="text-2xl font-bold mb-4">Proje Bulunamadı</h1>
          <p className="text-neutral-600 mb-6">İstediğiniz proje bulunamadı veya erişim izniniz yok.</p>
          <Button onClick={() => navigate("/projects")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Projelere Dön
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-neutral-50">
      <Header />
      
      <main className="container mx-auto p-4 pt-8">
        {isLoading ? (
          <div className="flex justify-center items-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <>
            <div className="flex flex-col md:flex-row justify-between items-start gap-4 mb-6">
              <div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate("/projects")}
                  className="mb-4"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Projelere Dön
                </Button>
                
                <div className="flex items-center gap-2">
                  <h1 className="text-3xl font-bold">{project.name}</h1>
                  <ProjectStatusBadge status={project.status} />
                </div>
                <p className="text-neutral-600 mt-1">{project.description}</p>
                
                <div className="flex flex-wrap gap-4 mt-4">
                  <div className="flex items-center gap-2 text-sm text-neutral-600">
                    <Calendar className="h-4 w-4" />
                    <span>Oluşturulma: {new Date(project.createdAt).toLocaleDateString('tr-TR')}</span>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm text-neutral-600">
                    <User className="h-4 w-4" />
                    <span>Oluşturan: {project.creator?.name || project.creator?.username || "Bilinmiyor"}</span>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm text-neutral-600">
                    <FileSymlink className="h-4 w-4" />
                    <span>Kaynak Sayısı: {documents.length}</span>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm text-neutral-600">
                    <Users className="h-4 w-4" />
                    <span>Kullanıcı Sayısı: {projectUsers.length}</span>
                  </div>
                </div>
              </div>
              
              <div className="flex gap-2 self-start">
                {canAddDocuments && (
                  <Dialog open={addSourceDialogOpen} onOpenChange={setAddSourceDialogOpen}>
                    <DialogTrigger asChild>
                      <Button>
                        <FileType className="mr-2 h-4 w-4" />
                        Kaynak Ekle
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-3xl">
                      <DialogHeader>
                        <DialogTitle>Yeni Kaynak Ekle</DialogTitle>
                        <DialogDescription>
                          Projeye manuel olarak dosya yükleyin veya entegrasyonlar üzerinden kaynak ekleyin.
                        </DialogDescription>
                      </DialogHeader>
                      
                      <Tabs value={sourceTab} onValueChange={setSourceTab} className="w-full mt-4">
                        <TabsList className="grid w-full grid-cols-2">
                          <TabsTrigger value="upload" className="flex items-center gap-2">
                            <Upload className="h-4 w-4" />
                            Dosya Yükle
                          </TabsTrigger>
                          <TabsTrigger value="integration" className="flex items-center gap-2">
                            <Globe className="h-4 w-4" />
                            Entegrasyonlardan Al
                          </TabsTrigger>
                        </TabsList>
                        
                        <TabsContent value="upload" className="mt-4">
                          <div className="space-y-4">
                            <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-md p-8 bg-gray-50">
                              <Upload className="h-8 w-8 text-neutral-400 mb-2" />
                              <p className="text-sm text-neutral-600 mb-4">
                                Analiz edilecek dosyaları sürükleyip bırakın veya dosya seçin
                              </p>
                              <Input
                                type="file"
                                id="file-upload"
                                multiple
                                onChange={handleFileSelect}
                                className="max-w-sm"
                              />
                              {selectedFiles.length > 0 && (
                                <div className="mt-4 text-sm">
                                  <p className="font-medium">{selectedFiles.length} dosya seçildi:</p>
                                  <ul className="list-disc list-inside mt-2">
                                    {selectedFiles.map((file, index) => (
                                      <li key={index} className="text-neutral-600">{file.name} ({Math.round(file.size / 1024)} KB)</li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </div>
                            
                            <div className="bg-neutral-50 p-4 rounded-md border text-sm">
                              <h4 className="font-medium mb-2 flex items-center gap-2">
                                <CheckCircle className="h-4 w-4 text-green-600" />
                                Otomatik İş Akışı
                              </h4>
                              <p className="text-neutral-600">
                                Dosyalar yüklendikten sonra, sistem otomatik olarak şu işlemleri gerçekleştirecektir:
                              </p>
                              <ol className="list-decimal list-inside mt-2 space-y-1 text-neutral-600">
                                <li>Doküman içeriği analiz edilecek</li>
                                <li>Gereksinim ve bileşenler çıkarılacak</li>
                                <li>Test senaryoları oluşturulacak</li>
                              </ol>
                            </div>
                          </div>
                          
                          <div className="flex justify-end mt-4">
                            <Button
                              type="button"
                              onClick={handleUploadFiles}
                              disabled={selectedFiles.length === 0 || isAnalyzing}
                            >
                              {isAnalyzing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                              Dosyaları Yükle ve Analize Başla
                            </Button>
                          </div>
                        </TabsContent>
                        
                        <TabsContent value="integration" className="mt-4">
                          {apiConnections.length === 0 ? (
                            <div className="text-center p-8 border rounded-md">
                              <Database className="h-8 w-8 text-neutral-400 mx-auto mb-4" />
                              <h3 className="text-lg font-medium mb-2">Entegrasyon Bulunamadı</h3>
                              <p className="text-neutral-500 mb-4">
                                Bu projeye henüz entegrasyon eklenmemiş. Önce entegrasyon ekleyin.
                              </p>
                              {canManageIntegrations && (
                                <Button onClick={() => {
                                  setAddSourceDialogOpen(false);
                                  setAddConnectionDialogOpen(true);
                                  setActiveTab("integrations");
                                }}>
                                  <PlusCircle className="mr-2 h-4 w-4" />
                                  Entegrasyon Ekle
                                </Button>
                              )}
                            </div>
                          ) : (
                            <div className="space-y-4">
                              <div className="grid gap-4">
                                <div className="space-y-2">
                                  <Label htmlFor="integration">Entegrasyon Seçin</Label>
                                  <Select
                                    value={selectedIntegration || ""}
                                    onValueChange={(value) => setSelectedIntegration(value)}
                                  >
                                    <SelectTrigger>
                                      <SelectValue placeholder="Veri kaynağı seçin" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {apiConnections.map((connection: any) => (
                                        <SelectItem key={connection.id} value={connection.id.toString()}>
                                          {connection.name} ({connection.type === 'jira' ? 'Jira' : 
                                            connection.type === 'testrail' ? 'TestRail' :
                                            connection.type === 'alm' ? 'ALM/Quality Center' : 
                                            connection.type === 'visium' ? 'Visium Manage' : 
                                            connection.type})
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                              </div>
                              
                              <div className="bg-neutral-50 p-4 rounded-md border text-sm">
                                <h4 className="font-medium mb-2 flex items-center gap-2">
                                  <CheckCircle className="h-4 w-4 text-green-600" />
                                  Entegrasyon Veri Akışı
                                </h4>
                                <p className="text-neutral-600">
                                  Seçilen entegrasyondan veriler çekildiğinde, sistem otomatik olarak şu işlemleri gerçekleştirecektir:
                                </p>
                                <ol className="list-decimal list-inside mt-2 space-y-1 text-neutral-600">
                                  <li>Dış sistemdeki veriler toplanacak</li>
                                  <li>Veriler içe aktarılacak ve analiz edilecek</li>
                                  <li>Test senaryoları oluşturulacak</li>
                                </ol>
                              </div>
                              
                              <div className="flex justify-end mt-4">
                                <Button
                                  type="button"
                                  onClick={handleFetchFromIntegration}
                                  disabled={!selectedIntegration || isAnalyzing}
                                >
                                  {isAnalyzing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                  Verileri Al ve Analize Başla
                                </Button>
                              </div>
                            </div>
                          )}
                        </TabsContent>
                      </Tabs>
                    </DialogContent>
                  </Dialog>
                )}
                
                {canManageUsers && (
                  <Dialog open={addUserDialogOpen} onOpenChange={setAddUserDialogOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline">
                        <UserPlus className="mr-2 h-4 w-4" />
                        Kullanıcı Ekle
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Kullanıcı Ekle</DialogTitle>
                        <DialogDescription>
                          Projeye yeni bir kullanıcı ekleyin ve erişim rolünü belirleyin.
                        </DialogDescription>
                      </DialogHeader>
                      
                      <form onSubmit={handleAddUser}>
                        <div className="space-y-4 py-4">
                          <div className="space-y-2">
                            <Label htmlFor="username">Kullanıcı Adı</Label>
                            <Input
                              id="username"
                              name="username"
                              placeholder="Kullanıcı adını girin"
                              value={newUser.username}
                              onChange={handleInputChange}
                              required
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="role">Kullanıcı Rolü</Label>
                            <select
                              id="role"
                              name="role"
                              className="w-full rounded-md border border-input px-3 py-2 text-sm"
                              value={newUser.role}
                              onChange={handleInputChange}
                              required
                            >
                              <option value={PROJECT_ROLES.VIEWER}>Görüntüleyici (Sadece okuma)</option>
                              <option value={PROJECT_ROLES.EDITOR}>Düzenleyici (Belge ekleyebilir)</option>
                              <option value={PROJECT_ROLES.OWNER}>Sahip (Tam yetki)</option>
                            </select>
                          </div>
                        </div>
                        
                        <DialogFooter>
                          <Button 
                            type="submit" 
                            disabled={addUserMutation.isPending}
                          >
                            {addUserMutation.isPending && (
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            )}
                            Kullanıcıyı Ekle
                          </Button>
                        </DialogFooter>
                      </form>
                    </DialogContent>
                  </Dialog>
                )}
              </div>
            </div>
            
            <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-6">
                <TabsTrigger value="overview" className="flex items-center gap-2">
                  <Book className="h-4 w-4" />
                  Genel Bakış
                </TabsTrigger>
                <TabsTrigger value="documents" className="flex items-center gap-2">
                  <FileType className="h-4 w-4" />
                  Kaynaklar
                </TabsTrigger>
                <TabsTrigger value="test-scenarios" className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Test Senaryoları
                </TabsTrigger>
                <TabsTrigger value="integrations" className="flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-plug-2">
                    <path d="M9 2v6"></path>
                    <path d="M15 2v6"></path>
                    <path d="M12 17v5"></path>
                    <path d="M5 8h14"></path>
                    <path d="M6 11V8h12v3a6 6 0 1 1-12 0v0Z"></path>
                  </svg>
                  Entegrasyonlar
                </TabsTrigger>
                <TabsTrigger value="users" className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Kullanıcılar
                </TabsTrigger>
                {canManageUsers && (
                  <TabsTrigger value="settings" className="flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    Ayarlar
                  </TabsTrigger>
                )}
              </TabsList>
              
              <TabsContent value="overview">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Proje Bilgileri</CardTitle>
                      <CardDescription>
                        Proje ile ilgili temel bilgiler ve istatistikler
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <dl className="space-y-4">
                        <div>
                          <dt className="text-sm font-medium text-neutral-500">Proje Adı</dt>
                          <dd className="mt-1">{project.name}</dd>
                        </div>
                        
                        <div>
                          <dt className="text-sm font-medium text-neutral-500">Açıklama</dt>
                          <dd className="mt-1">{project.description || "Açıklama yok"}</dd>
                        </div>
                        
                        <div>
                          <dt className="text-sm font-medium text-neutral-500">Durum</dt>
                          <dd className="mt-1">
                            <ProjectStatusBadge status={project.status} />
                          </dd>
                        </div>
                        
                        <div>
                          <dt className="text-sm font-medium text-neutral-500">Oluşturulma Tarihi</dt>
                          <dd className="mt-1">{new Date(project.createdAt).toLocaleDateString('tr-TR')}</dd>
                        </div>
                        
                        <div>
                          <dt className="text-sm font-medium text-neutral-500">Son Güncelleme</dt>
                          <dd className="mt-1">{new Date(project.updatedAt).toLocaleDateString('tr-TR')}</dd>
                        </div>
                      </dl>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle>Özet</CardTitle>
                      <CardDescription>
                        Projedeki doküman ve test senaryoları özeti
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-sm font-medium text-neutral-500">Kaynaklar</h3>
                          <p className="mt-1 text-2xl font-semibold">{documents.length}</p>
                          <p className="text-sm text-neutral-500">
                            {documents.length === 0 
                              ? "Henüz hiç kaynak eklenmemiş" 
                              : `Toplam ${documents.length} kaynak, ${documents.filter((d: any) => d.analyzedAt).length} analiz edilmiş`}
                          </p>
                        </div>
                        
                        <div>
                          <h3 className="text-sm font-medium text-neutral-500">Kullanıcılar</h3>
                          <p className="mt-1 text-2xl font-semibold">{projectUsers.length}</p>
                          <div className="text-sm text-neutral-500">
                            <p>
                              {projectUsers.filter((u: any) => u.role === PROJECT_ROLES.OWNER).length} sahip, 
                              {" "}{projectUsers.filter((u: any) => u.role === PROJECT_ROLES.EDITOR).length} düzenleyici, 
                              {" "}{projectUsers.filter((u: any) => u.role === PROJECT_ROLES.VIEWER).length} görüntüleyici
                            </p>
                          </div>
                        </div>
                        
                        <div>
                          <h3 className="text-sm font-medium text-neutral-500">Analiz Durumu</h3>
                          <div className="mt-2 flex items-center gap-4">
                            <div className="flex-1 bg-neutral-100 h-2 rounded-full overflow-hidden">
                              <div 
                                className="bg-primary h-full" 
                                style={{ 
                                  width: `${documents.length ? 
                                    (documents.filter((d: any) => d.analyzedAt).length / documents.length) * 100 : 0}%` 
                                }}
                              />
                            </div>
                            <span className="text-sm">
                              {documents.length ? 
                                Math.round((documents.filter((d: any) => d.analyzedAt).length / documents.length) * 100) : 0}%
                            </span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
              
              <TabsContent value="documents">
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <CardTitle>Kaynaklar</CardTitle>
                      {canAddDocuments && (
                        <Button size="sm" onClick={() => setAddSourceDialogOpen(true)}>
                          <FileType className="mr-2 h-4 w-4" />
                          Kaynak Ekle
                        </Button>
                      )}
                    </div>
                    <CardDescription>
                      Bu projeye ait tüm kaynaklar ve analiz durumları
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {documents.length === 0 ? (
                      <div className="text-center p-8">
                        <h3 className="text-lg font-medium mb-2">Henüz kaynak yok</h3>
                        <p className="text-neutral-500 mb-4">
                          Bu projeye henüz hiç kaynak eklenmemiş.
                        </p>
                        {canAddDocuments && (
                          <Button onClick={() => setAddSourceDialogOpen(true)}>
                            <FileType className="mr-2 h-4 w-4" />
                            Kaynak Ekle
                          </Button>
                        )}
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <table className="w-full border-collapse">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left py-3 px-4">Kaynak Adı</th>
                              <th className="text-left py-3 px-4">Türü</th>
                              <th className="text-left py-3 px-4">Oluşturulma</th>
                              <th className="text-left py-3 px-4">Analiz Durumu</th>
                              <th className="text-left py-3 px-4">İşlemler</th>
                            </tr>
                          </thead>
                          <tbody>
                            {documents.map((doc: any) => (
                              <tr key={doc.id} className="border-b hover:bg-neutral-50">
                                <td className="py-3 px-4">{doc.name}</td>
                                <td className="py-3 px-4 capitalize">{doc.type}</td>
                                <td className="py-3 px-4">{new Date(doc.createdAt).toLocaleDateString('tr-TR')}</td>
                                <td className="py-3 px-4">
                                  {doc.analyzedAt ? (
                                    <div className="flex items-center gap-2 text-green-600">
                                      <CheckCircle2 className="h-4 w-4" />
                                      <span>Analiz Edildi</span>
                                    </div>
                                  ) : (
                                    <div className="flex items-center gap-2 text-amber-600">
                                      <XCircle className="h-4 w-4" />
                                      <span>Analiz Edilmedi</span>
                                    </div>
                                  )}
                                </td>
                                <td className="py-3 px-4">
                                  <Button variant="ghost" size="sm">
                                    Görüntüle
                                  </Button>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="test-scenarios">
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <CardTitle>Test Senaryoları</CardTitle>
                      <Button size="sm" variant="outline">
                        <FileDown className="mr-2 h-4 w-4" />
                        Dışa Aktar
                      </Button>
                    </div>
                    <CardDescription>
                      Bu projede oluşturulan tüm test senaryoları
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center p-8">
                      <h3 className="text-lg font-medium mb-2">Henüz test senaryosu oluşturulmamış</h3>
                      <p className="text-neutral-500 mb-4">
                        Bu projeye ait test senaryoları oluşturmak için önce bir kaynak ekleyip analiz edin.
                      </p>
                      <Button onClick={() => setActiveTab("documents")}>
                        <FileType className="mr-2 h-4 w-4" />
                        Kaynaklar Sayfasına Git
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="integrations">
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <CardTitle>Entegrasyonlar</CardTitle>
                      {canManageIntegrations && (
                        <Dialog open={addConnectionDialogOpen} onOpenChange={setAddConnectionDialogOpen}>
                          <DialogTrigger asChild>
                            <Button size="sm">
                              <PlusCircle className="mr-2 h-4 w-4" />
                              Yeni Entegrasyon
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-lg">
                            <DialogHeader>
                              <DialogTitle>Yeni API Bağlantısı Ekle</DialogTitle>
                              <DialogDescription>
                                Dış sistemlere bağlanmak için API bilgilerini girin.
                              </DialogDescription>
                            </DialogHeader>
                            
                            <Form {...connectionForm}>
                              <form onSubmit={connectionForm.handleSubmit(onSubmitConnection)} className="space-y-6">
                                <FormField
                                  control={connectionForm.control}
                                  name="name"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Bağlantı Adı</FormLabel>
                                      <FormControl>
                                        <Input placeholder="Bağlantı adını girin" {...field} />
                                      </FormControl>
                                      <FormDescription>
                                        Bu bağlantıyı tanımlayan bir isim
                                      </FormDescription>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                                
                                <FormField
                                  control={connectionForm.control}
                                  name="type"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Bağlantı Türü</FormLabel>
                                      <Select 
                                        onValueChange={field.onChange} 
                                        defaultValue={field.value}
                                      >
                                        <FormControl>
                                          <SelectTrigger>
                                            <SelectValue placeholder="Bağlantı türünü seçin" />
                                          </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                          <SelectItem value="jira">Jira</SelectItem>
                                          <SelectItem value="testrail">TestRail</SelectItem>
                                          <SelectItem value="alm">ALM/Quality Center</SelectItem>
                                          <SelectItem value="visium">Visium Manage</SelectItem>
                                        </SelectContent>
                                      </Select>
                                      <FormDescription>
                                        Bağlanılacak sistemin türü
                                      </FormDescription>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                                
                                <FormField
                                  control={connectionForm.control}
                                  name="baseUrl"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>API URL</FormLabel>
                                      <FormControl>
                                        <Input placeholder="https://example.atlassian.net" {...field} />
                                      </FormControl>
                                      <FormDescription>
                                        Sistemin temel API URL'i
                                      </FormDescription>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                                
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <FormField
                                    control={connectionForm.control}
                                    name="username"
                                    render={({ field }) => (
                                      <FormItem>
                                        <FormLabel>Kullanıcı Adı (Opsiyonel)</FormLabel>
                                        <FormControl>
                                          <Input placeholder="API kullanıcı adı" {...field} />
                                        </FormControl>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />
                                  
                                  <FormField
                                    control={connectionForm.control}
                                    name="apiKey"
                                    render={({ field }) => (
                                      <FormItem>
                                        <FormLabel>API Anahtarı (Opsiyonel)</FormLabel>
                                        <FormControl>
                                          <Input type="password" placeholder="api-key-xxx" {...field} />
                                        </FormControl>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />
                                  
                                  <FormField
                                    control={connectionForm.control}
                                    name="token"
                                    render={({ field }) => (
                                      <FormItem>
                                        <FormLabel>Token (Opsiyonel)</FormLabel>
                                        <FormControl>
                                          <Input type="password" placeholder="token-xxx" {...field} />
                                        </FormControl>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />
                                  
                                  <FormField
                                    control={connectionForm.control}
                                    name="settings"
                                    render={({ field }) => (
                                      <FormItem>
                                        <FormLabel>Ek Ayarlar (Opsiyonel)</FormLabel>
                                        <FormControl>
                                          <Textarea 
                                            placeholder='{"property": "value"}'
                                            {...field}
                                          />
                                        </FormControl>
                                        <FormDescription className="text-xs">
                                          JSON formatında
                                        </FormDescription>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />
                                </div>
                                
                                <DialogFooter>
                                  <Button type="submit" disabled={addConnectionMutation.isPending}>
                                    {addConnectionMutation.isPending && (
                                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    )}
                                    Bağlantıyı Ekle
                                  </Button>
                                </DialogFooter>
                              </form>
                            </Form>
                          </DialogContent>
                        </Dialog>
                      )}
                    </div>
                    <CardDescription>
                      Dış sistemlerle bağlantılar ve ayarlar
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {/* Mevcut bağlantılar */}
                    {apiConnections && apiConnections.length > 0 ? (
                      <div className="mb-6">
                        <h3 className="text-lg font-medium mb-4">Mevcut Bağlantılar</h3>
                        <div className="overflow-x-auto">
                          <table className="w-full border-collapse">
                            <thead>
                              <tr className="border-b">
                                <th className="text-left py-3 px-4">Bağlantı Adı</th>
                                <th className="text-left py-3 px-4">Tür</th>
                                <th className="text-left py-3 px-4">Durum</th>
                                <th className="text-left py-3 px-4">Son Senkronizasyon</th>
                                {canManageIntegrations && <th className="text-left py-3 px-4">İşlemler</th>}
                              </tr>
                            </thead>
                            <tbody>
                              {apiConnections.map((connection: any) => (
                                <tr key={connection.id} className="border-b hover:bg-neutral-50">
                                  <td className="py-3 px-4">{connection.name}</td>
                                  <td className="py-3 px-4">
                                    <div className="flex items-center gap-2">
                                      {connection.type === "jira" && (
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-500">
                                          <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z" />
                                        </svg>
                                      )}
                                      {connection.type === "testrail" && (
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-green-500">
                                          <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z" />
                                        </svg>
                                      )}
                                      {connection.type === "alm" && (
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-orange-500">
                                          <rect width="18" height="18" x="3" y="3" rx="2" />
                                          <path d="M9 9h6v6H9z" />
                                        </svg>
                                      )}
                                      {connection.type === "visium" && (
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-purple-500">
                                          <circle cx="12" cy="12" r="10" />
                                          <path d="m4.9 4.9 14.2 14.2" />
                                        </svg>
                                      )}
                                      <span className="capitalize">{connection.type}</span>
                                    </div>
                                  </td>
                                  <td className="py-3 px-4">
                                    {connection.status === "active" ? (
                                      <Badge className="bg-green-100 text-green-800">Aktif</Badge>
                                    ) : (
                                      <Badge className="bg-neutral-100 text-neutral-800">Pasif</Badge>
                                    )}
                                  </td>
                                  <td className="py-3 px-4">
                                    {connection.lastSyncAt ? 
                                      new Date(connection.lastSyncAt).toLocaleDateString('tr-TR') : 
                                      <span className="text-neutral-500">Hiç senkronize edilmedi</span>
                                    }
                                  </td>
                                  {canManageIntegrations && (
                                    <td className="py-3 px-4">
                                      <div className="flex gap-2">
                                        <Button 
                                          variant="ghost" 
                                          size="sm"
                                          className="text-blue-500 hover:text-blue-600 hover:bg-blue-50"
                                          onClick={() => {
                                            // Düzenleme formu hazırlanacak
                                            connectionForm.reset({
                                              name: connection.name,
                                              type: connection.type,
                                              baseUrl: connection.baseUrl,
                                              apiKey: connection.apiKey || "",
                                              username: connection.username || "",
                                              token: connection.token || "",
                                              settings: connection.settings ? JSON.stringify(connection.settings) : ""
                                            });
                                            setAddConnectionDialogOpen(true);
                                          }}
                                        >
                                          Düzenle
                                        </Button>
                                        <Button 
                                          variant="ghost" 
                                          size="sm"
                                          className="text-red-500 hover:text-red-600 hover:bg-red-50"
                                          onClick={() => {
                                            // Silme işlemi yapılacak
                                            if (window.confirm("Bu bağlantıyı silmek istediğinizden emin misiniz?")) {
                                              // Silme mutasyonu eklenecek
                                              console.log("Bağlantı silme:", connection.id);
                                            }
                                          }}
                                        >
                                          Sil
                                        </Button>
                                      </div>
                                    </td>
                                  )}
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    ) : null}
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                      <h3 className="text-lg font-medium mb-2 col-span-full">Entegrasyon Ekle</h3>
                      <div className="border rounded-lg p-4">
                        <div className="flex justify-between items-center mb-4">
                          <div className="flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-500">
                              <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z" />
                            </svg>
                            <h3 className="text-lg font-medium">Jira</h3>
                          </div>
                          <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">Bağlantı Yok</span>
                        </div>
                        <p className="text-sm text-neutral-500 mb-4">Jira ile test senaryolarını senkronize edin ve iş akışlarını otomatikleştirin.</p>
                        {canManageIntegrations && (
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="w-full"
                            onClick={() => {
                              connectionForm.reset({
                                name: "Jira Bağlantısı",
                                type: "jira",
                                baseUrl: "",
                                apiKey: "",
                                username: "",
                                token: "",
                                settings: ""
                              });
                              setAddConnectionDialogOpen(true);
                            }}
                          >
                            <PlusCircle className="mr-2 h-4 w-4" />
                            Bağlantı Ekle
                          </Button>
                        )}
                      </div>
                      
                      <div className="border rounded-lg p-4">
                        <div className="flex justify-between items-center mb-4">
                          <div className="flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-green-500">
                              <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z" />
                            </svg>
                            <h3 className="text-lg font-medium">TestRail</h3>
                          </div>
                          <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">Bağlantı Yok</span>
                        </div>
                        <p className="text-sm text-neutral-500 mb-4">Test senaryolarını ve sonuçları TestRail'e aktarın.</p>
                        {canManageIntegrations && (
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="w-full"
                            onClick={() => {
                              connectionForm.reset({
                                name: "TestRail Bağlantısı",
                                type: "testrail",
                                baseUrl: "",
                                apiKey: "",
                                username: "",
                                token: "",
                                settings: ""
                              });
                              setAddConnectionDialogOpen(true);
                            }}
                          >
                            <PlusCircle className="mr-2 h-4 w-4" />
                            Bağlantı Ekle
                          </Button>
                        )}
                      </div>
                      
                      <div className="border rounded-lg p-4">
                        <div className="flex justify-between items-center mb-4">
                          <div className="flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-orange-500">
                              <rect width="18" height="18" x="3" y="3" rx="2" />
                              <path d="M9 9h6v6H9z" />
                            </svg>
                            <h3 className="text-lg font-medium">ALM</h3>
                          </div>
                          <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">Bağlantı Yok</span>
                        </div>
                        <p className="text-sm text-neutral-500 mb-4">HP ALM/Quality Center entegrasyonu ile test yönetimi.</p>
                        {canManageIntegrations && (
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="w-full"
                            onClick={() => {
                              connectionForm.reset({
                                name: "ALM Bağlantısı",
                                type: "alm",
                                baseUrl: "",
                                apiKey: "",
                                username: "",
                                token: "",
                                settings: ""
                              });
                              setAddConnectionDialogOpen(true);
                            }}
                          >
                            <PlusCircle className="mr-2 h-4 w-4" />
                            Bağlantı Ekle
                          </Button>
                        )}
                      </div>
                      
                      <div className="border rounded-lg p-4">
                        <div className="flex justify-between items-center mb-4">
                          <div className="flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-purple-500">
                              <circle cx="12" cy="12" r="10" />
                              <path d="m4.9 4.9 14.2 14.2" />
                            </svg>
                            <h3 className="text-lg font-medium">Visium Manage</h3>
                          </div>
                          <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">Bağlantı Yok</span>
                        </div>
                        <p className="text-sm text-neutral-500 mb-4">Test otomasyonu ve yönetimi için Visium Manage entegrasyonu.</p>
                        {canManageIntegrations && (
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="w-full"
                            onClick={() => {
                              connectionForm.reset({
                                name: "Visium Bağlantısı",
                                type: "visium",
                                baseUrl: "",
                                apiKey: "",
                                username: "",
                                token: "",
                                settings: ""
                              });
                              setAddConnectionDialogOpen(true);
                            }}
                          >
                            <PlusCircle className="mr-2 h-4 w-4" />
                            Bağlantı Ekle
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="users">
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <CardTitle>Kullanıcılar</CardTitle>
                      {canManageUsers && (
                        <Button size="sm" onClick={() => setAddUserDialogOpen(true)}>
                          <UserPlus className="mr-2 h-4 w-4" />
                          Kullanıcı Ekle
                        </Button>
                      )}
                    </div>
                    <CardDescription>
                      Bu projeye erişimi olan kullanıcılar ve rolleri
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {projectUsers.length === 0 ? (
                      <div className="text-center p-8">
                        <h3 className="text-lg font-medium mb-2">Hiç kullanıcı yok</h3>
                        <p className="text-neutral-500">
                          Bu projeye henüz hiç kullanıcı eklenmemiş.
                        </p>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <table className="w-full border-collapse">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left py-3 px-4">Kullanıcı Adı</th>
                              <th className="text-left py-3 px-4">İsim</th>
                              <th className="text-left py-3 px-4">Rol</th>
                              <th className="text-left py-3 px-4">Eklenme Tarihi</th>
                              {canManageUsers && <th className="text-left py-3 px-4">İşlemler</th>}
                            </tr>
                          </thead>
                          <tbody>
                            {projectUsers.map((projectUser: any) => (
                              <tr key={projectUser.userId} className="border-b hover:bg-neutral-50">
                                <td className="py-3 px-4">{projectUser.user?.username}</td>
                                <td className="py-3 px-4">{projectUser.user?.name || '-'}</td>
                                <td className="py-3 px-4">
                                  <RoleBadge role={projectUser.role} />
                                </td>
                                <td className="py-3 px-4">{new Date(projectUser.createdAt).toLocaleDateString('tr-TR')}</td>
                                {canManageUsers && (
                                  <td className="py-3 px-4">
                                    <div className="flex gap-2">
                                      {projectUser.userId !== user?.id && (
                                        <>
                                          <select
                                            value={projectUser.role}
                                            onChange={(e) => handleChangeUserRole(projectUser.userId, e.target.value)}
                                            className="w-auto rounded-md border border-input px-2 py-1 text-sm"
                                          >
                                            <option value={PROJECT_ROLES.VIEWER}>Görüntüleyici</option>
                                            <option value={PROJECT_ROLES.EDITOR}>Düzenleyici</option>
                                            <option value={PROJECT_ROLES.OWNER}>Sahip</option>
                                          </select>
                                          
                                          <Button 
                                            variant="ghost" 
                                            size="sm" 
                                            onClick={() => handleRemoveUser(projectUser.userId)}
                                            className="text-red-500 hover:text-red-600 hover:bg-red-50"
                                          >
                                            Kaldır
                                          </Button>
                                        </>
                                      )}
                                    </div>
                                  </td>
                                )}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
              
              {canManageUsers && (
                <TabsContent value="settings">
                  <Card>
                    <CardHeader>
                      <CardTitle>Proje Ayarları</CardTitle>
                      <CardDescription>
                        Proje ayarlarını yapılandırın ve yönetin
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        <div className="space-y-2">
                          <Label htmlFor="projectName">Proje Adı</Label>
                          <Input
                            id="projectName"
                            defaultValue={project.name}
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="projectDescription">Proje Açıklaması</Label>
                          <textarea
                            id="projectDescription"
                            className="w-full min-h-[100px] rounded-md border border-input px-3 py-2 text-sm"
                            defaultValue={project.description}
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="projectStatus">Proje Durumu</Label>
                          <select
                            id="projectStatus"
                            className="w-full rounded-md border border-input px-3 py-2 text-sm"
                            defaultValue={project.status}
                          >
                            <option value="active">Aktif</option>
                            <option value="planning">Planlama</option>
                            <option value="completed">Tamamlandı</option>
                            <option value="archived">Arşivlendi</option>
                          </select>
                        </div>
                        
                        <Button>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Değişiklikleri Kaydet
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              )}
            </Tabs>
          </>
        )}
      </main>
    </div>
  );
}

// Proje durum etiketi bileşeni
function ProjectStatusBadge({ status }: { status: string }) {
  const statusColors = {
    active: "bg-green-100 text-green-800",
    planning: "bg-blue-100 text-blue-800",
    completed: "bg-purple-100 text-purple-800",
    archived: "bg-gray-100 text-gray-800"
  };
  
  const statusLabels = {
    active: "Aktif",
    planning: "Planlama",
    completed: "Tamamlandı",
    archived: "Arşivlendi"
  };
  
  // Proje durumunu Türkçe olarak göster
  const statusClass = statusColors[status as keyof typeof statusColors] || statusColors.active;
  const statusLabel = statusLabels[status as keyof typeof statusLabels] || statusLabels.active;
  
  return <Badge className={statusClass}>{statusLabel}</Badge>;
}

// Kullanıcı rol etiketi bileşeni
function RoleBadge({ role }: { role: string }) {
  const roleColors = {
    [PROJECT_ROLES.OWNER]: "bg-purple-100 text-purple-800",
    [PROJECT_ROLES.EDITOR]: "bg-blue-100 text-blue-800",
    [PROJECT_ROLES.VIEWER]: "bg-neutral-100 text-neutral-800"
  };
  
  const roleLabels = {
    [PROJECT_ROLES.OWNER]: "Sahip",
    [PROJECT_ROLES.EDITOR]: "Düzenleyici",
    [PROJECT_ROLES.VIEWER]: "Görüntüleyici"
  };
  
  // Rolü Türkçe olarak göster
  const roleClass = roleColors[role as keyof typeof roleColors] || roleColors[PROJECT_ROLES.VIEWER];
  const roleLabel = roleLabels[role as keyof typeof roleLabels] || roleLabels[PROJECT_ROLES.VIEWER];
  
  return <Badge className={roleClass}>{roleLabel}</Badge>;
}