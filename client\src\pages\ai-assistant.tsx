import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { getTimeAgo } from "@/lib/utils";
import { sendAIAssistantMessage } from "@/lib/api";

interface Message {
  id: string;
  sender: "user" | "ai";
  text: string;
  timestamp: Date;
}

const AIAssistant = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "welcome",
      sender: "ai",
      text: "Merhaba! Ben sürekli öğrenen bir doküman analiz asistanınızım. Dokümanlarınız, gereksinimleriniz veya test senaryoları hakkında sorularınızı yanıtlayabilirim. Her sohbet benim bilgi dağ<PERSON>ığımı geliştiriyor! Size nasıl yardımcı olabilirim?",
      timestamp: new Date(),
    },
    {
      id: "suggestions",
      sender: "ai",
      text: "<PERSON>rnek sorular:\n- Bu dokümandaki temel gereksinimleri özetleyebilir misin?\n- Test senaryolarının kapsama oranını artırmak için ne yapabilirim?\n- Bu API entegrasyonu için başka ne tür testler yapılmalı?",
      timestamp: new Date(),
    },
  ]);
  const [userInput, setUserInput] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  // Get active documents
  const { data: documents } = useQuery({
    queryKey: ["/api/documents"],
  });

  // AI chat mutation
  const aiChatMutation = useMutation({
    mutationFn: async (message: string) => {
      return sendAIAssistantMessage(message, documents || []);
    },
    onError: (error) => {
      console.error("Error getting AI response:", error);
      toast({
        title: "Hata",
        description: "AI yanıtı alınamadı. Lütfen daha sonra tekrar deneyin.",
        variant: "destructive",
      });
    }
  });

  // Handle user message submission
  const handleSendMessage = async () => {
    if (!userInput.trim()) return;
    
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      sender: "user",
      text: userInput,
      timestamp: new Date(),
    };
    
    setMessages((prev) => [...prev, userMessage]);
    const currentInput = userInput;
    setUserInput("");
    setIsProcessing(true);
    
    try {
      const response = await aiChatMutation.mutateAsync(currentInput);
      
      const aiResponse: Message = {
        id: `ai-${Date.now()}`,
        sender: "ai",
        text: response.response,
        timestamp: new Date(),
      };
      
      setMessages((prev) => [...prev, aiResponse]);
    } catch (error) {
      // Error is handled in mutation's onError
    } finally {
      setIsProcessing(false);
    }
  };

  // Auto-scroll to bottom of chat when new messages arrive
  useEffect(() => {
    const chatContainer = document.getElementById("chat-container");
    if (chatContainer) {
      chatContainer.scrollTop = chatContainer.scrollHeight;
    }
  }, [messages]);

  return (
    <div className="flex flex-col h-screen bg-neutral-50">
      <header className="bg-white border-b border-neutral-200 py-4 px-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center">
            <i className="fas fa-robot text-primary mr-2 text-xl"></i>
            <h1 className="text-xl font-medium text-neutral-800">AI Dokümantasyon Asistanı</h1>
          </div>
          <Button variant="outline" size="sm" asChild>
            <a href="/">
              <i className="fas fa-arrow-left mr-2"></i>
              Dashboard'a Dön
            </a>
          </Button>
        </div>
      </header>

      <main className="flex-1 overflow-hidden p-4 sm:p-6 max-w-5xl mx-auto w-full">
        <div className="bg-white rounded-lg shadow-sm border border-neutral-200 h-full flex flex-col">
          <div className="p-4 border-b border-neutral-200 flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-primary">
                <i className="fas fa-robot"></i>
              </div>
              <div className="ml-3">
                <h2 className="font-medium text-neutral-900">Doküman Asistanı</h2>
                <p className="text-xs text-neutral-500">GPT-4o ile desteklenmektedir</p>
              </div>
            </div>
            <div className="text-xs px-2 py-0.5 bg-green-100 text-green-800 rounded-full flex items-center">
              <i className="fas fa-circle text-[6px] mr-1"></i>
              <span>Aktif</span>
            </div>
          </div>

          <div 
            id="chat-container"
            className="flex-1 overflow-y-auto p-4 space-y-4"
          >
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === "user" ? "justify-end" : "justify-start"}`}
              >
                <div
                  className={`max-w-[80%] p-3 rounded-lg ${
                    message.sender === "user"
                      ? "bg-primary text-white rounded-tr-none"
                      : "bg-neutral-100 text-neutral-800 rounded-tl-none"
                  }`}
                >
                  <p className="whitespace-pre-line">{message.text}</p>
                  <div
                    className={`text-xs mt-1 ${
                      message.sender === "user" ? "text-primary-50" : "text-neutral-500"
                    }`}
                  >
                    {getTimeAgo(message.timestamp)}
                  </div>
                </div>
              </div>
            ))}
            
            {isProcessing && (
              <div className="flex justify-start">
                <div className="max-w-[80%] p-3 rounded-lg bg-neutral-100 text-neutral-800 rounded-tl-none">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 rounded-full bg-neutral-400 animate-pulse"></div>
                    <div className="w-2 h-2 rounded-full bg-neutral-400 animate-pulse" style={{ animationDelay: "0.2s" }}></div>
                    <div className="w-2 h-2 rounded-full bg-neutral-400 animate-pulse" style={{ animationDelay: "0.4s" }}></div>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="p-4 border-t border-neutral-200">
            <div className="flex space-x-2">
              <Input
                value={userInput}
                onChange={(e) => setUserInput(e.target.value)}
                placeholder="Doküman asistanına bir soru sorun..."
                className="flex-1"
                onKeyPress={(e) => {
                  if (e.key === "Enter" && !isProcessing) {
                    handleSendMessage();
                  }
                }}
              />
              <Button 
                onClick={handleSendMessage} 
                disabled={isProcessing || !userInput.trim()}
              >
                {isProcessing ? (
                  <i className="fas fa-spinner fa-spin"></i>
                ) : (
                  <i className="fas fa-paper-plane"></i>
                )}
              </Button>
            </div>
            <div className="mt-3 flex flex-wrap gap-2">
              <Button variant="outline" size="sm" className="text-xs h-auto py-1" onClick={() => setUserInput("Bu dokümandaki temel gereksinimleri özetleyebilir misin?")}>
                Gereksinimleri özetle
              </Button>
              <Button variant="outline" size="sm" className="text-xs h-auto py-1" onClick={() => setUserInput("Test senaryolarının kapsama oranını artırmak için ne yapabilirim?")}>
                Kapsama oranını artır
              </Button>
              <Button variant="outline" size="sm" className="text-xs h-auto py-1" onClick={() => setUserInput("Hangi API endpoint'leri entegrasyon testi gerektiriyor?")}>
                API entegrasyonları
              </Button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default AIAssistant;