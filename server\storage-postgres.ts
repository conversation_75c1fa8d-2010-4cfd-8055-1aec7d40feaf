import { and, eq, inArray } from 'drizzle-orm';
import { db, pool } from './db';
import { type IStorage } from './storage';
import {
  users, type User, type InsertUser,
  documents, type Document, type InsertDocument,
  components, type Component, type InsertComponent,
  requirements, type Requirement, type InsertRequirement,
  apiEndpoints, type ApiEndpoint, type InsertApiEndpoint,
  testScenarios, type TestScenario, type InsertTestScenario,
  aiAnalysis, type AiAnalysis, type InsertAiAnalysis,
  coverageValidation, type CoverageValidation, type InsertCoverageValidation,
  aiLearningMemory, type AiLearningMemory, type InsertAiLearningMemory,
  aiInteractionHistory, type AiInteractionHistory, type InsertAiInteractionHistory,
  projects, type Project, type InsertProject,
  userProjects, type UserProject, type InsertUserProject,
  permissions, type Permission, type InsertPermission,
  userPermissions,
  apiConnections, type ApiConnection, type InsertApiConnection
} from '../shared/schema';
import session from "express-session";
import connectPg from "connect-pg-simple";

const PostgresSessionStore = connectPg(session);

/**
 * PostgreSQL desteği ile veritabanı uygulaması
 */
export class PostgresStorage implements IStorage {
  sessionStore?: session.Store;

  constructor() {
    // Oturum verilerini PostgreSQL'de saklama
    this.sessionStore = new PostgresSessionStore({ 
      pool, 
      createTableIfMissing: true,
      tableName: 'session' 
    });
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.id, id));
    return result[0];
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.username, username));
    return result[0];
  }

  async createUser(user: InsertUser): Promise<User> {
    const result = await db.insert(users).values(user).returning();
    return result[0];
  }
  
  async getAllUsers(): Promise<User[]> {
    return await db.select().from(users);
  }
  
  async updateUser(id: number, data: Partial<User>): Promise<User | undefined> {
    const result = await db.update(users)
      .set(data)
      .where(eq(users.id, id))
      .returning();
    return result[0];
  }

  // Document operations
  async getDocuments(): Promise<Document[]> {
    try {
      // Tüm kolonları belirtmeden, özel sorgular kullanarak status alanını hariç tutuyoruz
      const result = await db.query.documents.findMany({
        columns: {
          id: true,
          name: true,
          type: true,
          content: true,
          originalContent: true,
          projectId: true,
          createdBy: true,
          analyzedAt: true,
          createdAt: true
        }
      });
      
      // Status alanını sonuçlara manuel olarak ekleyelim
      return result.map(doc => ({
        ...doc,
        status: "pending" as string | null // Default değer ekle
      }));
    } catch (error) {
      console.error("getDocuments sorgusu hatası:", error);
      throw error;
    }
  }
  
  async getDocumentsByUserId(userId: number): Promise<Document[]> {
    try {
      const result = await db.query.documents.findMany({
        columns: {
          id: true,
          name: true,
          type: true,
          content: true,
          originalContent: true,
          projectId: true,
          createdBy: true,
          analyzedAt: true,
          createdAt: true
        },
        where: eq(documents.createdBy, userId)
      });
      
      return result.map(doc => ({
        ...doc,
        status: "pending" as string | null // Default değer ekle
      }));
    } catch (error) {
      console.error(`getDocumentsByUserId(${userId}) sorgusu hatası:`, error);
      throw error;
    }
  }
  
  async getDocumentsByProjectId(projectId: number): Promise<Document[]> {
    try {
      const result = await db.query.documents.findMany({
        columns: {
          id: true,
          name: true,
          type: true,
          content: true,
          originalContent: true,
          projectId: true,
          createdBy: true,
          analyzedAt: true,
          createdAt: true
        },
        where: eq(documents.projectId, projectId)
      });
      
      return result.map(doc => ({
        ...doc,
        status: "pending" as string | null // Default değer ekle
      }));
    } catch (error) {
      console.error(`getDocumentsByProjectId(${projectId}) sorgusu hatası:`, error);
      throw error;
    }
  }

  async getDocument(id: number): Promise<Document | undefined> {
    try {
      const result = await db.query.documents.findFirst({
        columns: {
          id: true,
          name: true,
          type: true,
          content: true,
          originalContent: true,
          projectId: true,
          createdBy: true,
          analyzedAt: true,
          createdAt: true
        },
        where: eq(documents.id, id)
      });
      
      if (!result) return undefined;
      
      return {
        ...result,
        status: "pending" as string | null // Default değer ekle
      };
    } catch (error) {
      console.error(`getDocument(${id}) sorgusu hatası:`, error);
      throw error;
    }
  }

  async createDocument(document: InsertDocument): Promise<Document> {
    try {
      // Status alanını çıkararak SQL sorgusunu oluştur
      const { name, type, content, originalContent, projectId, createdBy } = document;
      
      // Sadece bilinen alanları ekle, status alanını atla
      const sqlQuery = `
        INSERT INTO documents (name, type, content, original_content, project_id, created_by, created_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING id, name, type, content, original_content as "originalContent", 
                 project_id as "projectId", created_by as "createdBy",
                 created_at as "createdAt", analyzed_at as "analyzedAt"
      `;
      
      // Burada db yerine pool nesnesini kullanıyoruz
      const result = await pool.query(sqlQuery, [
        name, 
        type, 
        content, 
        originalContent || null, 
        projectId || null, 
        createdBy || null,
        new Date()
      ]);
      
      if (result.rows && result.rows.length > 0) {
        // Status alanını sonuca ekle
        return {
          ...result.rows[0],
          status: "pending" as string | null
        };
      }
      
      throw new Error("Doküman oluşturulamadı");
    } catch (error) {
      console.error("createDocument işlemi sırasında hata:", error);
      throw error;
    }
  }

  async updateDocument(id: number, updateData: Partial<InsertDocument>): Promise<Document | undefined> {
    try {
      // Status alanını updateData'dan çıkar (eğer varsa)
      const { status, ...cleanUpdateData } = updateData as any;
      
      // Özel SQL sorgusu oluştur
      const updateFields = [];
      const params = [];
      let paramIndex = 1;
      
      // Değiştirilecek alanları belirle
      Object.entries(cleanUpdateData).forEach(([key, value]) => {
        // Column ismi için snake_case dönüşümü yap
        const columnName = key.replace(/([A-Z])/g, '_$1').toLowerCase();
        updateFields.push(`${columnName} = $${paramIndex}`);
        params.push(value);
        paramIndex++;
      });
      
      // Boş güncelleme isteği kontrolü
      if (updateFields.length === 0) {
        console.log("Güncelleme için alan belirtilmemiş");
        const existingDoc = await this.getDocument(id);
        return existingDoc;
      }
      
      // SQL sorgusu
      const sqlQuery = `
        UPDATE documents 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING id, name, type, content, original_content as "originalContent", 
                 project_id as "projectId", created_by as "createdBy",
                 created_at as "createdAt", analyzed_at as "analyzedAt"
      `;
      
      params.push(id); // WHERE id = ? için parametre ekle
      
      // Sorguyu çalıştır
      const result = await pool.query(sqlQuery, params);
      
      if (result.rows && result.rows.length > 0) {
        // Status alanını sonuca ekle
        return {
          ...result.rows[0],
          status: "completed" as string | null  // Güncelleme yapıldığı için "completed" olarak işaretle
        };
      }
      
      return undefined;
    } catch (error) {
      console.error(`Doküman güncelleme hatası (ID: ${id}):`, error);
      throw error;
    }
  }

  async deleteDocument(id: number): Promise<boolean> {
    try {
      // İlişkili tüm test senaryolarını sil
      await db.delete(testScenarios).where(eq(testScenarios.documentId, id));
      
      // İlişkili tüm API uç noktalarını sil
      await db.delete(apiEndpoints).where(eq(apiEndpoints.documentId, id));
      
      // İlişkili tüm gereksinimleri sil
      await db.delete(requirements).where(eq(requirements.documentId, id));
      
      // İlişkili tüm bileşenleri sil
      await db.delete(components).where(eq(components.documentId, id));
      
      // İlişkili AI analiz verilerini sil
      await db.delete(aiAnalysis).where(eq(aiAnalysis.documentId, id));
      
      // İlişkili kapsam doğrulama sonuçlarını sil (varsa)
      try {
        await db.delete(coverageValidation).where(eq(coverageValidation.documentId, id));
      } catch (error) {
        console.warn(`Kapsam doğrulama verisi silme hatası (devam ediliyor): ${error}`);
      }
      
      // Ham SQL sorgusuyla dokümanı sil - Status alanı sorunu nedeniyle
      try {
        const query = `DELETE FROM documents WHERE id = $1 RETURNING id`;
        const result = await pool.query(query, [id]);
        
        console.log(`PostgreSQL: Doküman ve ilişkili tüm veriler silindi: ID=${id}`);
        return result.rows.length > 0;
      } catch (sqlError) {
        console.error("Doküman silme SQL hatası:", sqlError);
        
        // Son çare olarak Drizzle ile dene
        const result = await db.delete(documents).where(eq(documents.id, id)).returning();
        return result.length > 0;
      }
    } catch (error) {
      console.error("Doküman silme hatası:", error);
      return false;
    }
  }

  // Component operations
  async getComponentsByDocumentId(documentId: number): Promise<Component[]> {
    return await db.select().from(components).where(eq(components.documentId, documentId));
  }

  async createComponent(component: InsertComponent): Promise<Component> {
    const result = await db.insert(components).values(component).returning();
    return result[0];
  }

  // Requirement operations
  async getRequirements(): Promise<Requirement[]> {
    return await db.select().from(requirements);
  }

  async getRequirementsByDocumentId(documentId: number): Promise<Requirement[]> {
    return await db.select().from(requirements).where(eq(requirements.documentId, documentId));
  }

  async createRequirement(requirement: InsertRequirement): Promise<Requirement> {
    const result = await db.insert(requirements).values(requirement).returning();
    return result[0];
  }

  // API Endpoint operations
  async getApiEndpointsByDocumentId(documentId: number): Promise<ApiEndpoint[]> {
    return await db.select().from(apiEndpoints).where(eq(apiEndpoints.documentId, documentId));
  }

  async createApiEndpoint(apiEndpoint: InsertApiEndpoint): Promise<ApiEndpoint> {
    const result = await db.insert(apiEndpoints).values(apiEndpoint).returning();
    return result[0];
  }

  // Test Scenario operations
  async getTestScenariosByDocumentId(documentId: number): Promise<TestScenario[]> {
    if (documentId === null) {
      return await db.select().from(testScenarios);
    }
    return await db.select().from(testScenarios).where(eq(testScenarios.documentId, documentId));
  }

  async createTestScenario(testScenario: InsertTestScenario): Promise<TestScenario> {
    const result = await db.insert(testScenarios).values(testScenario).returning();
    return result[0];
  }
  
  async updateTestScenario(id: number, updateData: Partial<InsertTestScenario>): Promise<TestScenario | undefined> {
    const result = await db.update(testScenarios)
      .set(updateData)
      .where(eq(testScenarios.id, id))
      .returning();
    return result[0];
  }

  // AI Analysis operations
  async getAiAnalysisByDocumentId(documentId: number): Promise<AiAnalysis | undefined> {
    const result = await db.select().from(aiAnalysis).where(eq(aiAnalysis.documentId, documentId));
    return result[0];
  }

  async createAiAnalysis(analysis: InsertAiAnalysis): Promise<AiAnalysis> {
    const result = await db.insert(aiAnalysis).values({
      ...analysis,
      analyzedAt: new Date()
    }).returning();
    return result[0];
  }

  async saveAIAnalysis(documentId: number, analysisResult: any): Promise<void> {
    try {
      // AI analiz sonuçlarını veritabanına kaydet
      await this.createAiAnalysis({
        documentId: documentId,
        observations: analysisResult
      });
    } catch (error) {
      console.error('AI analiz sonuçları kaydedilirken hata:', error);
      throw error;
    }
  }

  // AI Learning Memory operations
  async searchAiLearningMemory(queryPattern: string): Promise<AiLearningMemory[]> {
    // Basit bir arama için LIKE kullanılıyor, daha gelişmiş bir semantik arama için PostgreSQL tam metin arama özellikleri kullanılabilir
    const result = await db.select().from(aiLearningMemory)
      .where(
        and(
          eq(aiLearningMemory.queryPattern, queryPattern)
        )
      )
      .orderBy(aiLearningMemory.successMetric);
    return result;
  }

  async getAiLearningMemoryById(id: number): Promise<AiLearningMemory | undefined> {
    const result = await db.select().from(aiLearningMemory).where(eq(aiLearningMemory.id, id));
    return result[0];
  }

  async createAiLearningMemory(memory: InsertAiLearningMemory): Promise<AiLearningMemory> {
    const now = new Date();
    const result = await db.insert(aiLearningMemory).values({
      ...memory,
      lastUsed: now,
      createdAt: now
    }).returning();
    return result[0];
  }

  async updateAiLearningMemory(id: number, memory: Partial<InsertAiLearningMemory>): Promise<AiLearningMemory | undefined> {
    const result = await db.update(aiLearningMemory)
      .set({
        ...memory,
        lastUsed: new Date()
      })
      .where(eq(aiLearningMemory.id, id))
      .returning();
    return result[0];
  }

  async incrementMemoryUsage(id: number): Promise<AiLearningMemory | undefined> {
    // İlk önce mevcut kaydı al
    const current = await this.getAiLearningMemoryById(id);
    if (!current) return undefined;
    
    // Frekansı artır ve son kullanım tarihini güncelle
    const result = await db.update(aiLearningMemory)
      .set({
        frequencyUsed: (current.frequencyUsed || 0) + 1,
        lastUsed: new Date()
      })
      .where(eq(aiLearningMemory.id, id))
      .returning();
    return result[0];
  }

  // AI Interaction History operations
  async getAiInteractionHistory(userId?: number, limit?: number): Promise<AiInteractionHistory[]> {
    let queryBuilder = db.select().from(aiInteractionHistory);
    
    if (userId) {
      queryBuilder = queryBuilder.where(eq(aiInteractionHistory.userId, userId));
    }
    
    // Sıralama ve limit ekleme
    queryBuilder = queryBuilder.orderBy(aiInteractionHistory.createdAt);
    
    if (limit) {
      queryBuilder = queryBuilder.limit(limit);
    }
    
    const result = await queryBuilder;
    return result;
  }

  async createAiInteractionHistory(interaction: InsertAiInteractionHistory): Promise<AiInteractionHistory> {
    const result = await db.insert(aiInteractionHistory).values({
      ...interaction,
      createdAt: new Date()
    }).returning();
    return result[0];
  }

  async updateInteractionFeedback(id: number, rating: number): Promise<AiInteractionHistory | undefined> {
    const result = await db.update(aiInteractionHistory)
      .set({ feedbackRating: rating })
      .where(eq(aiInteractionHistory.id, id))
      .returning();
    return result[0];
  }
  
  // Coverage Validation operations
  async getCoverageValidationByDocumentId(documentId: number): Promise<CoverageValidation | undefined> {
    const result = await db.select()
      .from(coverageValidation)
      .where(eq(coverageValidation.documentId, documentId));
    return result[0];
  }

  async createCoverageValidation(data: InsertCoverageValidation): Promise<CoverageValidation> {
    const result = await db.insert(coverageValidation).values({
      ...data,
      createdAt: new Date()
    }).returning();
    return result[0];
  }

  async deleteCoverageValidation(documentId: number): Promise<boolean> {
    const result = await db.delete(coverageValidation)
      .where(eq(coverageValidation.documentId, documentId));
    return true;
  }
  
  // Project operations
  async getProjects(): Promise<Project[]> {
    return await db.select({
      id: projects.id,
      name: projects.name,
      description: projects.description,
      createdBy: projects.createdBy,
      status: projects.status,
      createdAt: projects.createdAt,
      updatedAt: projects.updatedAt,
      automationEnabled: projects.automationEnabled,
      jiraEnabled: projects.jiraEnabled,
      testRailEnabled: projects.testRailEnabled,
      almEnabled: projects.almEnabled,
      visiumEnabled: projects.visiumEnabled
    }).from(projects);
  }
  
  async getProjectsByUserId(userId: number): Promise<Project[]> {
    const userProjectList = await db.select()
      .from(userProjects)
      .where(eq(userProjects.userId, userId));
      
    if (!userProjectList.length) {
      return [];
    }
    
    const projectIds = userProjectList.map(up => up.projectId);
    
    // Hiç proje yoksa boş dizi döndür
    if (projectIds.length === 0) {
      return [];
    }
    
    return await db.select({
      id: projects.id,
      name: projects.name,
      description: projects.description,
      createdBy: projects.createdBy,
      status: projects.status,
      createdAt: projects.createdAt,
      updatedAt: projects.updatedAt,
      automationEnabled: projects.automationEnabled,
      jiraEnabled: projects.jiraEnabled,
      testRailEnabled: projects.testRailEnabled,
      almEnabled: projects.almEnabled,
      visiumEnabled: projects.visiumEnabled
    }).from(projects)
      .where(
        inArray(projects.id, projectIds)
      );
  }
  
  async getProject(id: number): Promise<Project | undefined> {
    const result = await db.select({
      id: projects.id,
      name: projects.name,
      description: projects.description,
      createdBy: projects.createdBy,
      status: projects.status,
      createdAt: projects.createdAt,
      updatedAt: projects.updatedAt,
      automationEnabled: projects.automationEnabled,
      jiraEnabled: projects.jiraEnabled,
      testRailEnabled: projects.testRailEnabled,
      almEnabled: projects.almEnabled,
      visiumEnabled: projects.visiumEnabled
    }).from(projects).where(eq(projects.id, id));
    return result[0];
  }
  
  async createProject(project: InsertProject): Promise<Project> {
    const result = await db.insert(projects).values({
      ...project,
      createdAt: new Date(),
      updatedAt: new Date()
    }).returning();
    return result[0];
  }
  
  async updateProject(id: number, data: Partial<InsertProject>): Promise<Project | undefined> {
    const result = await db.update(projects)
      .set({
        ...data,
        updatedAt: new Date()
      })
      .where(eq(projects.id, id))
      .returning();
    return result[0];
  }
  
  async deleteProject(id: number): Promise<boolean> {
    try {
      // İlişkili tüm dokümanların ID'lerini al
      const projectDocuments = await this.getDocumentsByProjectId(id);
      
      // İlişkili tüm dokümanları sil
      for (const doc of projectDocuments) {
        await this.deleteDocument(doc.id);
      }
      
      // Kullanıcı-proje ilişkilerini sil
      await db.delete(userProjects).where(eq(userProjects.projectId, id));
      
      // Projeyi sil
      const result = await db.delete(projects).where(eq(projects.id, id)).returning();
      
      return result.length > 0;
    } catch (error) {
      console.error("Proje silme hatası:", error);
      return false;
    }
  }
  
  // User-Project operations
  async getUserProjects(userId: number): Promise<UserProject[]> {
    return await db.select()
      .from(userProjects)
      .where(eq(userProjects.userId, userId));
  }
  
  async getProjectUsers(projectId: number): Promise<UserProject[]> {
    return await db.select()
      .from(userProjects)
      .where(eq(userProjects.projectId, projectId));
  }
  
  async getUserProject(userId: number, projectId: number): Promise<UserProject | undefined> {
    const result = await db.select()
      .from(userProjects)
      .where(
        and(
          eq(userProjects.userId, userId),
          eq(userProjects.projectId, projectId)
        )
      );
    return result[0];
  }
  
  async addUserToProject(data: InsertUserProject): Promise<UserProject> {
    const result = await db.insert(userProjects).values({
      ...data,
      createdAt: new Date()
    }).returning();
    return result[0];
  }
  
  async updateUserProjectRole(userId: number, projectId: number, role: string): Promise<UserProject | undefined> {
    const result = await db.update(userProjects)
      .set({ role })
      .where(
        and(
          eq(userProjects.userId, userId),
          eq(userProjects.projectId, projectId)
        )
      )
      .returning();
    return result[0];
  }
  
  async removeUserFromProject(userId: number, projectId: number): Promise<boolean> {
    const result = await db.delete(userProjects)
      .where(
        and(
          eq(userProjects.userId, userId),
          eq(userProjects.projectId, projectId)
        )
      );
    return true;
  }
  
  async hasProjectAccess(userId: number, projectId: number, requiredRole?: string): Promise<boolean> {
    const userProject = await this.getUserProject(userId, projectId);
    if (!userProject) {
      return false;
    }
    
    if (!requiredRole) {
      return true;
    }
    
    // Role-based permission checking
    // owner > editor > viewer
    const roles: Record<string, number> = { 'owner': 3, 'editor': 2, 'viewer': 1 };
    const userRoleLevel = userProject.role ? (roles[userProject.role] || 0) : 0;
    const requiredRoleLevel = roles[requiredRole] || 0;
    
    return userRoleLevel >= requiredRoleLevel;
  }
  
  // Permission operations
  async getUserPermissions(userId: number): Promise<Permission[]> {
    const userPermResult = await db.select()
      .from(userPermissions)
      .where(eq(userPermissions.userId, userId));
      
    if (!userPermResult.length) {
      return [];
    }
    
    const permissionIds = userPermResult.map(up => up.permissionId);
    
    // Hiç izin yoksa boş dizi döndür
    if (permissionIds.length === 0) {
      return [];
    }
    
    // inArray kullanarak sorgu yap
    const permissionsResult = await db.select()
      .from(permissions)
      .where(
        inArray(permissions.id, permissionIds)
      );
      
    return permissionsResult;
  }
  
  async getAllPermissions(): Promise<Permission[]> {
    // Veritabanından tüm izinleri çek
    return await db.select().from(permissions);
  }
  
  async hasPermission(userId: number, permissionName: string): Promise<boolean> {
    // Önce kullanıcı rolünü kontrol et - admin ise direkt erişim ver
    const userInfo = await db.select()
      .from(users)
      .where(eq(users.id, userId));
      
    if (userInfo.length > 0 && userInfo[0].role === 'admin') {
      return true;
    }
    
    // Admin değilse normal izin kontrolü yap
    const permInfo = await db.select()
      .from(permissions)
      .where(eq(permissions.name, permissionName));
      
    if (!permInfo.length) {
      return false;
    }
    
    const permissionId = permInfo[0].id;
    
    const result = await db.select()
      .from(userPermissions)
      .where(
        and(
          eq(userPermissions.userId, userId),
          eq(userPermissions.permissionId, permissionId)
        )
      );
      
    return result.length > 0;
  }
  
  async grantPermission(userId: number, permissionId: number): Promise<void> {
    // İlk önce böyle bir ilişki var mı diye kontrol et
    const existing = await db.select()
      .from(userPermissions)
      .where(
        and(
          eq(userPermissions.userId, userId),
          eq(userPermissions.permissionId, permissionId)
        )
      );
      
    if (existing.length === 0) {
      await db.insert(userPermissions).values({
        userId,
        permissionId
      });
    }
  }
  
  async revokePermission(userId: number, permissionId: number): Promise<void> {
    await db.delete(userPermissions)
      .where(
        and(
          eq(userPermissions.userId, userId),
          eq(userPermissions.permissionId, permissionId)
        )
      );
  }

  // API Connection operations
  async getApiConnectionsByProjectId(projectId: number): Promise<ApiConnection[]> {
    return await db.select()
      .from(apiConnections)
      .where(eq(apiConnections.projectId, projectId));
  }
  
  async getApiConnection(id: number): Promise<ApiConnection | undefined> {
    const result = await db.select()
      .from(apiConnections)
      .where(eq(apiConnections.id, id));
    return result[0];
  }
  
  async createApiConnection(data: InsertApiConnection): Promise<ApiConnection> {
    const now = new Date();
    const result = await db.insert(apiConnections).values({
      ...data,
      createdAt: now,
      updatedAt: now
    }).returning();
    return result[0];
  }
  
  async updateApiConnection(id: number, data: Partial<InsertApiConnection>): Promise<ApiConnection | undefined> {
    const result = await db.update(apiConnections)
      .set({
        ...data,
        updatedAt: new Date()
      })
      .where(eq(apiConnections.id, id))
      .returning();
    return result[0];
  }
  
  async deleteApiConnection(id: number): Promise<boolean> {
    try {
      const result = await db.delete(apiConnections)
        .where(eq(apiConnections.id, id))
        .returning();
      return result.length > 0;
    } catch (error) {
      console.error("API bağlantısı silme hatası:", error);
      return false;
    }
  }
}

export const postgresStorage = new PostgresStorage();