import OpenAI from "openai";
import { storage } from '../storage';

// AI model sabitleri
export const AI_MODELS = {
  "o1": {
    displayName: "GPT-o1",
    deploymentName: "netas-ai-o1",
    maxTokens: 16000,
    description: "GPT-o1 modeli hızlı, performanslı ve güvenilir"
  },
  "o3": {
    displayName: "GPT-o3-mini",
    deploymentName: "netas-ai-o3-mini",
    maxTokens: 16000,
    description: "GPT-o3-mini modeli ekonomik ve verimli"
  },
  "o4": {
    displayName: "GPT-o4",
    deploymentName: "netas-ai-o4",
    maxTokens: 16000,
    description: "GPT-o4 modeli gelişmiş özelliklere sahip"
  }
};

// Başlangıçta hangi modelin kullanılacağını belirleyen değişken
let CURRENT_MODEL: keyof typeof AI_MODELS = "o4";

// Kullanılacak AI modelini ayarlamak için fonksiyon
export function setAIModel(modelType: keyof typeof AI_MODELS) {
  CURRENT_MODEL = modelType;
}

// Şu anda aktif olan modeli almak için fonksiyon
export function getCurrentModel() {
  return CURRENT_MODEL;
}

// OpenAI client oluşturma fonksiyonu (model parametresi ile)
const createOpenAIClient = (modelType: keyof typeof AI_MODELS) => {
  const model = AI_MODELS[modelType];
  
  return new OpenAI({
    apiKey: process.env.AZURE_OPENAI_API_KEY,
    baseURL: `https://netas-ai.openai.azure.com/openai/deployments/${model.deploymentName}`,
    defaultHeaders: {
      "api-key": process.env.AZURE_OPENAI_API_KEY,
    },
    defaultQuery: {
      "api-version": "2024-12-01-preview",
    },
  });
};

// Komponenti temsil eden arayüz
export interface ExtractedComponent {
  name: string;
  description: string;
  type: string;
  isNew: boolean;
}

// Gereksinimleri temsil eden arayüz
export interface ExtractedRequirement {
  code: string;
  description: string;
  category: string;
}

// API uç noktasını temsil eden arayüz
export interface ExtractedApiEndpoint {
  url: string;
  method: string;
  parameters: Record<string, any>;
  requirementCode?: string;
}

// Test senaryosunu temsil eden arayüz
export interface TestScenario {
  title: string;
  preconditions: string;
  steps: string[];
  expectedResults: string;
  requirementCode?: string;
  format?: string; // default, gherkin, selenium, etc.
}

// AI gözlemleri için arayüz
export interface AIObservation {
  text: string;
  type: "general" | "missing_info" | "suggestion" | "performance" | "integration" | "usability" | "functional";
  importance?: "low" | "medium" | "high";
  recommendedAction?: string;
}

// Doküman analiz sonuçları
export interface DocumentAnalysisResult {
  components: ExtractedComponent[];
  requirements: ExtractedRequirement[];
  apiEndpoints: ExtractedApiEndpoint[];
  observations: AIObservation[];
}

// Test senaryoları sonuçları
export interface TestScenariosResult {
  scenarios: TestScenario[];
  coverageRate: number;
  missingRequirements: string[];
}

/**
 * OpenAI kullanarak doküman içeriğini analiz eder ve gereksinimleri, bileşenleri ve API uç noktalarını çıkarır
 */
export async function analyzeDocument(documentContent: string): Promise<DocumentAnalysisResult> {
  try {
    const openai = createOpenAIClient(CURRENT_MODEL);
    
    const prompt = `
    Lütfen aşağıdaki dokümanı inceleyerek içerisindeki yazılım gereksinimlerini, bileşenleri ve API uç noktaları ile ilgili tüm bilgileri çıkar:
    
    ${documentContent}
    
    Analiz sonuçlarını aşağıdaki JSON formatında döndür:
    {
      "components": [
        {
          "name": "Komponent Adı",
          "description": "Komponentin detaylı açıklaması",
          "type": "UI Component|Service|Database|External System|Utility",
          "isNew": true|false
        }
      ],
      "requirements": [
        {
          "code": "Gereksinim Kodu",
          "description": "Gereksinimin açıklaması",
          "category": "Fonksiyonel|Performans|Güvenlik|Kullanılabilirlik|Uyumluluk"
        }
      ],
      "apiEndpoints": [
        {
          "url": "/api/resource/{id}",
          "method": "GET|POST|PUT|DELETE",
          "parameters": { "id": "string", "name": "string" },
          "requirementCode": "İlgili gereksinim kodu"
        }
      ],
      "observations": [
        {
          "text": "Gözlem metni",
          "type": "general|missing_info|suggestion|performance|integration|usability|functional",
          "importance": "low|medium|high",
          "recommendedAction": "Önerilen eylem"
        }
      ]
    }
    
    Talimatlar:
    - Komponentler için UI bileşenleri, servisler, veritabanları veya dış sistemler olabilir.
    - Her gereksinim için bir kod ata (REQ-001, REQ-002 gibi).
    - API uç noktalarını doküman içinden tespit et ve uygun HTTP metotlarıyla eşleştir.
    - Eksik veya belirsiz bilgileri "observations" kısmında belirt.
    - Dokümanı test gereksinimleri açısından analiz et, yeterli test kapsamı sağlamak için gereken gereksinimlere ve kritik noktalara dikkat et.
    - Dokümanda açıkça belirtilmemiş ancak test edilmesi gereken durumları da tespit et.
    - API uç noktalarını detaylı şekilde çıkararak test edilebilecek tüm parametreleri belirle.
    `;

    // o1 ve o3 modelleri için "max_completion_tokens", o4 için "max_tokens" kullanılmalı
    // o1 ve o3 modelleri için temperature parametresi de desteklenmiyor
    const completionParams: any = {
      model: AI_MODELS[CURRENT_MODEL as keyof typeof AI_MODELS].deploymentName,
      messages: [{ role: "user", content: prompt }],
      response_format: { type: "json_object" },
    };
    
    // Model tipine göre doğru parametre adını kullan
    if (CURRENT_MODEL === "o1" || CURRENT_MODEL === "o3") {
      completionParams.max_completion_tokens = AI_MODELS[CURRENT_MODEL as keyof typeof AI_MODELS].maxTokens;
    } else {
      completionParams.max_tokens = AI_MODELS[CURRENT_MODEL as keyof typeof AI_MODELS].maxTokens;
      completionParams.temperature = 0.1;
    }
    
    const response = await openai.chat.completions.create(completionParams);

    const responseContent = response.choices[0]?.message?.content;
    if (!responseContent) {
      throw new Error("OpenAI'dan yanıt alınamadı");
    }

    return JSON.parse(responseContent);
  } catch (error) {
    console.error("OpenAI ile doküman analizi hatası:", error);
    throw new Error(`Doküman analizi başarısız oldu: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Gereksinimlerden test senaryoları oluşturur
 */
export async function generateTestScenarios(
  requirements: ExtractedRequirement[], 
  maxTokens: number = 16000, // Token sınırını Azure OpenAI limitine göre ayarladık
  documentContent: string = ""
): Promise<TestScenariosResult> {
  try {
    const openai = createOpenAIClient(CURRENT_MODEL);
    const requirementsText = requirements.map(r => `${r.code}: ${r.description} (${r.category})`).join("\n");
    
    const prompt = `
Lütfen aşağıdaki doküman içeriği ve gereksinimler için aşırı detaylı, test senaryoları ve kesinlikle bu senaryolara bağlı test adımları oluştur:
    
    DOKÜMAN İÇERİĞİ (BU İÇERİĞİ TEST SENARYOLARI OLUŞTURMAK İÇİN TEMEL AL, BU DOKÜMANA TAMAMEN SADIK KAL):
    ${documentContent}
    
    AŞIRI DETAYLI VE TAM KAPSAM SENARYOLARI İÇİN ZORUNLU YÖNERGELERİM:
    
    1. TEST ADIM YAZIMINDA DETAYLI OLUN: 
       - Her test adımı dokümandan elde edilebilecek maksimum detaya sahip olmalı
       - Her adım en az 2-3 cümle uzunluğunda ve çok detaylı açıklamalar içermeli
       - KONTROLLERİ DETAYLANDIR: Her doğrulama adımını ayrıntılı aç, doğrulama için kullanılan kriterleri belirt
       
 İŞLEM SIRASINI NET BELİRT: "Önce X yapılır, ardından Y ekranında Z kontrolü gerçekleştirilir, daha sonra W tuşuna basılarak T işlemi tamamlanır" şeklinde detaylı adım sıralaması yap
       - Her kontrol adımı için beklenen davranış, ekranda görülmesi beklenen bilgiler ve bunların nasıl doğrulanacağı ayrıntılı açıklanmalı
    
    2. KAPSAMLI SENARYOLAR: 
    - Black box test senaryo kavramına uygun şekilde temel akış (happy path) test senaryo adımlarını oluştur
       - Alternatif akışlar - dokümanda var ise farklı kullanıcı tipleri veya durumları için oluştur
       - İşlem zaman aşımı testleri - zaman aşımı süreleriyle
       - Özellikle hata mesajları, hataları ele alma gibi detaylar dokümanda varsa bunları MUTLAKA dahil et
    
    3. ÖN KOŞULLAR VE BEKLENEN SONUÇLAR:
       - Her senaryo için dokümanda var ise ayrıntılı ön koşul belirt
       - Her ön koşulun nasıl sağlanacağını ayrıntılı açıkla
       - Doğrulama adımlarını spesifik olarak belirt (neyin, nasıl, hangi değerlerle doğrulanacağı)
       - Beklenen sonuçları sayısal değerler, metinler ve görseller bazında detaylandır
    
    4. DOKÜMAN DEĞERLERİNİ KULLAN:
       - Dokümanda belirtilen TÜM değerleri, örnek girdileri ve beklentileri aynen kullan
       - Terminolojiyi tamamen dokümandan al, hiçbir terim uydurma
       - Örnek değerler, sınır değerler gibi unsurlar dokümanda belirtilmişse MUTLAKA bunları aynı şekilde kullan
       - Dokümanda belirtilen tüm veri alanlarını, ekran alanlarını ve kontrolleri test adımlarında aynen belirt
    
    5. HER SENARYO KAPSAMLI VE DETAYLI OLSUN:
       - Her senaryo, use case adımlarının tamamını içerecek şekilde test adımlarına ayrıştırılmalı
       - Test adımları sırayla ve birbirini tamamlayacak şekilde düzenlenmeli
       - Her senaryo bir gereksinimin tüm yönlerini test etsin
       - Senaryolar çok net ve ayrıntılı başlıklara sahip olsun
    
    Format olarak SADECE "default" formatını kullan, başka bir format kabul edilmeyecektir:
    - Her adım en az 2-3 cümle uzunluğunda olmalı
    - Adımları "Tıkla, incele, kontrol et" gibi kısa ifadeler yerine tam ve uzun cümleler halinde belirt
    
 Adımlar tam olarak ne yapılacağını, hangi değerlerle yapılacağını, nerede yapılacağını ve sonuçların nasıl doğrulanacağını içermeli
    
    Cevap aşağıdaki alanları içeren bir JSON nesnesi olmalı:
    - scenarios: Tüm test senaryolarının bulunduğu dizi
    - coverageRate: Gereksinimlerin test kapsamı oranı (0-1 arası)
    - missingRequirements: Yeterli test kapsamı olmayan gereksinimlerin kodları
    
    Her senaryo şöyle formatlanmalıdır:
    {
      "title": "Gereksinim kodu ve uzun, açıklayıcı başlık",
      "preconditions": ["Detaylı ön koşul 1 (en az bir uzun cümle)", "Detaylı ön koşul 2 (en az bir uzun cümle)"],
      "steps": ["Aşırı detaylı adım 1 (en az 2-3 cümle)", "Aşırı detaylı adım 2 (en az 2-3 cümle)", "..."],
      "expectedResults": "Beklenen sonuçların aşırı kapsamlı açıklaması (en az 5-6 cümle)",
      "requirementCode": "İlgili gereksinim kodu",
      "format": "default"
    }
    
    
    BU TEST SENARYOLARININ  DETAYLI OLMALARI SON DERECE ÖNEMLİDİR. LÜTFEN HER ADIMI DOKÜMANDAKİ TÜM DETAYLARI İÇERECEK ŞEKİLDE UZUN, AÇIKLAYICI VE KAPSAMLI OLARAK YAZIN. TEST ADIMLARINI OLUŞTURURKEN DOKÜMANDAKİ ANLAM BÜTÜNLÜĞÜNE SADIK KAL VE DOKÜMANDA YER ALMAYAN  EKRAN, BUTON VE TERİMLERİ İÇERİĞİNDE KULLANMA. MÜMKÜN OLAN EN DETAYLI ADIM AÇIKLAMALARINI HAZIRLAYIN.
    `;

    // o1 ve o3 modelleri için "max_completion_tokens", o4 için "max_tokens" kullanılmalı
    // o1 ve o3 modelleri için temperature parametresi de desteklenmiyor
    const completionParams: any = {
      model: AI_MODELS[CURRENT_MODEL as keyof typeof AI_MODELS].deploymentName,
      messages: [{ role: "user", content: prompt }],
      response_format: { type: "json_object" },
    };
    
    // Model tipine göre doğru parametre adını kullan
    if (CURRENT_MODEL === "o1" || CURRENT_MODEL === "o3") {
      completionParams.max_completion_tokens = maxTokens || AI_MODELS[CURRENT_MODEL as keyof typeof AI_MODELS].maxTokens;
    } else {
      completionParams.max_tokens = maxTokens || AI_MODELS[CURRENT_MODEL as keyof typeof AI_MODELS].maxTokens;
      completionParams.temperature = 0.1;
    }
    
    const response = await openai.chat.completions.create(completionParams);

    const responseContent = response.choices[0]?.message?.content;
    if (!responseContent) {
      throw new Error("OpenAI'dan yanıt alınamadı");
    }

    try {
      return JSON.parse(responseContent);
    } catch (parseError) {
      console.error("JSON ayrıştırma hatası:", parseError);
      console.error("OpenAI'dan alınan yanıt içeriği (ilk 500 karakter):", responseContent.substring(0, 500) + "...");
      
      // JSON ayrıştırma hatası durumunda basit bir fallback döndür
      return {
        scenarios: [],
        coverageRate: 0,
        missingRequirements: ["JSON ayrıştırma hatası nedeniyle gereksinimler tespit edilemedi"]
      };
    }
  } catch (error) {
    console.error("OpenAI ile test senaryoları oluşturma hatası:", error);
    throw new Error(`Test senaryoları oluşturulamadı: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Belirsiz gereksinimleri daha ayrıntılı açıklayarak netleştirir
 */
export async function clarifyRequirement(requirement: string, documentContext: string): Promise<string> {
  try {
    const openai = createOpenAIClient(CURRENT_MODEL);
    const prompt = `
    Bir yazılım test dokümanından potansiyel olarak belirsiz bir gereksinimim var. 
    Lütfen bu gereksinimi test senaryosu oluşturmak için yeterli olacak şekilde netleştirmeme yardımcı ol.
    
    Gereksinim: ${requirement}
    
    Doküman Bağlamı: ${documentContext}
    
    Lütfen aşağıdaki yapıda kapsamlı bir yanıt ver:
    
    1. GEREKSİNİM ANALİZİ:
       - Bu gereksinimin asıl amacı nedir?
       - Belirsiz veya eksik noktalar nelerdir?
    
    2. NETLEŞTİRİLMİŞ GEREKSİNİM:
       - Gereksinimin daha net ve test edilebilir hali nedir?
       - Kabul kriterleri nelerdir?
    
    3. TEST EDİLEBİLİRLİK:
       - Bu gereksinim nasıl test edilebilir?
       - Test kapsamı için önemli noktalar nelerdir?
       - Pozitif ve negatif test senaryoları neler olabilir?
    
    4. BAĞLANTILAR:
       - Bu gereksinim diğer hangi gereksinimlerle ilişkilidir?
       - Bu gereksinimin test edilmesi için ön koşullar nelerdir?
    `;

    // o1 ve o3 modelleri için "max_completion_tokens", o4 için "max_tokens" kullanılmalı
    // o1 ve o3 modelleri için temperature parametresi de desteklenmiyor
    const completionParams: any = {
      model: AI_MODELS[CURRENT_MODEL as keyof typeof AI_MODELS].deploymentName,
      messages: [{ role: "user", content: prompt }],
    };
    
    // Model tipine göre doğru parametre adını kullan
    if (CURRENT_MODEL === "o1" || CURRENT_MODEL === "o3") {
      completionParams.max_completion_tokens = 1500;
    } else {
      completionParams.max_tokens = 1500;
      completionParams.temperature = 0.5;
    }
    
    const response = await openai.chat.completions.create(completionParams);

    return response.choices[0]?.message?.content || "Netleştirme mevcut değil";
  } catch (error) {
    console.error("OpenAI ile gereksinim netleştirme hatası:", error);
    return "Bir hata nedeniyle gereksinim netleştirilemedi";
  }
}

/**
 * Test senaryosu doğrulama - Belirli bir test senaryosunun ilgili gereksinimi ne kadar iyi karşıladığını değerlendirir
 */
export async function validateTestScenario(scenario: TestScenario, requirement: ExtractedRequirement): Promise<{
  score: number;  // 0-100 arası bir puan
  feedback: string;
  improvements: string[];
  coverage: {
    functional: number;
    edge_cases: number;
    negative_tests: number;
  }
}> {
  try {
    const openai = createOpenAIClient(CURRENT_MODEL);
    const prompt = `
    Lütfen aşağıdaki test senaryosunun ilgili gereksinimi ne kadar iyi karşıladığını değerlendir:
    
    GEREKSİNİM:
    Kod: ${requirement.code}
    Açıklama: ${requirement.description}
    Kategori: ${requirement.category}
    
    TEST SENARYOSU:
    Başlık: ${scenario.title}
    Ön Koşullar: ${typeof scenario.preconditions === 'string' 
      ? scenario.preconditions 
      : Array.isArray(scenario.preconditions) 
        ? scenario.preconditions.join('\n') 
        : 'Belirtilmemiş'}
    Adımlar:
    ${Array.isArray(scenario.steps) 
      ? scenario.steps.map((step, i) => `${i+1}. ${step}`).join('\n') 
      : typeof scenario.steps === 'string' ? scenario.steps : 'Belirtilmemiş'}
    Beklenen Sonuçlar: ${scenario.expectedResults}
    Format: ${scenario.format || 'default'}
    
    Lütfen aşağıdaki formatta JSON yanıtı oluştur:
    {
      "score": 0-100 arası bir puan,
      "feedback": "Genel değerlendirme içeren bir metin",
      "improvements": [
        "İyileştirme önerisi 1",
        "İyileştirme önerisi 2",
        "..."
      ],
      "coverage": {
        "functional": 0-100 arası bir puan (işlevsel kapsam),
        "edge_cases": 0-100 arası bir puan (sınır durumları kapsama),
        "negative_tests": 0-100 arası bir puan (negatif test kapsama)
      }
    }
    
    Değerlendirme Kriterleri:
    - Adımlar gereksinimi test etmek için yeterince kapsamlı mı?
    - Beklenen sonuçlar net ve doğrulanabilir mi?
    - Test, gereksinimin farklı yönlerini kapsıyor mu?
    - Sınır değerleri ve hata durumları test ediliyor mu?
    - Test adımları açık ve anlaşılır mı?
    `;

    // o1 ve o3 modelleri için "max_completion_tokens", o4 için "max_tokens" kullanılmalı
    // o1 ve o3 modelleri için temperature parametresi de desteklenmiyor
    const completionParams: any = {
      model: AI_MODELS[CURRENT_MODEL as keyof typeof AI_MODELS].deploymentName,
      messages: [{ role: "user", content: prompt }],
      response_format: { type: "json_object" },
    };
    
    // Model tipine göre doğru parametre adını kullan
    if (CURRENT_MODEL === "o1" || CURRENT_MODEL === "o3") {
      completionParams.max_completion_tokens = 1000;
    } else {
      completionParams.max_tokens = 1000;
      completionParams.temperature = 0.5;
    }
    
    const response = await openai.chat.completions.create(completionParams);

    const responseContent = response.choices[0]?.message?.content;
    if (!responseContent) {
      throw new Error("OpenAI'dan yanıt alınamadı");
    }

    try {
      return JSON.parse(responseContent);
    } catch (parseError) {
      console.error("JSON ayrıştırma hatası:", parseError);
      console.error("OpenAI'dan alınan yanıt içeriği (ilk 500 karakter):", responseContent.substring(0, 500) + "...");
      
      // JSON ayrıştırma hatası durumunda basit bir fallback döndür
      return {
        score: 0,
        feedback: "JSON ayrıştırma hatası nedeniyle değerlendirme yapılamadı",
        improvements: ["JSON ayrıştırma hatası: " + (parseError instanceof Error ? parseError.message : String(parseError))],
        coverage: {
          functional: 0,
          edge_cases: 0,
          negative_tests: 0
        }
      };
    }
  } catch (error) {
    console.error("OpenAI ile test senaryosu doğrulama hatası:", error);
    throw new Error(`Test senaryosu doğrulanamadı: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Doküman doğrulama - Test senaryolarının gereksinim dokümanını ne kadar iyi kapsadığını değerlendirir
 */
export async function validateDocumentCoverage(
  requirements: ExtractedRequirement[], 
  scenarios: TestScenario[]
): Promise<{
  overallCoverage: number;
  feedbackSummary: string;
  requirementCoverage: Array<{
    requirementCode: string;
    coverageRate: number;
    uncoveredAspects: string[];
  }>;
  recommendations: string[];
}> {
  try {
    const openai = createOpenAIClient(CURRENT_MODEL);
    // Gereksinimleri ve senaryoları formatla
    const requirementsText = requirements.map(r => 
      `${r.code}: ${r.description} (${r.category})`
    ).join("\n");
    
    const scenariosText = scenarios.map(s => 
      `Başlık: ${s.title}\nGereksinim: ${s.requirementCode}\nFormat: ${s.format}\nAdım Sayısı: ${Array.isArray(s.steps) ? s.steps.length : 0}`
    ).join("\n\n");
    
    const prompt = `
    Lütfen aşağıdaki test senaryolarının verilen gereksinimleri ne kadar iyi kapsadığını değerlendir:
    
    GEREKSİNİMLER:
    ${requirementsText}
    
    TEST SENARYOLARI:
    ${scenariosText}
    
    Lütfen aşağıdaki formatta JSON yanıtı oluştur:
    {
      "overallCoverage": 0-100 arası bir puan (genel kapsama derecesi),
      "feedbackSummary": "Genel değerlendirme özeti",
      "requirementCoverage": [
        {
          "requirementCode": "REQ-001",
          "coverageRate": 0-100 arası bir puan,
          "uncoveredAspects": ["Kapsanmayan yön 1", "Kapsanmayan yön 2"]
        },
        ...
      ],
      "recommendations": [
        "İyileştirme önerisi 1",
        "İyileştirme önerisi 2",
        ...
      ]
    }
    
    Değerlendirme Kriterleri:
    - Her gereksinim için yeterli sayıda test senaryosu var mı?
    - Her gereksinimin farklı yönleri test ediliyor mu?
    - Uygun negatif test senaryoları var mı?
    - Sınır değer testleri ve özel durum kontrolleri test ediliyor mu?
    - Kapsanmayan gereksinimler veya gereksinim yönleri nelerdir?
    `;

    // o1 ve o3 modelleri için "max_completion_tokens", o4 için "max_tokens" kullanılmalı
    // o1 ve o3 modelleri için temperature parametresi de desteklenmiyor
    const completionParams: any = {
      model: AI_MODELS[CURRENT_MODEL as keyof typeof AI_MODELS].deploymentName,
      messages: [{ role: "user", content: prompt }],
      response_format: { type: "json_object" },
    };
    
    // Model tipine göre doğru parametre adını kullan
    if (CURRENT_MODEL === "o1" || CURRENT_MODEL === "o3") {
      completionParams.max_completion_tokens = 2000;
    } else {
      completionParams.max_tokens = 2000;
      completionParams.temperature = 0.5;
    }
    
    const response = await openai.chat.completions.create(completionParams);

    const responseContent = response.choices[0]?.message?.content;
    if (!responseContent) {
      throw new Error("OpenAI'dan yanıt alınamadı");
    }

    try {
      return JSON.parse(responseContent);
    } catch (parseError) {
      console.error("JSON ayrıştırma hatası:", parseError);
      console.error("OpenAI'dan alınan yanıt içeriği (ilk 500 karakter):", responseContent.substring(0, 500) + "...");
      
      // JSON ayrıştırma hatası durumunda basit bir fallback döndür
      return {
        overallCoverage: 0,
        feedbackSummary: "JSON ayrıştırma hatası nedeniyle değerlendirme yapılamadı",
        requirementCoverage: [],
        recommendations: ["JSON ayrıştırma hatası: " + (parseError instanceof Error ? parseError.message : String(parseError))]
      };
    }
  } catch (error) {
    console.error("OpenAI ile doküman kapsama değerlendirme hatası:", error);
    throw new Error(`Doküman kapsamı değerlendirilemedi: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * AI Asistanı için sürekli öğrenme yetenekli yanıt fonksiyonu
 * Bu fonksiyon geçmiş öğrenme verilerini kullanarak daha akıllı yanıtlar üretir
 */
export async function getAIAssistantResponse(prompt: string): Promise<string> {
  try {
    const openai = createOpenAIClient(CURRENT_MODEL);
    // Başlangıç zamanını not al (performans ölçümü için)
    const startTime = Date.now();
    
    // Prompt içindeki anahtar sözcüklere göre bellek arama yaparak benzer sorguları bul
    const memories = await storage.searchAiLearningMemory(prompt);
    
    // İlgili geçmiş bellekleri birleştirerek bağlam oluştur
    let contextFromMemory = "";
    let usedMemoryIds: number[] = [];
    
    if (memories.length > 0) {
      // Kullanılacak en fazla bellek sayısı 
      const MAX_MEMORIES = 3;
      const relevantMemories = memories.slice(0, MAX_MEMORIES);
      
      contextFromMemory = relevantMemories.map(memory => {
        usedMemoryIds.push(memory.id);
        return `Geçmiş Başarılı Analiz (${memory.successMetric.toFixed(2)} puanlı): ${JSON.stringify(memory.knowledgeStore)}`;
      }).join("\n\n");
      
      // Kullanılan belleklerin kullanım sayacını artır
      for (const memoryId of usedMemoryIds) {
        await storage.incrementMemoryUsage(memoryId);
      }
    }
    
    // Prompt başına bağlama bilgisi ekle
    const enhancedPrompt = `
    Sen bir doküman analiz asistanısın. Teknik dokümanları analiz edip, gereksinimleri ve test senaryolarını belirleyebilirsin.
    
    ${contextFromMemory ? `Aşağıda benzer sorulara daha önce verilen başarılı yanıtlardan öğrendiğin bilgiler var:
    ${contextFromMemory}
    
    Bu bilgileri kullanarak daha iyi bir yanıt ver.` : ''}
    
    Aşağıdaki soruya Türkçe olarak cevap ver:
    
    ${prompt}
    `;

    // OpenAI API çağrısı
    // o1 ve o3 modelleri için "max_completion_tokens", o4 için "max_tokens" kullanılmalı
    // o1 ve o3 modelleri için temperature parametresi de desteklenmiyor
    const completionParams: any = {
      model: AI_MODELS[CURRENT_MODEL as keyof typeof AI_MODELS].deploymentName,
      messages: [{ role: "user", content: enhancedPrompt }],
    };
    
    // Model tipine göre doğru parametre adını kullan
    if (CURRENT_MODEL === "o1" || CURRENT_MODEL === "o3") {
      completionParams.max_completion_tokens = 2000;
    } else {
      completionParams.max_tokens = 2000;
      completionParams.temperature = 0.5; // Daha tutarlı yanıtlar için
    }
    
    const response = await openai.chat.completions.create(completionParams);

    const responseText = response.choices[0].message.content || "Yanıt oluşturulamadı.";
    
    // İşlem süresini hesapla
    const processingTime = (Date.now() - startTime) / 1000; // saniye cinsinden
    
    // Etkileşimi kaydet
    await storage.createAiInteractionHistory({
      userId: null, // Anonim kullanıcı
      query: prompt,
      response: responseText,
      memoryReferences: usedMemoryIds.length > 0 ? usedMemoryIds : null,
      feedbackRating: null, // Henüz geri bildirim yok
      processingTime
    });
    
    // Yeni bilgi olarak kaydet (varsayılan başarı oranı orta düzeyde)
    // Bu başarı oranı daha sonra kullanıcı geri bildirimleriyle güncellenebilir
    if (prompt.length > 10) { // Çok kısa sorguları kaydetme
      await storage.createAiLearningMemory({
        queryPattern: prompt.substring(0, 100), // İlk 100 karakter
        knowledgeStore: responseText.substring(0, 1000), // İlk 1000 karakter
        successMetric: 0.75, // Varsayılan başarı oranı (0-1 arası)
        frequencyUsed: 1 // İlk kullanım
      });
    }

    return responseText;
    
  } catch (error) {
    console.error("OpenAI yanıt hatası:", error);
    throw new Error("Yanıt alınırken bir hata oluştu: " + (error instanceof Error ? error.message : String(error)));
  }
}

export default {
  analyzeDocument,
  generateTestScenarios,
  clarifyRequirement,
  validateTestScenario,
  validateDocumentCoverage,
  getAIAssistantResponse
};