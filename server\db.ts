import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import ws from "ws";
import * as schema from "@shared/schema";

// Set up WebSocket constructor
neonConfig.webSocketConstructor = ws;

// Configure any additional Neon options if needed
// Note: Advanced WebSocket configuration options may not be supported
// in the current version of the Neon library

if (!process.env.DATABASE_URL) {
  console.warn("DATABASE_URL ayarlanmamış. Veritabanı bağlantısı kurulamayabilir!");
}

// Create a connection pool with appropriate settings
export const pool = new Pool({ 
  connectionString: process.env.DATABASE_URL,
  // More balanced timeout values
  connectionTimeoutMillis: 10000, // 10 seconds to connect
  idleTimeoutMillis: 60000, // Close idle connections after 1 minute
  // Keep connection pool smaller but sufficient
  max: 10,
  // Set max uses for each client to prevent resource leaks
  maxUses: 500,
  // Allow connections to exit when idle
  allowExitOnIdle: true
});

// Add error handler to prevent crashes on connection issues
pool.on('error', (err) => {
  console.error('Unexpected database pool error:', err);
  // Log but don't crash the application
});

// Wrap pool in drizzle ORM
export const db = drizzle(pool, { schema });

// Add graceful shutdown handler
process.on('SIGTERM', async () => {
  console.log('Closing database pool connections...');
  await pool.end();
  console.log('Database pool connections closed.');
});