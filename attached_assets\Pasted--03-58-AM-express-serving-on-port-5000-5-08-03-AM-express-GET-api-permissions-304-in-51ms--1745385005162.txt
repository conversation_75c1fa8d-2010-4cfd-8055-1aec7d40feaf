:03:58 AM [express] serving on port 5000
5:08:03 AM [express] GET /api/permissions 304 in 51ms :: ["user_management","project_management","do…
5:08:03 AM [express] GET /api/user-projects 304 in 56ms :: [{"userId":1,"projectId":1,"role":"owner"…
5:08:03 AM [express] GET /api/ai-models 304 in 25ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
5:08:03 AM [express] GET /api/documents 200 in 207ms :: [{"id":42,"name":"test iÃ§erik.txt","type":"…
5:08:08 AM [express] GET /api/documents/45/requirements 200 in 57ms :: []
5:08:08 AM [express] GET /api/documents/45 200 in 156ms :: {"id":45,"name":"SmartOpsFaz12AnalizDokum…
5:08:08 AM [express] GET /api/documents/45/ai-analysis 404 in 61ms :: {"error":"<PERSON>u doküman için AI a…
5:08:08 AM [express] GET /api/documents/45/components 200 in 58ms :: []
5:08:08 AM [express] GET /api/documents/45/api-endpoints 200 in 75ms :: []
5:08:09 AM [express] GET /api/documents/45/ai-analysis 404 in 46ms :: {"error":"Bu doküman için AI a…
5:08:12 AM [express] GET /api/documents/45/ai-analysis 404 in 53ms :: {"error":"Bu doküman için AI a…
5:08:16 AM [express] GET /api/documents/45/ai-analysis 404 in 52ms :: {"error":"Bu doküman için AI a…
PostgreSQL: Doküman ve ilişkili tüm veriler silindi: ID=45
5:08:17 AM [express] DELETE /api/documents/45 200 in 359ms :: {"success":true,"message":"Doküman baş…
5:08:18 AM [express] GET /api/documents 200 in 81ms :: [{"id":42,"name":"test iÃ§erik.txt","type":"t…
5:08:19 AM [express] GET /api/documents/42/requirements 200 in 45ms :: [{"id":470,"documentId":42,"c…
5:08:19 AM [express] GET /api/documents/42/ai-analysis 200 in 40ms :: {"id":47,"documentId":42,"obse…
5:08:19 AM [express] GET /api/documents/42 200 in 56ms :: {"id":42,"name":"test iÃ§erik.txt","type":…
5:08:19 AM [express] GET /api/documents/42/api-endpoints 200 in 52ms :: [{"id":161,"documentId":42,"…
5:08:19 AM [express] GET /api/documents/42/components 200 in 57ms :: [{"id":257,"documentId":42,"nam…
5:08:20 AM [express] GET /api/documents/44/ai-analysis 304 in 49ms :: {"id":49,"documentId":44,"obse…
5:08:20 AM [express] GET /api/documents/44/components 304 in 59ms :: [{"id":271,"documentId":44,"nam…
5:08:20 AM [express] GET /api/documents/44/requirements 304 in 65ms :: [{"id":493,"documentId":44,"c…
5:08:20 AM [express] GET /api/documents/44/api-endpoints 304 in 59ms :: [{"id":167,"documentId":44,"…
5:08:20 AM [express] GET /api/documents/44 304 in 157ms :: {"id":44,"name":"SmartOpsFaz12AnalizDokum…
PostgreSQL: Doküman ve ilişkili tüm veriler silindi: ID=44
5:08:25 AM [express] DELETE /api/documents/44 200 in 342ms :: {"success":true,"message":"Doküman baş…
5:08:25 AM [express] GET /api/documents 200 in 46ms :: [{"id":42,"name":"test iÃ§erik.txt","type":"t…
Doküman yükleniyor. Proje ID: Proje seçilmedi
Doküman işleniyor. Dosya türü: docx, Görsel analizi: Aktif
DOCX processing: Document might contain images, checking content...
DOCX processing: Image references found in the document. Types: şekil, tablo
Görsel içerik analizi başlatılıyor. Doküman metni analiz ediliyor...
o4 modeli için OpenAI API anahtarı: ******Gwegj
Görsel içerik analizi için kullanılan model: o4
Görsel analizi için aranan görsel tipleri: şekil, tablo, diagram, resim
OpenAI ile görsel içerik analizi hatası: BadRequestError: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.
    at Function.generate (/home/<USER>/workspace/node_modules/openai/src/error.ts:72:14)
    at OpenAI.makeStatusError (/home/<USER>/workspace/node_modules/openai/src/core.ts:462:21)
    at OpenAI.makeRequest (/home/<USER>/workspace/node_modules/openai/src/core.ts:526:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async analyzeVisualContent (/home/<USER>/workspace/server/services/openai.ts:276:22)
    at async Object.processDocument (/home/<USER>/workspace/server/services/document-processor.ts:252:30)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:831:31) {
  status: 400,
  headers: {
    'apim-request-id': 'b13cbee7-3105-40e1-b75f-c90ae69611e6',
    'azureml-model-session': 'v20250411-1-167922813',
    'content-length': '219',
    'content-type': 'application/json',
    date: 'Wed, 23 Apr 2025 05:08:31 GMT',
    'ms-azureml-model-error-reason': 'model_error',
    'ms-azureml-model-error-statuscode': '400',
    'ms-azureml-model-time': '20',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    'x-aml-cluster': 'hyena-japaneast-01',
    'x-content-type-options': 'nosniff',
    'x-envoy-upstream-service-time': '21',
    'x-ms-client-request-id': 'b13cbee7-3105-40e1-b75f-c90ae69611e6',
    'x-ms-deployment-name': 'gpt-4o',
    'x-ms-rai-invoked': 'true',
    'x-ms-region': 'East US',
    'x-ratelimit-limit-requests': '450',
    'x-ratelimit-limit-tokens': '450000',
    'x-ratelimit-remaining-requests': '449',
    'x-ratelimit-remaining-tokens': '445180',
    'x-request-id': '3f97dcca-61a5-40df-afbd-4281716262b2'
  },
  request_id: '3f97dcca-61a5-40df-afbd-4281716262b2',
  error: {
    message: "'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.",
    type: 'invalid_request_error',
    param: 'messages',
    code: null
  },
  code: null,
  param: 'messages',
  type: 'invalid_request_error'
}
o1/o3 modelleri için Azure OpenAI API anahtarı: ******GrPml
5:09:25 AM [express] POST /api/documents/upload 201 in 55234ms :: {"document":{"id":46,"name":"Smart…
5:09:26 AM [express] GET /api/documents 200 in 162ms :: [{"id":42,"name":"test iÃ§erik.txt","type":"…
5:09:30 AM [express] GET /api/documents/46 200 in 162ms :: {"id":46,"name":"SmartOpsFaz12AnalizDokum…
5:09:31 AM [express] GET /api/documents/46/components 200 in 47ms :: [{"id":277,"documentId":46,"nam…
5:09:31 AM [express] GET /api/documents/46/ai-analysis 200 in 45ms :: {"id":50,"documentId":46,"obse…
5:09:31 AM [express] GET /api/documents/46/requirements 200 in 45ms :: [{"id":502,"documentId":46,"c…
5:09:31 AM [express] GET /api/documents/46/api-endpoints 200 in 49ms :: [{"id":169,"documentId":46,"…
5:09:53 AM [express] GET /api/documents/46/test-scenarios 200 in 58ms :: []
