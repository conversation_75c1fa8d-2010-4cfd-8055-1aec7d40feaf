import { useAuth } from "@/hooks/use-auth";
import { usePermission, SYSTEM_PERMISSIONS } from "@/hooks/use-permission";
import { Loader2 } from "lucide-react";
import { Redirect, Route } from "wouter";

export function ProtectedRoute({
  path,
  component: Component,
  requiredPermission
}: {
  path: string;
  component: () => React.JSX.Element;
  requiredPermission?: string;
}) {
  const { user, isLoading: authLoading } = useAuth();
  const { hasPermission, loading: permissionLoading } = usePermission();
  const isLoading = authLoading || permissionLoading;

  if (isLoading) {
    return (
      <Route path={path}>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </Route>
    );
  }

  if (!user) {
    return (
      <Route path={path}>
        <Redirect to="/auth" />
      </Route>
    );
  }

  // Eğer belirli bir izin gerekliyse ve kullanıcı bu izne sahip değilse
  if (requiredPermission && !hasPermission(requiredPermission)) {
    return (
      <Route path={path}>
        <div className="flex flex-col items-center justify-center min-h-screen gap-4">
          <h1 className="text-2xl font-bold text-destructive">Yetkisiz Erişim</h1>
          <p className="text-muted-foreground">Bu sayfaya erişim için gerekli yetkiye sahip değilsiniz.</p>
          <Redirect to="/" />
        </div>
      </Route>
    );
  }

  return <Route path={path} component={Component} />;
}