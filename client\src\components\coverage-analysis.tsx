import { useState, useEffect } from "react";
import { type Requirement, type TestScenario } from "@shared/schema";
import { Tooltip } from "@/components/ui/tooltip";
import {
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { AiLearningDialog } from "@/components/ai-learning-dialog";
import { 
  PieChart, 
  Pie, 
  Cell, 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip as RechartTooltip,
  Legend,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from "recharts";
import { useToast } from "@/hooks/use-toast";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { validateDocumentCoverage, getCoverageValidation, deleteCoverageValidation } from "@/lib/api";

interface CoverageAnalysisProps {
  requirements: Requirement[];
  testScenarios: TestScenario[];
}

interface CoverageValidationResult {
  id?: number;
  coverageRate: number;
  missingRequirements: string[];
  analysisDetails?: {
    completeness: number;
    accuracy: number;
    clarity: number;
    maintainability: number;
    traceability: number;
  };
  recommendations?: string[];
  weakPoints?: string[];
  strongPoints?: string[];
  createdAt?: Date | string | null;
}

const CoverageAnalysis = ({ requirements, testScenarios }: CoverageAnalysisProps) => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [coverageValidation, setCoverageValidation] = useState<CoverageValidationResult | null>(null);
  const [analysisStarted, setAnalysisStarted] = useState<boolean>(false);
  const [selectedRequirement, setSelectedRequirement] = useState<Requirement | null>(null);
  const [isLearningDialogOpen, setIsLearningDialogOpen] = useState(false);
  const [isProcessDialogOpen, setIsProcessDialogOpen] = useState(false);
  const { toast } = useToast();
  const documentId = requirements.length > 0 ? requirements[0].documentId : null;
  
  // Veritabanından kapsam analizi sonuçlarını getir
  const { data: savedValidation, isLoading: isLoadingValidation } = useQuery({
    queryKey: [`/api/documents/${documentId}/coverage-validation`],
    queryFn: async () => {
      if (!documentId) return null;
      const response = await getCoverageValidation(documentId);
      const data = await response.json();
      return data;
    },
    enabled: !!documentId, // document id varsa etkinleştir
  });
  
  // Kayıtlı kapsam analizi sonuçlarını state'e yükle
  useEffect(() => {
    if (savedValidation) {
      setCoverageValidation(savedValidation);
      setAnalysisStarted(true);
    }
  }, [savedValidation]);

  // Calculate coverage metrics - test senaryolarıyla kapsanan gereksinimleri bul
  const coveredRequirements = requirements.filter((req) => {
    // Eğer gereksinim kodu yoksa geçersiz
    if (!req.code) return false;
    
    // Bu gereksinime bağlı test senaryolarını bul
    return testScenarios.some((scenario) => {
      // Eğer senaryo gereksinim kodu yoksa eşleşme yok
      if (!scenario.requirementCode) return false;
      
      // Tam eşleşme (gereksinim kodu ile doğrudan eşleşme)
      if (scenario.requirementCode === req.code) return true;
      
      // Kısmi eşleşme (virgülle ayrılmış liste içinde yer alma)
      if (scenario.requirementCode.includes(',')) {
        const reqCodes = scenario.requirementCode.split(',').map(code => code.trim());
        return reqCodes.includes(req.code);
      }
      
      // Basit içerme kontrolü (test senaryosu'nun gereksinim kodu içinde gereksinim kodu var mı)
      return scenario.requirementCode.includes(req.code);
    });
  });
  
  // AI kapsam analizi mutasyonu
  const coverageValidationMutation = useMutation({
    mutationFn: async (documentId: number) => {
      const response = await apiRequest("POST", `/api/documents/${documentId}/validate-coverage`);
      return response.json();
    },
    onSuccess: (data: CoverageValidationResult) => {
      setCoverageValidation(data);
      setIsProcessDialogOpen(false); // İşlem tamamlandığında diyaloğu kapat
      toast({
        title: "Kapsam analizi tamamlandı",
        description: "Test senaryoları ve gereksinimler analiz edildi",
        variant: "default",
      });
    },
    onError: (error: Error) => {
      setIsProcessDialogOpen(false); // Hata durumunda da diyaloğu kapat
      toast({
        title: "Analiz hatası",
        description: `Kapsam analizi yapılırken bir hata oluştu: ${error.message}`,
        variant: "destructive",
      });
    },
    onSettled: () => {
      setIsAnalyzing(false);
    }
  });
  
  // Anlık test senaryolarına göre kapsam oranını hesapla - tutarlı ölçüm için
  const calculateCoverageRate = () => {
    if (requirements.length === 0) return 0;
    
    // Kapsanan gereksinimler sayısı
    const coveredReqCount = coveredRequirements.length;
    
    // Toplam gereksinim sayısı
    const totalReqCount = requirements.length;
    
    // Kapsama kalitesi faktörü hesapla
    // Her gereksinimin en az 1 test senaryosu tarafından kapsanması
    const coverageQualityFactor = testScenarios.length > 0 ? 
      Math.min(1, testScenarios.length / Math.max(1, requirements.length)) : 0;
    
    // Basit kapsama oranını hesapla (kapsanan / toplam)
    let simpleCoverage = coveredReqCount / totalReqCount;
    
    // Kalite faktörünü ekleyerek daha gerçekçi bir oran elde et
    // (test senaryosu olmaması durumunda kapsam oranı 0 olmalı)
    const adjustedCoverage = testScenarios.length > 0 ? 
      simpleCoverage * (0.8 + 0.2 * coverageQualityFactor) : 0;
    
    return adjustedCoverage; 
  };

  // Analizi temizleme/yeniden başlatma mutasyonu
  const resetCoverageAnalysisMutation = useMutation({
    mutationFn: async (documentId: number) => {
      return await deleteCoverageValidation(documentId);
    },
    onSuccess: () => {
      // Analiz sonuçlarını sıfırla
      setCoverageValidation(null);
      setAnalysisStarted(false);
      // Query cache'i güncelle
      if (documentId) {
        queryClient.invalidateQueries({
          queryKey: [`/api/documents/${documentId}/coverage-validation`],
        });
      }
      toast({
        title: "Analiz sıfırlandı",
        description: "Kapsam analizi sonuçları silindi. Yeni analiz yapabilirsiniz.",
        variant: "default",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Sıfırlama hatası",
        description: `Kapsam analizi sıfırlanırken bir hata oluştu: ${error.message}`,
        variant: "destructive",
      });
    },
    onSettled: () => {
      setIsResetting(false);
    }
  });

  // Analizi sıfırla ve yeniden başlat
  const resetAnalysis = () => {
    if (!documentId) {
      toast({
        title: "Doküman bulunamadı",
        description: "Analizi sıfırlamak için geçerli bir doküman seçili olmalıdır",
        variant: "destructive",
      });
      return;
    }
    
    setIsResetting(true);
    resetCoverageAnalysisMutation.mutate(documentId);
  };

  // Analizi başlat
  const startCoverageAnalysis = () => {
    if (requirements.length === 0 || testScenarios.length === 0) {
      toast({
        title: "Analiz yapılamıyor",
        description: "Kapsam analizi için gereksinim ve test senaryoları gereklidir",
        variant: "destructive",
      });
      return;
    }

    // Bir doküman seçilmiş olmalı
    if (documentId) {
      setIsAnalyzing(true);
      setAnalysisStarted(true); // Analiz başlatıldı
      setIsProcessDialogOpen(true); // Process diyaloğunu aç
      
      // Analiz işlemini başlat
      coverageValidationMutation.mutate(documentId);
    } else {
      toast({
        title: "Doküman bulunamadı",
        description: "Kapsam analizi için geçerli bir doküman seçili olmalıdır",
        variant: "destructive",
      });
    }
  };
  
  // AI'dan veya hesaplanan kapsam oranını al
  const coverageRate = coverageValidation 
    ? coverageValidation.coverageRate
    : calculateCoverageRate();
  
  // Yüzde olarak ifade et
  const coveragePercentage = Math.round(coverageRate * 100);
  
  // Radar chart data
  const radarData = coverageValidation?.analysisDetails 
    ? [
        {
          subject: "Bütünlük",
          value: coverageValidation.analysisDetails.completeness * 100,
          fullMark: 100,
        },
        {
          subject: "Doğruluk",
          value: coverageValidation.analysisDetails.accuracy * 100,
          fullMark: 100,
        },
        {
          subject: "Netlik",
          value: coverageValidation.analysisDetails.clarity * 100,
          fullMark: 100,
        },
        {
          subject: "Bakım",
          value: coverageValidation.analysisDetails.maintainability * 100,
          fullMark: 100,
        },
        {
          subject: "İzlenebilirlik",
          value: coverageValidation.analysisDetails.traceability * 100,
          fullMark: 100,
        },
      ]
    : [];
  
  // Data for pie chart - sunucudan gelen değerlere göre pasta grafik
  const pieData = coverageValidation 
    ? [
        { 
          name: "Kapsanan", 
          value: requirements.length - (coverageValidation.missingRequirements?.length || 0), 
          color: "#107C10" 
        },
        { 
          name: "Kapsanmayan", 
          value: coverageValidation.missingRequirements?.length || 0, 
          color: "#D83B01" 
        }
      ]
    : [
        { name: "Kapsanan", value: coveredRequirements.length, color: "#107C10" },
        { name: "Kapsanmayan", value: requirements.length - coveredRequirements.length, color: "#D83B01" }
      ];
  
  // Group test scenarios by requirement category
  const categoryCoverage: Record<string, { total: number; covered: number }> = {};
  
  // Initialize with all requirement categories
  requirements.forEach((req) => {
    const category = req.category || "Tanımlanmamış";
    if (!categoryCoverage[category]) {
      categoryCoverage[category] = { total: 0, covered: 0 };
    }
    categoryCoverage[category].total += 1;
  });
  
  // Count covered requirements by category
  // Kullanıcı kendi kapsamını başlatmadan - hesaplanan durumdan
  if (!coverageValidation) {
    coveredRequirements.forEach((req) => {
      const category = req.category || "Tanımlanmamış";
      categoryCoverage[category].covered += 1;
    });
  } 
  // AI analiz kapsamını kullan
  else {
    // AI analizi olan sunucudan gelen kapsamı kullan
    const missingCodes = coverageValidation.missingRequirements || [];
    
    // Eksik olmayan gereksinim kodlarını bul
    requirements.forEach((req) => {
      if (req.code && !missingCodes.includes(req.code)) {
        const category = req.category || "Tanımlanmamış";
        categoryCoverage[category].covered += 1;
      }
    });
  }
  
  // Prepare data for bar chart
  const barChartData = Object.entries(categoryCoverage).map(([category, data]) => ({
    name: category,
    Kapsanan: data.covered,
    Kapsanmayan: data.total - data.covered,
  }));

  // Öğrenme diyaloğunu kapat
  const closeLearningDialog = () => {
    setIsLearningDialogOpen(false);
    setSelectedRequirement(null);
  };

  return (
    <div className="flex-1 p-6 bg-white h-[calc(100vh-12rem)] overflow-y-auto">
      {/* AI Öğrenme Diyaloğu */}
      {selectedRequirement && (
        <AiLearningDialog
          isOpen={isLearningDialogOpen}
          onClose={closeLearningDialog}
          requirementCode={selectedRequirement.code || ""}
          requirementDescription={selectedRequirement.description || ""}
          documentId={documentId || 0}
          missingRequirements={coverageValidation?.missingRequirements || []}
          allRequirements={requirements}
        />
      )}
      
      {/* İşlem Diyaloğu - Kapsam Analizi Sırasında */}
      <Dialog open={isProcessDialogOpen} onOpenChange={(open) => !open && setIsProcessDialogOpen(false)}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Kapsam Analizi Yapılıyor</DialogTitle>
            <DialogDescription>
              Gereksinimler ve test senaryoları AI tarafından analiz ediliyor. Bu işlem senaryo sayısına göre uzun süremektedir.
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex flex-col items-center justify-center py-6">
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-primary"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <i className="fas fa-chart-pie text-primary/60"></i>
              </div>
            </div>
            
            <div className="mt-6 text-center">
              <h4 className="text-lg font-medium text-neutral-900">AI Analizi Yürütülüyor</h4>
              <p className="text-sm text-neutral-500 mt-2">
                Bu süreçte test senaryoları ve gereksinimler arasındaki ilişkiler belirleniyor, kapsam oranı ve kalite metrikleri hesaplanıyor.
              </p>
            </div>
            
            <div className="w-full bg-neutral-200 rounded-full h-2 mt-5">
              <div className="bg-primary h-2 rounded-full animate-pulse"></div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      
      <div className="max-w-5xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-neutral-800">Test Kapsamı Analizi</h2>
          
          <div className="flex space-x-3">
            {/* Analiz Başlatma Butonu */}
            {requirements.length > 0 && testScenarios.length > 0 && !analysisStarted && (
              <Button 
                onClick={startCoverageAnalysis} 
                disabled={isAnalyzing || coverageValidationMutation.isPending}
                className="bg-primary hover:bg-primary/90 text-white"
              >
                {isAnalyzing || coverageValidationMutation.isPending ? (
                  <>
                    <i className="fas fa-circle-notch fa-spin mr-2"></i>
                    Analiz Yapılıyor...
                  </>
                ) : (
                  <>
                    <i className="fas fa-chart-pie mr-2"></i>
                    Kapsam Analizi Başlat
                  </>
                )}
              </Button>
            )}
            
            {/* Analizi Yeniden Başlat Butonu */}
            {requirements.length > 0 && testScenarios.length > 0 && analysisStarted && coverageValidation && (
              <Button 
                onClick={resetAnalysis} 
                disabled={isResetting || resetCoverageAnalysisMutation.isPending}
                className="bg-neutral-700 hover:bg-neutral-800 text-white"
              >
                {isResetting || resetCoverageAnalysisMutation.isPending ? (
                  <>
                    <i className="fas fa-circle-notch fa-spin mr-2"></i>
                    Sıfırlanıyor...
                  </>
                ) : (
                  <>
                    <i className="fas fa-redo-alt mr-2"></i>
                    Analizi Yeniden Başlat
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
        
        {requirements.length === 0 ? (
          <div className="text-center p-10 border-2 border-dashed border-neutral-300 rounded-lg">
            <i className="fas fa-chart-pie text-4xl text-neutral-400 mb-3"></i>
            <p className="text-neutral-600">Analiz için gereksinim bulunamadı</p>
            <p className="text-sm text-neutral-500 mt-2">Kapsam analizi için önce dokümanın analiz edilmesi ve gereksinimlerin tespit edilmesi gerekiyor</p>
          </div>
        ) : testScenarios.length === 0 ? (
          <div className="text-center p-10 border-2 border-dashed border-neutral-300 rounded-lg">
            <i className="fas fa-vial text-4xl text-neutral-400 mb-3"></i>
            <p className="text-neutral-600">Test senaryosu bulunamadı</p>
            <p className="text-sm text-neutral-500 mt-2">Kapsam analizi için test senaryolarının oluşturulması gerekiyor</p>
          </div>
        ) : !analysisStarted && !coverageValidation ? (
          <div className="text-center p-10 border-2 border-dashed border-neutral-300 rounded-lg">
            <i className="fas fa-chart-pie text-4xl text-neutral-400 mb-3"></i>
            <p className="text-neutral-600">Kapsam analizi henüz başlatılmadı</p>
            <p className="text-sm text-neutral-500 mt-2">Kapsam analizi sonuçlarını görmek için "Kapsam Analizi Başlat" butonunu kullanın</p>
          </div>
        ) : isAnalyzing ? (
          <div className="text-center p-10 border-2 border-dashed border-neutral-300 rounded-lg">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-3"></div>
            <p className="text-neutral-600">Kapsam analizi yapılıyor...</p>
            <p className="text-sm text-neutral-500 mt-2">Bu işlem bir kaç dakika sürebilir, lütfen bekleyin</p>
          </div>
        ) : (
          <>
            <div className="bg-white p-5 rounded-lg border border-neutral-200 shadow-sm mb-8">
              <div className="flex flex-col md:flex-row md:items-center justify-between mb-3">
                <h3 className="text-base font-semibold text-neutral-700">Analiz Özeti</h3>
                
                <div className="flex items-center gap-2">
                  {/* Öğrenme Belleği Bilgisi */}
                  <div className="mt-2 md:mt-0 flex items-center">
                    <i className="fas fa-graduation-cap mr-2 text-primary"></i>
                    <span className="text-sm text-neutral-600">
                      Kapsanmayan gereksinimler için <strong>AI'a Öğret</strong> butonunu kullanarak sistem öğrenim belleğine katkıda bulunabilirsiniz.
                    </span>
                  </div>
                  
                  {/* Toplu Öğrenme Butonu */}
                  {coverageValidation?.missingRequirements && coverageValidation.missingRequirements.length > 0 && (
                    <Button 
                      onClick={() => {
                        // Toplu mod etkin olacak şekilde diyaloğu aç
                        if (requirements.length > 0) {
                          setSelectedRequirement(requirements[0]);
                          setIsLearningDialogOpen(true);
                        }
                      }}
                      variant="outline"
                      size="sm"
                      className="text-xs whitespace-nowrap"
                    >
                      <i className="fas fa-graduation-cap mr-1"></i>
                      Toplu AI Öğretimi
                    </Button>
                  )}
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-neutral-50 p-4 rounded-lg border border-neutral-200">
                  <h3 className="text-sm font-medium text-neutral-500 mb-1">Toplam Gereksinim</h3>
                  <p className="text-3xl font-semibold text-neutral-900">{requirements.length}</p>
                  <div className="flex justify-between mt-2 text-xs text-neutral-500">
                    <span>{coveredRequirements.length} kapsanan</span>
                    <span>{requirements.length - coveredRequirements.length} kapsanmayan</span>
                  </div>
                </div>
                
                <div className="bg-neutral-50 p-4 rounded-lg border border-neutral-200">
                  <h3 className="text-sm font-medium text-neutral-500 mb-1">Kapsam Oranı</h3>
                  {/* Genel Kapsam Durumundan alınacak doğru kapsam oranı */}
                  <p className={`text-3xl font-semibold ${
                    // coverageValidation varsa pieData'dan, yoksa hesaplanmış değerden al
                    (coverageValidation ? 
                      (pieData[0].value / (pieData[0].value + pieData[1].value) * 100) : 
                      coveragePercentage) >= 90 ? 'text-green-600' : 
                    (coverageValidation ? 
                      (pieData[0].value / (pieData[0].value + pieData[1].value) * 100) : 
                      coveragePercentage) >= 75 ? 'text-amber-600' : 'text-red-600'
                  }`}>
                    {coverageValidation ? 
                      // Pie chart verisinden kapsama oranını hesapla (doğru oran)
                      Math.round(pieData[0].value / (pieData[0].value + pieData[1].value) * 100) : 
                      coveragePercentage}%
                  </p>
                  <div className="mt-2 w-full bg-neutral-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        (coverageValidation ? 
                          (pieData[0].value / (pieData[0].value + pieData[1].value) * 100) : 
                          coveragePercentage) >= 90 ? 'bg-green-500' : 
                        (coverageValidation ? 
                          (pieData[0].value / (pieData[0].value + pieData[1].value) * 100) : 
                          coveragePercentage) >= 70 ? 'bg-amber-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${
                        coverageValidation ? 
                          Math.min(100, Math.round(pieData[0].value / (pieData[0].value + pieData[1].value) * 100)) : 
                          Math.min(100, coveragePercentage)
                      }%` }}
                    ></div>
                  </div>
                </div>
                
                <div className="bg-neutral-50 p-4 rounded-lg border border-neutral-200">
                  <h3 className="text-sm font-medium text-neutral-500 mb-1">Test Senaryosu</h3>
                  <p className="text-3xl font-semibold text-neutral-900">{testScenarios.length}</p>
                  <div className="flex justify-between mt-2 text-xs text-neutral-500">
                    <span>Senaryo/Gereksinim: {(testScenarios.length / Math.max(1, requirements.length)).toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              {/* Pie Chart */}
              <div className="bg-white p-4 rounded-lg border border-neutral-200 shadow-sm">
                <h3 className="text-base font-medium text-neutral-700 mb-4">Genel Kapsam Durumu</h3>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={pieData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {pieData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>
              
              {/* Bar Chart */}
              <div className="bg-white p-4 rounded-lg border border-neutral-200 shadow-sm">
                <h3 className="text-base font-medium text-neutral-700 mb-4">Kategori Bazlı Kapsam</h3>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={barChartData}
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="name" type="category" width={120} />
                      <RechartTooltip />
                      <Legend />
                      <Bar dataKey="Kapsanan" fill="#107C10" stackId="a" />
                      <Bar dataKey="Kapsanmayan" fill="#D83B01" stackId="a" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
            
            {/* Analysis Results */}
            {coverageValidation?.analysisDetails ? (
              <>
                <div className="bg-white p-4 rounded-lg border border-neutral-200 shadow-sm mb-8">
                  <h3 className="text-base font-medium text-neutral-700 mb-4">Detaylı Analiz Kalite Metrikleri</h3>
                  
                  <div className="h-72">
                    <ResponsiveContainer width="100%" height="100%">
                      <RadarChart cx="50%" cy="50%" outerRadius="80%" data={radarData}>
                        <PolarGrid />
                        <PolarAngleAxis dataKey="subject" />
                        <PolarRadiusAxis angle={30} domain={[0, 100]} />
                        <Radar
                          name="Test Senaryoları"
                          dataKey="value"
                          stroke="#0078D4"
                          fill="#0078D4"
                          fillOpacity={0.5}
                        />
                        <Legend />
                      </RadarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                  {/* Strengths */}
                  {coverageValidation.strongPoints && coverageValidation.strongPoints.length > 0 && (
                    <div className="bg-white p-4 rounded-lg border border-neutral-200 shadow-sm">
                      <h3 className="text-base font-medium text-green-700 mb-4">
                        <i className="fas fa-check-circle mr-2"></i>
                        Güçlü Noktalar
                      </h3>
                      <ul className="list-disc pl-5 space-y-2 text-sm text-neutral-700">
                        {coverageValidation.strongPoints.map((point, index) => (
                          <li key={index}>{point}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {/* Weaknesses */}
                  {coverageValidation.weakPoints && coverageValidation.weakPoints.length > 0 && (
                    <div className="bg-white p-4 rounded-lg border border-neutral-200 shadow-sm">
                      <h3 className="text-base font-medium text-red-700 mb-4">
                        <i className="fas fa-exclamation-triangle mr-2"></i>
                        İyileştirme Alanları
                      </h3>
                      <ul className="list-disc pl-5 space-y-2 text-sm text-neutral-700">
                        {coverageValidation.weakPoints.map((point, index) => (
                          <li key={index}>{point}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
                
                {/* Recommendations */}
                {coverageValidation.recommendations && coverageValidation.recommendations.length > 0 && (
                  <div className="bg-white p-4 rounded-lg border border-neutral-200 shadow-sm mb-8">
                    <h3 className="text-base font-medium text-neutral-700 mb-4">
                      <i className="fas fa-lightbulb mr-2 text-amber-500"></i>
                      İyileştirme Önerileri
                    </h3>
                    <ul className="list-decimal pl-5 space-y-3 text-sm text-neutral-700">
                      {coverageValidation.recommendations.map((rec, index) => (
                        <li key={index} className="pb-2 border-b border-neutral-100">{rec}</li>
                      ))}
                    </ul>
                  </div>
                )}
                
                {/* Missing Requirements */}
                {coverageValidation.missingRequirements && coverageValidation.missingRequirements.length > 0 && (
                  <div className="bg-white p-4 rounded-lg border border-neutral-200 shadow-sm mb-8">
                    <h3 className="text-base font-medium text-red-700 mb-4">
                      <i className="fas fa-clipboard-list mr-2"></i>
                      Kapsanmayan Gereksinimler
                    </h3>
                    <div className="overflow-x-auto">
                      <table className="min-w-full border-collapse">
                        <thead>
                          <tr className="bg-neutral-100">
                            <th className="px-4 py-2 text-left text-sm font-medium text-neutral-700 border border-neutral-200">Kod</th>
                            <th className="px-4 py-2 text-left text-sm font-medium text-neutral-700 border border-neutral-200">Gereksinim</th>
                          </tr>
                        </thead>
                        <tbody>
                          {coverageValidation.missingRequirements.map((reqCode, index) => {
                            const req = requirements.find(r => r.code === reqCode);
                            return req ? (
                              <tr key={index} className="hover:bg-neutral-50">
                                <td className="px-4 py-2 text-sm border border-neutral-200">{req.code}</td>
                                <td className="px-4 py-2 text-sm border border-neutral-200">{req.description}</td>
                              </tr>
                            ) : null;
                          })}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <>
                {/* Kapsam durumu özeti - AI analizi yokken gösterilir */}
                <div className="bg-white p-4 rounded-lg border border-neutral-200 shadow-sm mb-8">
                  <h3 className="text-base font-medium text-neutral-700 mb-4">
                    <div className="flex justify-between items-center">
                      <span>Kapsam Analizi Özeti</span>
                      <span className="text-sm text-neutral-500 font-normal italic">
                        Daha detaylı analiz için "Kapsam Analizi Başlat" butonunu kullanın
                      </span>
                    </div>
                  </h3>
                  
                  {/* Kapsam durumu özeti */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-5">
                    {/* Kapsam durumu özeti */}
                    <div className="bg-neutral-50 p-4 rounded-lg border border-neutral-200">
                      <h4 className="text-sm font-medium text-neutral-600 mb-3">Genel Kapsam Durumu</h4>
                      <div className="mb-3">
                        <p className="text-3xl font-semibold text-neutral-900">{coveragePercentage}%</p>
                        <div className="flex justify-between mt-2 text-xs text-neutral-500">
                          <span>{coveredRequirements.length} / {requirements.length} gereksinim</span>
                          <span>{testScenarios.length} test senaryosu</span>
                        </div>
                      </div>
                      
                      <div className="w-full bg-neutral-200 rounded-full h-2.5">
                        <div 
                          className={`h-2.5 rounded-full ${
                            coveragePercentage >= 90 ? 'bg-green-500' : 
                            coveragePercentage >= 70 ? 'bg-amber-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${Math.min(100, coveragePercentage)}%` }}
                        ></div>
                      </div>
                    </div>
                    
                    {/* Kategorik dağılım */}
                    <div className="bg-neutral-50 p-4 rounded-lg border border-neutral-200">
                      <h4 className="text-sm font-medium text-neutral-600 mb-3">Kapsam Dağılımı</h4>
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                          <span className="text-sm">{coveredRequirements.length} Kapsanan</span>
                        </div>
                        <span className="text-xs text-neutral-500">
                          {Math.round((coveredRequirements.length / Math.max(1, requirements.length)) * 100)}%
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                          <span className="text-sm">{requirements.length - coveredRequirements.length} Kapsanmayan</span>
                        </div>
                        <span className="text-xs text-neutral-500">
                          {Math.round(((requirements.length - coveredRequirements.length) / Math.max(1, requirements.length)) * 100)}%
                        </span>
                      </div>
                      
                      <div className="mt-3 flex h-6 overflow-hidden rounded-md">
                        <div 
                          className="bg-green-500 flex-grow" 
                          style={{ width: `${Math.round((coveredRequirements.length / Math.max(1, requirements.length)) * 100)}%` }}>
                        </div>
                        <div 
                          className="bg-red-500 flex-grow" 
                          style={{ width: `${Math.round(((requirements.length - coveredRequirements.length) / Math.max(1, requirements.length)) * 100)}%` }}>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Varsayılan iyileştirme önerileri - gereksinim ve senaryo analizine dayanır */}
                  <div className="border-t border-neutral-200 pt-4 mt-2">
                    <h4 className="text-sm font-medium text-neutral-700 mb-3">
                      <i className="fas fa-lightbulb mr-2 text-amber-500"></i>
                      Geliştirme Önerileri
                    </h4>
                    
                    <ul className="list-disc pl-5 space-y-2 text-sm text-neutral-600">
                      {requirements.length - coveredRequirements.length > 0 && (
                        <li>Kapsanmayan {requirements.length - coveredRequirements.length} gereksinim için test senaryoları oluşturun</li>
                      )}
                      
                      {testScenarios.filter(s => !s.requirementCode).length > 0 && (
                        <li>{testScenarios.filter(s => !s.requirementCode).length} test senaryosunun gereksinim bağlantısı eksik. Test senaryolarına gereksinim kodu atayın</li>
                      )}
                      
                      {requirements.some(r => testScenarios.filter(s => s.requirementCode === r.code).length < 2) && (
                        <li>Her gereksinim için en az 2 test senaryosu oluşturulması önerilir</li>
                      )}
                      
                      <li>Test senaryolarında sınır değerler ve negatif test senaryoları ekleyerek kapsam kalitesini artırın</li>
                      
                      {testScenarios.filter(s => !s.expectedResults).length > 0 && (
                        <li>{testScenarios.filter(s => !s.expectedResults).length} test senaryosunun beklenen sonuçları eksik. Tüm senaryolar için beklenen sonuçları tanımlayın</li>
                      )}
                      
                      <li>Detaylı analiz için AI destekli kapsam analizi çalıştırın</li>
                    </ul>
                  </div>
                </div>
                
                {/* Kapsanmayan Gereksinimler - Otomatik hesaplama */}
                {requirements.length - coveredRequirements.length > 0 && (
                  <div className="bg-white p-4 rounded-lg border border-neutral-200 shadow-sm mb-8">
                    <h3 className="text-base font-medium text-red-700 mb-4">
                      <i className="fas fa-clipboard-list mr-2"></i>
                      Kapsanmayan Gereksinimler
                    </h3>
                    <div className="overflow-x-auto">
                      <table className="min-w-full border-collapse">
                        <thead>
                          <tr className="bg-neutral-100">
                            <th className="px-4 py-2 text-left text-sm font-medium text-neutral-700 border border-neutral-200">Kod</th>
                            <th className="px-4 py-2 text-left text-sm font-medium text-neutral-700 border border-neutral-200">Gereksinim</th>
                            <th className="px-4 py-2 text-left text-sm font-medium text-neutral-700 border border-neutral-200">Kategori</th>
                          </tr>
                        </thead>
                        <tbody>
                          {requirements.filter(req => !coveredRequirements.includes(req)).map((req) => (
                            <tr key={req.id} className="hover:bg-neutral-50">
                              <td className="px-4 py-2 text-sm border border-neutral-200">{req.code}</td>
                              <td className="px-4 py-2 text-sm border border-neutral-200">{req.description}</td>
                              <td className="px-4 py-2 text-sm border border-neutral-200">{req.category || "Tanımlanmamış"}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </>
            )}
            
            {/* Requirements List */}
            <div className="mt-8 bg-white p-4 rounded-lg border border-neutral-200 shadow-sm">
              <h3 className="text-base font-medium text-neutral-700 mb-4">Gereksinim Kapsam Detayları</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full border-collapse">
                  <thead>
                    <tr className="bg-neutral-100">
                      <th className="px-4 py-2 text-left text-sm font-medium text-neutral-700 border border-neutral-200">Kod</th>
                      <th className="px-4 py-2 text-left text-sm font-medium text-neutral-700 border border-neutral-200">Gereksinim</th>
                      <th className="px-4 py-2 text-left text-sm font-medium text-neutral-700 border border-neutral-200">Kategori</th>
                      <th className="px-4 py-2 text-left text-sm font-medium text-neutral-700 border border-neutral-200">Durum</th>
                    </tr>
                  </thead>
                  <tbody>
                    {requirements.map((requirement) => {
                      let isCovered = false;
                      let scenarios = [];
                      
                      // Kapsamı doğru tespit etme
                      if (coverageValidation) {
                        // AI analizi varsa, missingRequirements listesine göre kontrol et
                        isCovered = !coverageValidation.missingRequirements.includes(requirement.code);
                        // Ek olarak, ilişkili senaryoları görüntülemek için
                        scenarios = testScenarios.filter(
                          (scenario) => 
                            scenario.requirementCode === requirement.code || 
                            (requirement.code && scenario.requirementCode && scenario.requirementCode.includes(requirement.code))
                        );
                      } else {
                        // Manuel hesaplama - test senaryolarından tespit
                        scenarios = testScenarios.filter(
                          (scenario) => 
                            scenario.requirementCode === requirement.code || 
                            (requirement.code && scenario.requirementCode && scenario.requirementCode.includes(requirement.code))
                        );
                        isCovered = scenarios.length > 0;
                      }
                      
                      return (
                        <tr key={requirement.id} className="hover:bg-neutral-50">
                          <td className="px-4 py-2 text-sm border border-neutral-200">{requirement.code}</td>
                          <td className="px-4 py-2 text-sm border border-neutral-200">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="truncate max-w-xs cursor-help">
                                    {requirement.description}
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p className="max-w-sm">{requirement.description}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </td>
                          <td className="px-4 py-2 text-sm border border-neutral-200">{requirement.category || "Tanımlanmamış"}</td>
                          <td className="px-4 py-2 text-sm border border-neutral-200">
                            <div className="flex items-center justify-between">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${isCovered ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                {isCovered ? 'Kapsanıyor' : 'Kapsanmıyor'}
                              </span>
                              
                              {!isCovered && (
                                <button 
                                  onClick={() => {
                                    setSelectedRequirement(requirement);
                                    setIsLearningDialogOpen(true);
                                  }}
                                  className="text-xs text-primary hover:text-primary/80 font-medium whitespace-nowrap ml-2"
                                  title="AI'a bu gereksinim hakkında bilgi öğret"
                                >
                                  <i className="fas fa-graduation-cap mr-1"></i>
                                  AI'a Öğret
                                </button>
                              )}
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default CoverageAnalysis;