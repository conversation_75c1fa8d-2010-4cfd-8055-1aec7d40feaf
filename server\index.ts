import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import { setupAuth, hashPassword } from "./auth";
import { storage } from "./storage";

// Admin kullanıcısını oluşturan fonksiyon
async function createInitialAdmin() {
  try {
    // Admin kullanıcısı mevcutsa oluşturma
    const existingAdmin = await storage.getUserByUsername("netas");
    if (existingAdmin) {
      log("Admin kullanıcısı zaten mevcut: netas");
      return existingAdmin;
    }

    // Yeni admin kullanıcısı oluştur
    const hashedPassword = await hashPassword("netas-admin");
    const admin = await storage.createUser({
      username: "netas",
      password: hashedPassword, // password_hash olarak veritabanına kaydedilecek
      email: "<EMAIL>",
      role: "admin",
    });

    log(`<PERSON><PERSON> kullanıcısı başarıyla oluşturuldu: ${admin.username}`);
    return admin;
  } catch (error) {
    log(`Admin kullanıcısı oluşturulurken hata: ${error}`);
    throw error;
  }
}

const app = express();
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: false, limit: '50mb' }));

// Timeout değerini 60 dakikaya çıkar (3600000ms) - uzun süren AI işlemleri için
app.use((req, res, next) => {
  res.setTimeout(3600000, () => {
    console.log('Request has timed out after 60 minutes.');
    res.status(504).send('Request timeout - İşlem zaman aşımına uğradı (60 dakika)');
  });
  next();
});

// Auth sistemini kur
setupAuth(app);

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  // Varsayılan admin kullanıcısını oluştur
  try {
    await createInitialAdmin();
  } catch (error) {
    log(`Admin kullanıcısı başlatma hatası: ${error}`);
  }

  // Health check endpoint - sadece API sağlık kontrolü için
  app.get("/api/health", (_req, res) => {
    res.status(200).send("OK");
  });

  const server = await registerRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // ALWAYS serve the app on port 5000
  // this serves both the API and the client.
  // It is the only port that is not firewalled.
  const port = 5000;
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true,
  }, () => {
    log(`serving on port ${port}`);
  });
})();