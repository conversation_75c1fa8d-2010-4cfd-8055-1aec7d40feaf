5:51:18 PM [express] GET /api/projects/1/users 304 in 106ms :: [{"userId":1,"projectId":1,"role":"ow…
<PERSON><PERSON><PERSON><PERSON> yükleme hatası: error: column "status" of relation "documents" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NeonPreparedQuery.execute (/home/<USER>/workspace/node_modules/src/neon-serverless/session.ts:102:18)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:184:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:826:26) {
  length: 129,
  severity: 'ERROR',
  code: '42703',
  detail: undefined,
  hint: undefined,
  position: '122',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_target.c',
  line: '1066',
  routine: 'checkInsertTargets'
}
5:51:30 PM [express] POST /api/documents/upload 500 in 1780ms :: {"error":"Doküman yüklenirken hata …
Doküman yükleme hatası: error: column "status" of relation "documents" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NeonPreparedQuery.execute (/home/<USER>/workspace/node_modules/src/neon-serverless/session.ts:102:18)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:184:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:826:26) {
  length: 129,
  severity: 'ERROR',
  code: '42703',
  detail: undefined,
  hint: undefined,
  position: '122',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_target.c',
  line: '1066',
  routine: 'checkInsertTargets'
}
5:51:32 PM [express] POST /api/documents/upload 500 in 1236ms :: {"error":"Doküman yüklenirken hata …
Doküman yükleme hatası: error: column "status" of relation "documents" does not exist
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async NeonPreparedQuery.execute (/home/<USER>/workspace/node_modules/src/neon-serverless/session.ts:102:18)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:184:20)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:826:26) {
  length: 129,
  severity: 'ERROR',
  code: '42703',
  detail: undefined,
  hint: undefined,
  position: '122',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_target.c',
  line: '1066',
  routine: 'checkInsertTargets'
}
5:51:36 PM [express] POST /api/documents/upload 500 in 1101ms :: {"error":"Doküman yüklenirken hata …
