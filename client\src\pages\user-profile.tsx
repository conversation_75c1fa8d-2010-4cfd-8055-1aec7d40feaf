import { useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Loader2, UserCircle, Mail, Briefcase, Building } from "lucide-react";
import Header from "@/components/header";

export default function UserProfile() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: user?.name || "",
    email: user?.email || "",
    department: user?.department || "",
    jobTitle: user?.jobTitle || "",
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) return;
    
    setIsSubmitting(true);
    
    try {
      await apiRequest("PUT", `/api/users/${user.id}`, formData);
      
      queryClient.invalidateQueries({ queryKey: ["/api/user"] });
      
      toast({
        title: "Profil güncellendi",
        description: "Kullanıcı bilgileriniz başarıyla güncellendi.",
      });
      
      setIsEditing(false);
    } catch (error) {
      toast({
        title: "Güncelleme hatası",
        description: "Profil güncellenirken bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-neutral-50">
      <Header />
      
      <main className="container mx-auto p-4 pt-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Profil Bilgileri</h1>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-1">
            <Card>
              <CardHeader className="text-center">
                <div className="mx-auto bg-primary/10 w-24 h-24 rounded-full flex items-center justify-center mb-4">
                  <UserCircle className="w-16 h-16 text-primary" />
                </div>
                <CardTitle>{user?.name || user?.username}</CardTitle>
                <CardDescription>{user?.role === "admin" ? "Yönetici" : "Kullanıcı"}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2 text-center">
                <div className="flex items-center justify-center gap-2">
                  <Mail className="h-4 w-4 text-neutral-500" />
                  <span>{user?.email || "E-posta yok"}</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <Building className="h-4 w-4 text-neutral-500" />
                  <span>{user?.department || "Departman belirtilmedi"}</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <Briefcase className="h-4 w-4 text-neutral-500" />
                  <span>{user?.jobTitle || "Unvan belirtilmedi"}</span>
                </div>
              </CardContent>
              <CardFooter>
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => setIsEditing(!isEditing)}
                >
                  {isEditing ? "İptal Et" : "Profili Düzenle"}
                </Button>
              </CardFooter>
            </Card>
          </div>
          
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Profil Bilgileriniz</CardTitle>
                <CardDescription>
                  Kişisel bilgilerinizi güncelleyebilirsiniz. Bu bilgiler, sistemde size ait dokümanlar ve projelerle ilişkilendirilecektir.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Ad Soyad</Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder="Ad Soyad"
                      disabled={!isEditing || isSubmitting}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="email">E-posta Adresi</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="E-posta"
                      disabled={!isEditing || isSubmitting}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="department">Departman</Label>
                    <Input
                      id="department"
                      name="department"
                      value={formData.department}
                      onChange={handleInputChange}
                      placeholder="Departman"
                      disabled={!isEditing || isSubmitting}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="jobTitle">Unvan</Label>
                    <Input
                      id="jobTitle"
                      name="jobTitle"
                      value={formData.jobTitle}
                      onChange={handleInputChange}
                      placeholder="Unvan"
                      disabled={!isEditing || isSubmitting}
                    />
                  </div>
                  
                  {isEditing && (
                    <Button type="submit" disabled={isSubmitting} className="w-full">
                      {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Bilgileri Kaydet
                    </Button>
                  )}
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}