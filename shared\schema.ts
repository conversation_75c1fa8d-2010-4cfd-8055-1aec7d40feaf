import { pgTable, text, serial, integer, boolean, jsonb, timestamp, real, primaryKey } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";

// User schema
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  role: text("role").default("user").notNull(), // "admin" veya "user"
  name: text("name"),
  email: text("email"),
  department: text("department"), // Departman bilgisi
  jobTitle: text("job_title"), // İş unvanı
  status: text("status").default("active").notNull(), // "active" veya "inactive"
  lastLogin: timestamp("last_login"), // Son giriş zamanı
  createdAt: timestamp("created_at").defaultNow(),
});

// Kullanıcı izinleri - belgeler ve projeler üzerindeki yetkileri
export const permissions = pgTable("permissions", {
  id: serial("id").primaryKey(),
  name: text("name").notNull().unique(), // Yetki adı: DOCUMENT_CREATE, PROJECT_VIEW, vb.
  description: text("description"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Kullanıcı ile izinler arasındaki ilişki (Many-to-Many)
export const userPermissions = pgTable("user_permissions", {
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  permissionId: integer("permission_id").notNull().references(() => permissions.id, { onDelete: "cascade" }),
}, (t) => ({
  pk: primaryKey(t.userId, t.permissionId),
}));

// İşlem durumu tablosu - Süreçleri takip etmek için (Dosya yükleme, analiz vb)
export const processStatus = pgTable("process_status", {
  id: serial("id").primaryKey(),
  processType: text("process_type").notNull(), // upload, analyze, generate-scenarios
  status: text("status").notNull(), // pending, processing, completed, error
  documentId: integer("document_id"),
  projectId: integer("project_id").notNull(),
  progress: integer("progress").default(0),
  error: text("error"),
  message: text("message"),
  startedAt: timestamp("started_at").defaultNow(),
  completedAt: timestamp("completed_at"),
  createdBy: integer("created_by").notNull(),
  metadata: jsonb("metadata"),
});

export const insertProcessStatusSchema = createInsertSchema(processStatus).pick({
  processType: true,
  status: true,
  documentId: true,
  projectId: true,
  progress: true,
  error: true,
  message: true,
  createdBy: true,
  metadata: true,
});

export type InsertProcessStatus = z.infer<typeof insertProcessStatusSchema>;
export type ProcessStatus = typeof processStatus.$inferSelect;

// Projeler tablosu (dokümanlar projelere bağlı olacak)
export const projects = pgTable("projects", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  createdBy: integer("created_by").notNull().references(() => users.id),
  status: text("status").default("active").notNull(), // "active", "archived", "completed"
  automationEnabled: boolean("automation_enabled").default(false),
  jiraEnabled: boolean("jira_enabled").default(false),
  testRailEnabled: boolean("testrail_enabled").default(false),
  almEnabled: boolean("alm_enabled").default(false),
  visiumEnabled: boolean("visium_enabled").default(false),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Kullanıcı-Proje ilişkisi (Many-to-Many)
export const userProjects = pgTable("user_projects", {
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  projectId: integer("project_id").notNull().references(() => projects.id, { onDelete: "cascade" }),
  role: text("role").default("viewer").notNull(), // "owner", "editor", "viewer"
  createdAt: timestamp("created_at").defaultNow(),
}, (t) => ({
  pk: primaryKey(t.userId, t.projectId),
}));

// İlişkileri tanımlama
export const usersRelations = relations(users, ({ many }) => ({
  userProjects: many(userProjects),
  userPermissions: many(userPermissions),
}));

export const projectsRelations = relations(projects, ({ many }) => ({
  userProjects: many(userProjects),
  documents: many(documents),
}));

export const permissionsRelations = relations(permissions, ({ many }) => ({
  userPermissions: many(userPermissions),
}));

export const userProjectsRelations = relations(userProjects, ({ one }) => ({
  user: one(users, {
    fields: [userProjects.userId],
    references: [users.id],
  }),
  project: one(projects, {
    fields: [userProjects.projectId],
    references: [projects.id],
  }),
}));

export const userPermissionsRelations = relations(userPermissions, ({ one }) => ({
  user: one(users, {
    fields: [userPermissions.userId],
    references: [users.id],
  }),
  permission: one(permissions, {
    fields: [userPermissions.permissionId],
    references: [permissions.id],
  }),
}));

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
  role: true,
  name: true,
  email: true,
  department: true,
  jobTitle: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

export const insertProjectSchema = createInsertSchema(projects).pick({
  name: true,
  description: true,
  createdBy: true,
  status: true,
});

export type InsertProject = z.infer<typeof insertProjectSchema>;
export type Project = typeof projects.$inferSelect;

export const insertPermissionSchema = createInsertSchema(permissions).pick({
  name: true,
  description: true,
});

export type InsertPermission = z.infer<typeof insertPermissionSchema>;
export type Permission = typeof permissions.$inferSelect;

export const insertUserProjectSchema = createInsertSchema(userProjects).pick({
  userId: true,
  projectId: true,
  role: true,
});

export type InsertUserProject = z.infer<typeof insertUserProjectSchema>;
export type UserProject = typeof userProjects.$inferSelect;

// Document schema
export const documents = pgTable("documents", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  type: text("type").notNull(), // pdf, docx, etc.
  content: text("content").notNull(), // Document content as text
  originalContent: text("original_content"), // Original document content (base64)
  projectId: integer("project_id").references(() => projects.id, { onDelete: "set null" }),
  createdBy: integer("created_by").references(() => users.id),
  analyzedAt: timestamp("analyzed_at"),
  status: text("status").default("pending"), // pending, processing, completed, error
  createdAt: timestamp("created_at").defaultNow(),
});

export const documentsRelations = relations(documents, ({ one }) => ({
  project: one(projects, {
    fields: [documents.projectId],
    references: [projects.id],
  }),
  creator: one(users, {
    fields: [documents.createdBy],
    references: [users.id],
  }),
}));

export const insertDocumentSchema = createInsertSchema(documents).pick({
  name: true,
  type: true,
  content: true,
  originalContent: true,
  projectId: true,
  createdBy: true,
  status: true,
});

export type InsertDocument = z.infer<typeof insertDocumentSchema>;
export type Document = typeof documents.$inferSelect;

// Component schema (document components like modules, systems, etc.)
export const components = pgTable("components", {
  id: serial("id").primaryKey(),
  documentId: integer("document_id").notNull(),
  name: text("name").notNull(),
  description: text("description"),
  type: text("type"), // e.g., module, system, etc.
  isNew: boolean("is_new").default(false),
});

export const insertComponentSchema = createInsertSchema(components).pick({
  documentId: true,
  name: true,
  description: true,
  type: true,
  isNew: true,
});

export type InsertComponent = z.infer<typeof insertComponentSchema>;
export type Component = typeof components.$inferSelect;

// Requirement schema
export const requirements = pgTable("requirements", {
  id: serial("id").primaryKey(),
  documentId: integer("document_id").notNull(),
  code: text("code").notNull(), // REQ-001, etc.
  description: text("description").notNull(),
  category: text("category"),
});

export const insertRequirementSchema = createInsertSchema(requirements).pick({
  documentId: true,
  code: true,
  description: true,
  category: true,
});

export type InsertRequirement = z.infer<typeof insertRequirementSchema>;
export type Requirement = typeof requirements.$inferSelect;

// API Endpoint schema
export const apiEndpoints = pgTable("api_endpoints", {
  id: serial("id").primaryKey(),
  documentId: integer("document_id").notNull(),
  url: text("url").notNull(),
  method: text("method").notNull(), // GET, POST, etc.
  parameters: jsonb("parameters"), // JSON of parameters
  requirementCode: text("requirement_code"), // Reference to requirement code
});

export const insertApiEndpointSchema = createInsertSchema(apiEndpoints).pick({
  documentId: true,
  url: true,
  method: true,
  parameters: true,
  requirementCode: true,
});

export type InsertApiEndpoint = z.infer<typeof insertApiEndpointSchema>;
export type ApiEndpoint = typeof apiEndpoints.$inferSelect;

// Test Scenario schema
export const testScenarios = pgTable("test_scenarios", {
  id: serial("id").primaryKey(),
  documentId: integer("document_id").notNull(),
  title: text("title").notNull(),
  preconditions: text("preconditions"),
  steps: jsonb("steps"), // JSON array of steps
  expectedResults: text("expected_results"),
  requirementCode: text("requirement_code"), // Reference to requirement code
  format: text("format").default("default"), // default, gherkin, selenium, etc.
});

export const insertTestScenarioSchema = createInsertSchema(testScenarios).pick({
  documentId: true,
  title: true,
  preconditions: true,
  steps: true,
  expectedResults: true,
  requirementCode: true,
  format: true,
});

export type InsertTestScenario = z.infer<typeof insertTestScenarioSchema>;
export type TestScenario = typeof testScenarios.$inferSelect;

// AI Analysis schema
export const aiAnalysis = pgTable("ai_analysis", {
  id: serial("id").primaryKey(),
  documentId: integer("document_id").notNull(),
  observations: jsonb("observations"), // JSON array of observations
  analyzedAt: timestamp("analyzed_at").defaultNow(),
});

export const insertAiAnalysisSchema = createInsertSchema(aiAnalysis).pick({
  documentId: true,
  observations: true,
});

export type InsertAiAnalysis = z.infer<typeof insertAiAnalysisSchema>;
export type AiAnalysis = typeof aiAnalysis.$inferSelect;

// Coverage Validation schema - Test senaryoları ile gereksinimlerin kapsam analizini saklar
export const coverageValidation = pgTable("coverage_validation", {
  id: serial("id").primaryKey(),
  documentId: integer("document_id").notNull(),
  coverageRate: real("coverage_rate").notNull(),
  missingRequirements: jsonb("missing_requirements").notNull(), // JSON array of requirement codes
  analysisDetails: jsonb("analysis_details"), // JSON object with quality metrics
  recommendations: jsonb("recommendations"), // JSON array of recommendation strings
  weakPoints: jsonb("weak_points"), // JSON array of weakness strings
  strongPoints: jsonb("strong_points"), // JSON array of strength strings
  createdAt: timestamp("created_at").defaultNow(),
});

export const insertCoverageValidationSchema = createInsertSchema(coverageValidation).pick({
  documentId: true,
  coverageRate: true,
  missingRequirements: true,
  analysisDetails: true,
  recommendations: true,
  weakPoints: true,
  strongPoints: true,
});

export type InsertCoverageValidation = z.infer<typeof insertCoverageValidationSchema>;
export type CoverageValidation = typeof coverageValidation.$inferSelect;

// AI Learning Memory schema - Öğrenen AI için bellek yapısı
export const aiLearningMemory = pgTable("ai_learning_memory", {
  id: serial("id").primaryKey(),
  queryPattern: text("query_pattern").notNull(), // Sorgu deseni (anahtar kelimeleri içeren)
  knowledgeStore: jsonb("knowledge_store").notNull(), // Analiz sonucunda öğrenilen bilgiler
  successMetric: real("success_metric").notNull(), // Başarı ölçütü 0-1 arası
  frequencyUsed: integer("frequency_used").default(1), // Kaç kez kullanıldığı
  lastUsed: timestamp("last_used").defaultNow(), // Son kullanım zamanı
  createdAt: timestamp("created_at").defaultNow(), // İlk oluşturma zamanı
});

export const insertAiLearningMemorySchema = createInsertSchema(aiLearningMemory).pick({
  queryPattern: true,
  knowledgeStore: true,
  successMetric: true,
  frequencyUsed: true
});

export type InsertAiLearningMemory = z.infer<typeof insertAiLearningMemorySchema>;
export type AiLearningMemory = typeof aiLearningMemory.$inferSelect;

// AI Interaction History schema - Kullanıcı etkileşim geçmişi
export const aiInteractionHistory = pgTable("ai_interaction_history", {
  id: serial("id").primaryKey(),
  userId: integer("user_id"), // Null olabilir, anonim kullanıcılar için
  query: text("query").notNull(), // Kullanıcının sorgusu
  response: text("response").notNull(), // AI'nin yanıtı
  memoryReferences: jsonb("memory_references"), // Kullanılan bellek referansları
  feedbackRating: integer("feedback_rating"), // Kullanıcı geri bildirimi (1-5)
  processingTime: real("processing_time"), // İşlem süresi (ms)
  createdAt: timestamp("created_at").defaultNow(), // Etkileşim zamanı
});

export const insertAiInteractionHistorySchema = createInsertSchema(aiInteractionHistory).pick({
  userId: true,
  query: true,
  response: true,
  memoryReferences: true,
  feedbackRating: true,
  processingTime: true
});

export type InsertAiInteractionHistory = z.infer<typeof insertAiInteractionHistorySchema>;
export type AiInteractionHistory = typeof aiInteractionHistory.$inferSelect;

// API Bağlantı Ayarları - Dış servislerle entegrasyonu yönetir
export const apiConnections = pgTable("api_connections", {
  id: serial("id").primaryKey(),
  projectId: integer("project_id").notNull().references(() => projects.id, { onDelete: "cascade" }),
  name: text("name").notNull(), // Bağlantı adı
  type: text("type").notNull(), // "jira", "testrail", "alm", "visium"
  baseUrl: text("base_url").notNull(), // Servis temel URL'i
  apiKey: text("api_key"), // API anahtarı (şifrelenmiş olmalı)
  username: text("username"), // Kullanıcı adı (opsiyonel)
  token: text("token"), // Token (opsiyonel)
  settings: jsonb("settings"), // Ek ayarlar JSON olarak
  status: text("status").default("active"), // Bağlantı durumu
  lastSyncAt: timestamp("last_sync_at"), // Son senkronizasyon zamanı
  createdBy: integer("created_by").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const apiConnectionsRelations = relations(apiConnections, ({ one }) => ({
  project: one(projects, {
    fields: [apiConnections.projectId],
    references: [projects.id],
  }),
  creator: one(users, {
    fields: [apiConnections.createdBy],
    references: [users.id],
  }),
}));

export const insertApiConnectionSchema = createInsertSchema(apiConnections).pick({
  projectId: true,
  name: true,
  type: true,
  baseUrl: true,
  apiKey: true,
  username: true,
  token: true,
  settings: true,
  status: true,
  createdBy: true,
});

export type InsertApiConnection = z.infer<typeof insertApiConnectionSchema>;
export type ApiConnection = typeof apiConnections.$inferSelect;

// Sistem Ayarları - Global sistem ayarlarını yönetir
export const systemSettings = pgTable("system_settings", {
  id: serial("id").primaryKey(),
  category: text("category").notNull(), // "openai", "azure_openai", "visual_analysis", "general"
  key: text("key").notNull(), // Ayar anahtarı
  value: text("value"), // Ayar değeri
  description: text("description"), // Ayar açıklaması
  type: text("type").default("string"), // "string", "number", "boolean", "json"
  isEncrypted: boolean("is_encrypted").default(false), // Şifrelenmiş mi
  isActive: boolean("is_active").default(true), // Aktif mi
  createdBy: integer("created_by").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const systemSettingsRelations = relations(systemSettings, ({ one }) => ({
  creator: one(users, {
    fields: [systemSettings.createdBy],
    references: [users.id],
  }),
}));

export const insertSystemSettingSchema = createInsertSchema(systemSettings).pick({
  category: true,
  key: true,
  value: true,
  description: true,
  type: true,
  isEncrypted: true,
  isActive: true,
  createdBy: true,
});

export type SystemSetting = typeof systemSettings.$inferSelect;
export type InsertSystemSetting = z.infer<typeof insertSystemSettingSchema>;
