
> rest-express@1.0.0 dev
Warning: Indexing all PDF objects
1:46:04 AM [express] <PERSON><PERSON> kullanıcısı zaten mevcut: netas
1:46:04 AM [express] serving on port 5000
1:46:19 AM [express] GET /api/permissions 304 in 47ms :: ["user_management","project_management","do…
1:46:19 AM [express] GET /api/user-projects 304 in 46ms :: [{"userId":1,"projectId":1,"role":"owner"…
1:46:19 AM [express] GET /api/ai-models 304 in 23ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
1:46:20 AM [express] GET /api/documents 304 in 285ms :: [{"id":80,"name":"ABC Bank KampanyalÄ± Birey…
1:46:21 AM [express] GET /api/documents/115 200 in 48ms :: {"id":115,"name":"Jira Issue: SCRUM-1 - T…
1:46:21 AM [express] GET /api/documents/115/api-endpoints 200 in 56ms :: []
1:46:21 AM [express] GET /api/documents/115/components 200 in 49ms :: []
1:46:21 AM [express] GET /api/documents/115/ai-analysis 404 in 49ms :: {"error":"Bu doküman için AI …
1:46:21 AM [express] GET /api/documents/115/requirements 200 in 51ms :: []
1:46:23 AM [express] GET /api/documents/115/ai-analysis 404 in 57ms :: {"error":"Bu doküman için AI …
1:46:25 AM [express] GET /api/documents/115/ai-analysis 404 in 52ms :: {"error":"Bu doküman için AI …
1:46:25 AM [express] GET /api/documents/111/components 200 in 46ms :: []
1:46:25 AM [express] GET /api/documents/111/api-endpoints 200 in 52ms :: []
1:46:25 AM [express] GET /api/documents/111/requirements 200 in 51ms :: []
1:46:25 AM [express] GET /api/documents/111/ai-analysis 404 in 52ms :: {"error":"Bu doküman için AI …
1:46:25 AM [express] GET /api/documents/111 200 in 54ms :: {"id":111,"name":"Jira Issue: SCRUM-5 - v…
1:46:26 AM [express] GET /api/documents/111/ai-analysis 404 in 50ms :: {"error":"Bu doküman için AI …
1:46:29 AM [express] GET /api/documents/111/ai-analysis 404 in 106ms :: {"error":"Bu doküman için AI…
1:46:33 AM [express] GET /api/documents/111/ai-analysis 404 in 51ms :: {"error":"Bu doküman için AI …
1:46:37 AM [express] GET /api/documents/115/ai-analysis 404 in 51ms :: {"error":"Bu doküman için AI …
1:46:39 AM [express] GET /api/documents/115/ai-analysis 404 in 45ms :: {"error":"Bu doküman için AI …
1:46:39 AM [vite] hmr update /src/components/document-upload-modal.tsx, /src/index.css?v=CZaHZWxfeKX7I7gEM8Goc
1:46:41 AM [express] GET /api/documents/115/ai-analysis 404 in 49ms :: {"error":"Bu doküman için AI …
PostgreSQL: Doküman ve ilişkili tüm veriler silindi: ID=115
1:46:41 AM [express] DELETE /api/documents/115 200 in 292ms :: {"success":true,"message":"Doküman ba…
1:46:42 AM [express] GET /api/documents 200 in 302ms :: [{"id":80,"name":"ABC Bank KampanyalÄ± Birey…
PostgreSQL: Doküman ve ilişkili tüm veriler silindi: ID=114
1:46:43 AM [express] DELETE /api/documents/114 200 in 219ms :: {"success":true,"message":"Doküman ba…
1:46:44 AM [express] GET /api/documents 200 in 236ms :: [{"id":80,"name":"ABC Bank KampanyalÄ± Birey…
PostgreSQL: Doküman ve ilişkili tüm veriler silindi: ID=113
1:46:45 AM [express] DELETE /api/documents/113 200 in 261ms :: {"success":true,"message":"Doküman ba…
1:46:45 AM [express] GET /api/documents 200 in 276ms :: [{"id":80,"name":"ABC Bank KampanyalÄ± Birey…
PostgreSQL: Doküman ve ilişkili tüm veriler silindi: ID=112
1:46:47 AM [express] DELETE /api/documents/112 200 in 243ms :: {"success":true,"message":"Doküman ba…
1:46:47 AM [express] GET /api/documents 200 in 238ms :: [{"id":80,"name":"ABC Bank KampanyalÄ± Birey…
PostgreSQL: Doküman ve ilişkili tüm veriler silindi: ID=111
1:46:49 AM [express] DELETE /api/documents/111 200 in 220ms :: {"success":true,"message":"Doküman ba…
1:46:49 AM [express] GET /api/documents 200 in 242ms :: [{"id":80,"name":"ABC Bank KampanyalÄ± Birey…
1:46:55 AM [express] GET /api/user-projects 304 in 49ms :: [{"userId":1,"projectId":1,"role":"owner"…
1:46:55 AM [express] GET /api/permissions 304 in 51ms :: ["user_management","project_management","do…
1:46:55 AM [express] GET /api/ai-models 304 in 34ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
1:46:55 AM [express] GET /api/documents 200 in 348ms :: [{"id":80,"name":"ABC Bank KampanyalÄ± Birey…
1:47:03 AM [express] GET /api/projects/1/connections 304 in 97ms :: []
1:47:50 AM [express] POST /api/integrations/jira/test 200 in 439ms :: {"success":true,"message":"Bağ…
1:47:50 AM [express] POST /api/projects/1/connections 201 in 99ms :: {"id":1,"projectId":1,"name":"n…
1:47:51 AM [express] GET /api/projects/1/connections 200 in 85ms :: [{"id":1,"projectId":1,"name":"n…
1:47:51 AM [express] POST /api/integrations/jira/projects 200 in 358ms :: {"success":true,"projects"…
1:47:53 AM [express] POST /api/integrations/jira/issues 200 in 452ms :: {"success":true,"issues":[{"…
Doküman işleniyor. Dosya türü: 1, Görsel analizi: Aktif
Issue SCRUM-5 analiz hatası: TypeError: fileType.toLowerCase is not a function
    at Object.processDocument (/home/<USER>/workspace/server/services/document-processor.ts:490:20)
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:2073:37)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
Doküman işleniyor. Dosya türü: 1, Görsel analizi: Aktif
Issue SCRUM-4 analiz hatası: TypeError: fileType.toLowerCase is not a function
    at Object.processDocument (/home/<USER>/workspace/server/services/document-processor.ts:490:20)
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:2073:37)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
Doküman işleniyor. Dosya türü: 1, Görsel analizi: Aktif
Issue SCRUM-3 analiz hatası: TypeError: fileType.toLowerCase is not a function
    at Object.processDocument (/home/<USER>/workspace/server/services/document-processor.ts:490:20)
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:2073:37)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1:47:58 AM [express] POST /api/integrations/jira/analyze 200 in 1188ms :: {"success":true,"message":…
1:47:59 AM [express] GET /api/documents 200 in 363ms :: [{"id":80,"name":"ABC Bank KampanyalÄ± Birey…