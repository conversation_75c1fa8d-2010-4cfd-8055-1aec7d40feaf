import { type Document } from "@shared/schema";
import { useState, useEffect } from "react";
import { 
  AlertCircle, 
  Image as ImageIcon, 
  FileText, 
  Maximize2, 
  Minimize2, 
  FileCode, 
  Heading1, 
  ListOrdered, 
  Info, 
  FileQuestion,
  BookOpen,
  PanelRight,
  Loader2
} from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";


interface DocumentViewerProps {
  document?: Document | null;
  error?: any;
}

// Doküman içinde görsel içerik tespit edilip edilmediğini belirleyen yardımcı fonksiyon
const hasVisualContent = (content?: string): boolean => {
  if (!content) return false;
  
  return content.includes("[GÖRSEL İÇERİK ALGILANDI:") || 
    (content.includes("[BİLGİ:") && 
     (content.toLowerCase().includes("görsel") || 
      content.toLowerCase().includes("resim") || 
      content.toLowerCase().includes("diagram") ||
      content.toLowerCase().includes("tablo")));
};

// Görsel içerikle ilgili bilgileri ayıklayan yardımcı fonksiyon
const extractVisualInfo = (content?: string): string[] => {
  if (!content) return [];
  
  const visualInfos: string[] = [];
  const paragraphs = content.split(/\n\s*\n/);
  
  paragraphs.forEach(paragraph => {
    if ((paragraph.includes("[GÖRSEL İÇERİK ALGILANDI:") || paragraph.includes("[BİLGİ:")) && 
        (paragraph.toLowerCase().includes("görsel") || 
         paragraph.toLowerCase().includes("resim") || 
         paragraph.toLowerCase().includes("diagram") ||
         paragraph.toLowerCase().includes("tablo"))) {
      visualInfos.push(paragraph);
    }
  });
  
  return visualInfos;
};

const DocumentViewer = ({ document, error }: DocumentViewerProps) => {
  const [fullscreen, setFullscreen] = useState(false);
  const [hasVisuals, setHasVisuals] = useState(false);
  const [visualInfos, setVisualInfos] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [documentId, setDocumentId] = useState<number | null>(null);
  
  // Dokuman ID'si değiştiğinde yükleme durumunu kontrol et
  useEffect(() => {
    // Doküman değiştiğinde yükleme durumunu aktif et
    if (document?.id !== documentId) {
      // Yeni bir doküman seçildiğinde
      if (document?.id) {
        console.log("Yeni doküman seçildi, yükleniyor: ", document.id);
        setIsLoading(true);
        setDocumentId(document.id);
      } else {
        setDocumentId(null);
      }
    }
  }, [document?.id, documentId]);
  
  // İçerik geldiğinde yükleme durumunu güncelle
  useEffect(() => {
    // İçerik yüklendiğinde yüklemeyi durdur, ama minimum 1.2 saniye göster
    if (document?.content && isLoading && document.id === documentId) {
      console.log("İçerik yüklendi, minimum yükleme süresi bekleniyor: ", document.id);
      
      // Minimum gösterme süresi için bekle (kullanıcı deneyimini iyileştirmek için)
      const timer = setTimeout(() => {
        console.log("Minimum süre tamamlandı, yükleme duruyor: ", document.id);
        setIsLoading(false);
      }, 1200); // 1.2 saniye minimum gösterme süresi
      
      return () => clearTimeout(timer);
    }
    
    // Yükleme durumu 8 saniyeden fazla sürerse otomatik kapat
    let maxTimer: NodeJS.Timeout | null = null;
    if (isLoading) {
      maxTimer = setTimeout(() => {
        console.log("Zamanaşımı: Yükleme duruyor");
        setIsLoading(false);
      }, 8000);
    }
    
    return () => {
      if (maxTimer) clearTimeout(maxTimer);
    };
  }, [document?.content, document?.id, documentId, isLoading]);
  
  // Görsel içerik kontrolü
  useEffect(() => {
    if (document?.content) {
      const visualContentDetected = hasVisualContent(document.content);
      setHasVisuals(visualContentDetected);
      
      if (visualContentDetected) {
        setVisualInfos(extractVisualInfo(document.content));
      } else {
        setVisualInfos([]);
      }
    } else {
      setHasVisuals(false);
      setVisualInfos([]);
    }
  }, [document?.content]);
  
  const toggleFullscreen = () => {
    setFullscreen(!fullscreen);
  };

  // Format content for display with proper line breaks and formatting
  const formatContent = (content?: string) => {
    if (!content) return null;
    
    // Split by double newlines to get paragraphs
    const paragraphs = content.split(/\n\s*\n/);
    
    return paragraphs.map((paragraph, index) => {
      // Görsel içerik analiz bilgileri için özel formatlama
      if (paragraph.includes("[GÖRSEL İÇERİK ALGILANDI:") || 
          paragraph.includes("[BİLGİ:") || 
          paragraph.includes("[NOT:")) {
        
        // Görsel analiz bilgisi
        if (paragraph.toLowerCase().includes("görsel") || 
            paragraph.toLowerCase().includes("resim") || 
            paragraph.toLowerCase().includes("diagram")) {
            
          return (
            <Alert key={index} className="my-4 bg-blue-50 border-blue-200 text-blue-800">
              <div className="flex gap-2">
                <ImageIcon className="h-4 w-4 mt-1" />
                <div>
                  <span className="font-medium">Görsel İçerik Bilgisi:</span>{' '}
                  {paragraph.replace(/\[GÖRSEL İÇERİK ALGILANDI:|\[BİLGİ:|\[NOT:|\]/g, '')}
                </div>
              </div>
            </Alert>
          );
        }
        
        // Doküman meta bilgisi
        if (paragraph.includes("[DOKÜMAN BİLGİLERİ]")) {
          const lines = paragraph.split('\n').filter(line => line.trim() && !line.includes("[DOKÜMAN BİLGİLERİ]"));
          
          if (lines.length === 0) return null;
          
          return (
            <Card key={index} className="my-4 border-neutral-200 shadow-sm overflow-hidden">
              <div className="px-4 py-3 bg-neutral-50 border-b border-neutral-200 font-medium flex items-center gap-2">
                <FileText size={16} />
                Doküman Bilgileri
              </div>
              <div className="p-4 space-y-1 text-sm">
                {lines.map((line, i) => {
                  const [label, value] = line.split(': ');
                  return value ? (
                    <div key={i} className="grid grid-cols-3 gap-2">
                      <div className="font-medium text-neutral-700">{label}:</div>
                      <div className="col-span-2 text-neutral-900">{value}</div>
                    </div>
                  ) : <div key={i}>{line}</div>;
                })}
              </div>
            </Card>
          );
        }
        
        // Genel bilgi notu
        return (
          <Alert key={index} className="my-4 bg-neutral-50 border-neutral-200">
            <div className="flex gap-2">
              <Info className="h-4 w-4 mt-1" />
              <div>
                <span className="font-medium">Bilgi Notu:</span>{' '}
                {paragraph.replace(/\[GÖRSEL İÇERİK ALGILANDI:|\[BİLGİ:|\[NOT:|\]/g, '')}
              </div>
            </div>
          </Alert>
        );
      }
      
      // Check if this might be a heading (simple heuristic)
      const isHeading = paragraph.trim().length < 100 && (
        /^\d+\.\s+\w+/.test(paragraph.trim()) || // numbered headings like "1. Title"
        /^[A-Z\s]+$/.test(paragraph.trim()) // ALL CAPS HEADINGS
      );
      
      if (isHeading) {
        return (
          <div key={index} className="flex items-center gap-2 mt-8 mb-3">
            <Heading1 size={18} className="text-primary/70" />
            <h2 className="text-xl font-bold text-neutral-800">{paragraph}</h2>
          </div>
        );
      }
      
      // Check if this might be a code block (indented by spaces or has specific patterns)
      const isCodeBlock = paragraph.trim().startsWith("    ") || 
                          paragraph.includes("import ") || 
                          paragraph.includes("function ") ||
                          paragraph.includes("class ") ||
                          /^[{}[\];]+$/.test(paragraph.trim());
      
      if (isCodeBlock) {
        return (
          <div key={index} className="my-4 rounded-md overflow-hidden border border-neutral-200 shadow-sm">
            <div className="bg-neutral-800 py-2 px-4 flex items-center justify-between text-xs text-neutral-400">
              <div className="flex items-center gap-2">
                <FileCode size={14} className="text-neutral-400" />
                <span>Kod</span>
              </div>
            </div>
            <pre className="bg-neutral-900 text-neutral-50 p-4 overflow-x-auto font-mono text-sm">
              {paragraph}
            </pre>
          </div>
        );
      }
      
      // Check if this might be a list item
      const isListItem = paragraph.trim().startsWith("- ") || paragraph.trim().startsWith("* ") || /^\d+\.\s/.test(paragraph.trim());
      
      if (isListItem) {
        // Split into individual list items
        const items = paragraph.split(/\n/).filter(item => item.trim());
        const isNumbered = /^\d+\.\s/.test(items[0].trim());
        
        return (
          <div key={index} className="my-3 flex">
            <div className="mt-1 mr-2">
              <ListOrdered size={16} className="text-primary/70" />
            </div>
            <ul className={`${isNumbered ? 'list-decimal' : 'list-disc'} space-y-1.5 text-neutral-800`}>
              {items.map((item, i) => (
                <li key={i} className="pl-1">{item.replace(/^-\s*|\*\s*|\d+\.\s*/, '')}</li>
              ))}
            </ul>
          </div>
        );
      }
      
      // Regular paragraph
      return <p key={index} className="my-3 text-neutral-700 leading-relaxed">{paragraph}</p>;
    });
  };

  return (
    <div className={`${fullscreen ? 'w-full' : 'w-1/2'} flex flex-col border-r border-neutral-200 transition-all duration-300 h-full overflow-hidden`}>
      <div className="bg-white px-4 py-3 border-b border-neutral-200 flex items-center justify-between shadow-sm flex-shrink-0">
        <div className="flex items-center gap-2">
          <BookOpen size={18} className="text-primary" />
          <h2 className="font-medium">Kaynak İçeriği</h2>
        </div>
        <div className="flex items-center gap-2">
          {hasVisuals && (
            <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-100 border-blue-200 gap-1">
              <ImageIcon size={14} />
              <span className="hidden sm:inline">Görsel İçerik</span>
            </Badge>
          )}
          
          <Button 
            variant="ghost" 
            size="icon"
            className="h-8 w-8 rounded-full"
            title={fullscreen ? "Normal görünüme dön" : "Genişlet"}
            onClick={toggleFullscreen}
          >
            {fullscreen ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
          </Button>
          
          <Button 
            variant="ghost" 
            size="icon"
            className="h-8 w-8 rounded-full"
            title="Sayfa görünümü"
          >
            <PanelRight size={16} />
          </Button>
        </div>
      </div>
      
      <div className="flex-1 overflow-hidden">
        {error ? (
          <div className="flex h-full items-center justify-center p-6">
            <Card className="max-w-md p-6 shadow-lg border-red-200">
              <div className="text-center">
                <AlertCircle className="h-12 w-12 mb-4 mx-auto text-red-500" />
                <h3 className="text-lg font-medium mb-2">Yetki Hatası</h3>
                <p className="text-neutral-600">Bu dokümanı görüntüleme yetkiniz bulunmamaktadır.</p>
              </div>
            </Card>
          </div>
        ) : document ? (
          <div className="h-full overflow-y-auto">
            {/* Yükleme göstergesi */}
            {isLoading && (
              <div className="sticky top-0 z-20 px-4 pt-4">
                <Alert className="bg-primary-50 border-primary-200 shadow-md animate-pulse">
                  <Loader2 className="h-4 w-4 animate-spin text-primary" />
                  <AlertTitle className="text-primary-700">Doküman İçeriği Yükleniyor</AlertTitle>
                  <AlertDescription className="text-primary-600">
                    Doküman içeriği hazırlanıyor, lütfen bekleyin...
                  </AlertDescription>
                </Alert>
              </div>
            )}
            
            {/* Görsel içerik özeti */}
            {hasVisuals && visualInfos.length > 0 && (
              <div className={`sticky ${isLoading ? 'top-[85px]' : 'top-0'} z-10 px-4 pt-4`}>
                <Alert className="bg-blue-50 border-blue-200 text-blue-800 shadow-md">
                  <ImageIcon className="h-4 w-4" />
                  <AlertTitle className="text-blue-800">Görsel İçerik </AlertTitle>
                  <AlertDescription className="text-blue-700">
                    Bu sürümde performans için görsel analiz sınırlandırılmıştır ama genel sürümde bu sınırlandırma olmayacak
                  </AlertDescription>
                </Alert>
              </div>
            )}
            
            {/* Ana içerik */}
            <div className="p-6 pb-20 max-w-3xl mx-auto space-y-6">
              <div className="flex items-center gap-3 mb-6 bg-white/80 backdrop-blur-sm p-2 rounded-lg shadow-sm">
                {document.type === 'pdf' && <FileText size={24} className="text-red-500" />}
                {document.type === 'docx' && <FileText size={24} className="text-blue-500" />}
                {!document.type && <FileText size={24} className="text-neutral-500" />}
                <h1 className="text-2xl font-bold">{document.name}</h1>
              </div>
              
              {/* İçerik yüklenirken içerik yerine iskelet gösterge göster */}
              {isLoading ? (
                <div className="space-y-6 animate-pulse">
                  {[...Array(8)].map((_, idx) => (
                    <div key={idx} className="h-4 bg-gray-200 rounded w-full"></div>
                  ))}
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="space-y-2">
                    {[...Array(4)].map((_, idx) => (
                      <div key={idx} className="h-3 bg-gray-200 rounded w-full"></div>
                    ))}
                  </div>
                  <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                  <div className="space-y-2">
                    {[...Array(3)].map((_, idx) => (
                      <div key={idx} className="h-3 bg-gray-200 rounded w-full"></div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {formatContent(document.content)}
                </div>
              )}
              
              {/* Spacing at the bottom for better scrolling */}
              <div className="h-20"></div>
            </div>
          </div>
        ) : (
          <div className="flex h-full items-center justify-center p-6">
            <Card className="max-w-md p-8 shadow-lg border-neutral-200">
              <div className="text-center">
                <FileQuestion size={48} className="mx-auto mb-4 text-neutral-400" />
                <h3 className="text-lg font-medium mb-2">Henüz doküman seçilmedi</h3>
                <p className="text-neutral-500 text-sm">Sol menüden bir doküman seçin veya yeni bir doküman yükleyin</p>
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentViewer;
