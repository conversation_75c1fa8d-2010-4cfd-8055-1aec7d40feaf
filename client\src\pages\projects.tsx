import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useAuth } from "@/hooks/use-auth";
import { usePermission } from "@/hooks/use-permission";
import { SYSTEM_PERMISSIONS } from "@/hooks/use-permission";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { getQueryFn, apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import Header from "@/components/header";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Loader2, FolderPlus, FileSymlink, Calendar, Users2, Trash2, FolderOpen, Clock, Shield } from "lucide-react";

type Project = {
  id: number;
  name: string;
  description: string | null;
  createdBy: number;
  status: string;
  createdAt: string;
  updatedAt: string;
  creator?: {
    username: string;
    name: string | null;
  };
  documentCount?: number;
  userCount?: number;
};

export default function Projects() {
  const { user } = useAuth();
  const { hasPermission, isAdmin } = usePermission();
  const { toast } = useToast();
  const [, navigate] = useLocation();
  const [newProjectDialogOpen, setNewProjectDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("my-projects");
  const [newProject, setNewProject] = useState({
    name: "",
    description: "",
    status: "active"
  });

  // Projeleri getir
  const { data: projects = [], isLoading } = useQuery({
    queryKey: ["/api/projects"],
    queryFn: getQueryFn({ on401: "throw" }),
  });

  // Yeni proje oluştur
  const createProjectMutation = useMutation({
    mutationFn: async (projectData: typeof newProject) => {
      const res = await apiRequest("POST", "/api/projects", projectData);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/projects"] });
      toast({
        title: "Proje oluşturuldu",
        description: "Yeni proje başarıyla oluşturuldu.",
      });
      setNewProjectDialogOpen(false);
      setNewProject({ name: "", description: "", status: "active" });
    },
    onError: (error: Error) => {
      toast({
        title: "Proje oluşturulamadı",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Projemi sil
  const deleteProjectMutation = useMutation({
    mutationFn: async (projectId: number) => {
      await apiRequest("DELETE", `/api/projects/${projectId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/projects"] });
      toast({
        title: "Proje silindi",
        description: "Proje başarıyla silindi.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Proje silinemedi",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleCreateProject = (e: React.FormEvent) => {
    e.preventDefault();
    createProjectMutation.mutate(newProject);
  };

  const handleDeleteProject = (projectId: number) => {
    if (window.confirm("Bu projeyi silmek istediğinizden emin misiniz?")) {
      deleteProjectMutation.mutate(projectId);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewProject(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleStatusChange = (value: string) => {
    setNewProject(prev => ({
      ...prev,
      status: value
    }));
  };

  // Kullanıcının projeleri ile diğer projeleri ayır
  const myProjects = Array.isArray(projects)
    ? projects.filter((project: Project) => project.createdBy === user?.id)
    : [];
    
  const otherProjects = Array.isArray(projects)
    ? projects.filter((project: Project) => project.createdBy !== user?.id)
    : [];

  return (
    <div className="min-h-screen bg-neutral-50">
      <Header />
      
      <main className="container mx-auto p-4 pt-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Projeler</h1>
          
          {hasPermission(SYSTEM_PERMISSIONS.PROJECT_CREATE) && (
            <Dialog open={newProjectDialogOpen} onOpenChange={setNewProjectDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <FolderPlus className="h-4 w-4" />
                  Yeni Proje
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Yeni Proje Oluştur</DialogTitle>
                  <DialogDescription>
                    Yeni bir proje oluşturun. Projeler, belgeleri ve test senaryolarını gruplamak için kullanılır.
                  </DialogDescription>
                </DialogHeader>
                
                <form onSubmit={handleCreateProject}>
                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Proje Adı</Label>
                      <Input
                        id="name"
                        name="name"
                        placeholder="Örn: Web Uygulaması API Testleri"
                        value={newProject.name}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="description">Proje Açıklaması</Label>
                      <textarea
                        id="description"
                        name="description"
                        placeholder="Projenin amacı ve kapsamı..."
                        className="w-full min-h-[100px] rounded-md border border-input px-3 py-2 text-sm"
                        value={newProject.description}
                        onChange={handleInputChange}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="status">Proje Durumu</Label>
                      <Select value={newProject.status} onValueChange={handleStatusChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="Durum seçin" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="active">Aktif</SelectItem>
                          <SelectItem value="planning">Planlama</SelectItem>
                          <SelectItem value="completed">Tamamlandı</SelectItem>
                          <SelectItem value="archived">Arşivlendi</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <DialogFooter>
                    <Button 
                      type="submit" 
                      disabled={createProjectMutation.isPending}
                      className="w-full sm:w-auto"
                    >
                      {createProjectMutation.isPending && (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      )}
                      Projeyi Oluştur
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          )}
        </div>
        
        <Tabs defaultValue="my-projects" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-6">
            <TabsTrigger value="my-projects">Projelerim</TabsTrigger>
            <TabsTrigger value="all-projects">Tüm Projeler</TabsTrigger>
          </TabsList>
          
          <TabsContent value="my-projects">
            {isLoading ? (
              <div className="flex justify-center items-center p-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : myProjects.length === 0 ? (
              <div className="text-center p-8 border rounded-lg bg-white">
                <h3 className="text-lg font-medium mb-2">Henüz bir projeniz yok</h3>
                <p className="text-neutral-500 mb-4">
                  Belgelerinizi ve test senaryolarınızı organize etmek için yeni bir proje oluşturun.
                </p>
                {hasPermission(SYSTEM_PERMISSIONS.PROJECT_CREATE) && (
                  <Button onClick={() => setNewProjectDialogOpen(true)}>
                    <FolderPlus className="mr-2 h-4 w-4" />
                    Yeni Proje Oluştur
                  </Button>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {myProjects.map((project: Project) => (
                  <ProjectCard 
                    key={project.id} 
                    project={project} 
                    onDelete={handleDeleteProject}
                    onOpen={() => navigate(`/projects/${project.id}`)}
                  />
                ))}
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="all-projects">
            {isLoading ? (
              <div className="flex justify-center items-center p-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : otherProjects.length === 0 ? (
              <div className="text-center p-8 border rounded-lg bg-white">
                <h3 className="text-lg font-medium mb-2">Erişilebilir başka proje yok</h3>
                <p className="text-neutral-500">
                  Şu anda size erişim izni verilmiş başka proje bulunmamaktadır.
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {otherProjects.map((project: Project) => (
                  <ProjectCard 
                    key={project.id} 
                    project={project} 
                    readonly
                    onOpen={() => navigate(`/projects/${project.id}`)}
                  />
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}

// Proje kartı bileşeni
function ProjectCard({ 
  project, 
  readonly = false,
  onDelete,
  onOpen
}: { 
  project: Project; 
  readonly?: boolean;
  onDelete?: (id: number) => void;
  onOpen?: () => void;
}) {
  const statusColors = {
    active: "bg-green-100 text-green-800",
    planning: "bg-blue-100 text-blue-800",
    completed: "bg-purple-100 text-purple-800",
    archived: "bg-gray-100 text-gray-800"
  };
  
  const statusLabels = {
    active: "Aktif",
    planning: "Planlama",
    completed: "Tamamlandı",
    archived: "Arşivlendi"
  };
  
  // Proje durumunu Türkçe olarak göster
  const statusClass = statusColors[project.status as keyof typeof statusColors] || statusColors.active;
  const statusLabel = statusLabels[project.status as keyof typeof statusLabels] || statusLabels.active;
  
  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-4">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg">{project.name}</CardTitle>
          <Badge className={statusClass}>{statusLabel}</Badge>
        </div>
        <CardDescription>
          {project.description || "Proje açıklaması yok"}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="space-y-2 text-sm">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-neutral-500" />
            <span>Oluşturulma: {new Date(project.createdAt).toLocaleDateString('tr-TR')}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-neutral-500" />
            <span>Güncelleme: {new Date(project.updatedAt).toLocaleDateString('tr-TR')}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <FileSymlink className="h-4 w-4 text-neutral-500" />
            <span>Doküman Sayısı: {project.documentCount || 0}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <Users2 className="h-4 w-4 text-neutral-500" />
            <span>Kullanıcı Sayısı: {project.userCount || 1}</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="pt-4 flex justify-between">
        <Button variant="outline" className="flex-1 mr-2" onClick={onOpen}>
          <FolderOpen className="mr-2 h-4 w-4" />
          Açık
        </Button>
        
        {!readonly && onDelete && (
          <Button 
            variant="outline" 
            className="flex-0"
            onClick={() => onDelete(project.id)}
          >
            <Trash2 className="h-4 w-4 text-red-500" />
          </Button>
        )}
        
        {readonly && (
          <Button variant="outline" className="flex-0">
            <Shield className="h-4 w-4 text-blue-500" />
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}