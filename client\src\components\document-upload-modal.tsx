import { useState } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import JiraManagement from "@/components/jira-management";
import JiraAIBot from "@/components/jira-ai-bot";

interface DocumentUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
}

// Veri kaynağı türleri
type DataSourceType = "document" | "text" | "jira" | "jira-management" | "jira-ai-bot" | "alm" | "testrail" | "confluence" | "visium";

const DocumentUploadModal = ({ isOpen, onClose }: DocumentUploadModalProps) => {
  // Kaynak seçim adımı ve seçilen kaynak türü
  const [step, setStep] = useState<"select-source" | "upload">("select-source");
  const [selectedSource, setSelectedSource] = useState<DataSourceType | null>(null);

  // Doküman yükleme durumu
  const [file, setFile] = useState<File | null>(null);
  const [textContent, setTextContent] = useState<string>("");
  const [textTitle, setTextTitle] = useState<string>("");
  const [useAI, setUseAI] = useState(true);
  const [extractCode, setExtractCode] = useState(true);
  const [includeVisuals, setIncludeVisuals] = useState(true);
  const [dragActive, setDragActive] = useState(false);

  // Jira entegrasyon state'leri
  const [jiraUrl, setJiraUrl] = useState<string>("");
  const [jiraUsername, setJiraUsername] = useState<string>("");
  const [jiraApiToken, setJiraApiToken] = useState<string>("");
  const [jiraConnectionName, setJiraConnectionName] = useState<string>("");
  const [saveConnection, setSaveConnection] = useState<boolean>(true);
  const [selectedConnection, setSelectedConnection] = useState<string>("");
  const [jiraStep, setJiraStep] = useState<"config" | "projects" | "issues">("config");
  const [selectedProject, setSelectedProject] = useState<string>("");
  const [selectedIssues, setSelectedIssues] = useState<string[]>([]);

  const { toast } = useToast();

  // Kayıtlı Jira bağlantılarını getir
  const { data: savedConnections } = useQuery({
    queryKey: ["api-connections", "jira"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/projects/1/connections?type=jira");
      return response.json();
    },
    enabled: selectedSource === "jira",
  });

  // Jira bağlantısı kaydetme
  const saveConnectionMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("POST", "/api/projects/1/connections", {
        name: jiraConnectionName,
        type: "jira",
        baseUrl: jiraUrl,
        username: jiraUsername,
        token: jiraApiToken
      });
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Bağlantı kaydedildi",
        description: "Jira bağlantısı başarıyla kaydedildi. Gelecekte tekrar kullanabilirsiniz.",
      });
      queryClient.invalidateQueries({ queryKey: ["api-connections"] });
    },
    onError: (error: any) => {
      toast({
        title: "Kaydetme hatası",
        description: error.message || "Bağlantı kaydedilemedi.",
        variant: "destructive",
      });
    },
  });

  // Jira bağlantı testi
  const jiraTestMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("POST", "/api/integrations/jira/test", {
        url: jiraUrl,
        username: jiraUsername,
        apiToken: jiraApiToken
      });
      return response.json();
    },
    onSuccess: async () => {
      toast({
        title: "Bağlantı başarılı",
        description: "Jira sunucusu ile bağlantı kuruldu. Projeler yükleniyor...",
      });

      // Bağlantıyı kaydet (eğer isteniyorsa)
      if (saveConnection && jiraConnectionName.trim()) {
        try {
          await saveConnectionMutation.mutateAsync();
        } catch (error) {
          console.error("Bağlantı kaydetme hatası:", error);
        }
      }

      setJiraStep("projects");
    },
    onError: (error: any) => {
      toast({
        title: "Bağlantı hatası",
        description: error.message || "Jira sunucusu ile bağlantı kurulamadı.",
        variant: "destructive",
      });
    },
  });

  // Jira projelerini getir
  const { data: jiraProjects } = useQuery({
    queryKey: ["jira-projects", jiraUrl, jiraUsername],
    queryFn: async () => {
      const response = await apiRequest("POST", "/api/integrations/jira/projects", {
        url: jiraUrl,
        username: jiraUsername,
        apiToken: jiraApiToken
      });
      const data = await response.json();
      return data.projects || [];
    },
    enabled: jiraStep === "projects" && !!jiraUrl && !!jiraUsername && !!jiraApiToken,
  });

  // Jira issue'larını getir
  const { data: jiraIssues } = useQuery({
    queryKey: ["jira-issues", selectedProject],
    queryFn: async () => {
      const response = await apiRequest("POST", "/api/integrations/jira/issues", {
        url: jiraUrl,
        username: jiraUsername,
        apiToken: jiraApiToken,
        projectKey: selectedProject
      });
      const data = await response.json();
      return data.issues || [];
    },
    enabled: jiraStep === "issues" && !!selectedProject,
  });

  // Issue analizi
  const jiraAnalyzeMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("POST", "/api/integrations/jira/analyze", {
        url: jiraUrl,
        username: jiraUsername,
        apiToken: jiraApiToken,
        projectKey: selectedProject,
        issueKeys: selectedIssues
      });
      return response.json();
    },
    onSuccess: (data) => {
      toast({
        title: "Analiz tamamlandı",
        description: `${selectedIssues.length} issue başarıyla analiz edildi ve doküman olarak eklendi.`,
      });
      queryClient.invalidateQueries({ queryKey: ["/api/documents"] });
      handleClose();
    },
    onError: (error: any) => {
      toast({
        title: "Analiz hatası",
        description: error.message || "Issue analizi sırasında hata oluştu.",
        variant: "destructive",
      });
    },
  });

  const uploadMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      // For file uploads, we need to use the native fetch API directly
      // as apiRequest doesn't handle FormData properly
      const response = await fetch("/api/documents/upload", {
        method: "POST",
        body: formData,
        credentials: "include",
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || response.statusText);
      }

      return response.json();
    },
    onSuccess: (data) => {
      toast({
        title: "Analiz tamamlandı",
        description: selectedSource === "text" ? "Metin başarıyla analiz edildi." : "Doküman başarıyla yüklendi ve analiz edildi.",
        variant: "default",
      });

      // Yeni doküman eklendikten sonra doküman listesini otomatik olarak güncelle
      // Önce mevcut doküman listesini al ve yeni dokümanı başa ekleyerek cache'i güncelle
      if (data && data.document) {
        const newDocument = data.document;

        // Cache'i güncelle (manuel olarak)
        queryClient.setQueryData<any[]>(["/api/documents"], (oldData) => {
          // Eğer önceki veri yoksa sadece yeni dokümanı içeren bir dizi döndür
          if (!oldData) return [newDocument];

          // Yeni dokümanı en başa ekle (en yeni olduğu için)
          return [newDocument, ...oldData];
        });
      }

      // Ardından API'den güncel listeyi çek
      queryClient.invalidateQueries({ queryKey: ["/api/documents"] });

      // Modal'ı temizle ve kapat
      setFile(null);
      setTextContent("");
      setTextTitle("");
      onClose();
    },
    onError: (error) => {
      toast({
        title: "Hata",
        description: `Analiz işlemi sırasında bir hata oluştu: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`,
        variant: "destructive",
      });
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setFile(e.dataTransfer.files[0]);
    }
  };

  const handleSubmit = () => {
    if (selectedSource === "document") {
      if (file) {
        const formData = new FormData();
        formData.append("file", file);
        formData.append("useAI", useAI.toString());
        formData.append("extractCode", extractCode.toString());
        formData.append("includeVisuals", includeVisuals.toString());
        uploadMutation.mutate(formData);
      } else {
        toast({
          title: "Uyarı",
          description: "Lütfen bir dosya seçin",
          variant: "destructive",
        });
      }
    } else if (selectedSource === "text") {
      if (textContent.trim().length < 500) {
        toast({
          title: "Uyarı",
          description: "Lütfen en az bir sayfa uzunluğunda metin girin (en az 500 karakter)",
          variant: "destructive",
        });
        return;
      }

      if (!textTitle.trim()) {
        toast({
          title: "Uyarı",
          description: "Lütfen belge için bir başlık girin",
          variant: "destructive",
        });
        return;
      }

      const formData = new FormData();
      // Metin içeriğini dosya olarak gönder
      const textBlob = new Blob([textContent], { type: 'text/plain' });
      const textFile = new File([textBlob], `${textTitle.trim()}.txt`, { type: 'text/plain' });

      formData.append("file", textFile);
      formData.append("useAI", useAI.toString());
      formData.append("extractCode", extractCode.toString());
      formData.append("sourceType", "text");
      formData.append("title", textTitle);

      uploadMutation.mutate(formData);
    } else if (selectedSource === "jira") {
      // Jira analizi başlat
      if (selectedIssues.length === 0) {
        toast({
          title: "Uyarı",
          description: "Lütfen en az bir issue seçin",
          variant: "destructive",
        });
        return;
      }
      jiraAnalyzeMutation.mutate();
    } else {
      // Diğer kaynaklar için örnek submit işlemi
      toast({
        title: "Bilgi",
        description: `${getSourceDisplayName(selectedSource || "unknown")} entegrasyonu yakında eklenecek.`,
        variant: "default",
      });
      handleClose();
    }
  };

  // Kaynak seçimi işleme
  const handleSourceSelect = (source: DataSourceType) => {
    setSelectedSource(source);
    setStep("upload");
  };

  // Kaynak adını görüntüye dönüştür
  const getSourceDisplayName = (source: DataSourceType | "unknown"): string => {
    switch (source) {
      case "document": return "Doküman Yükleme";
      case "text": return "Metin Girişi";
      case "jira": return "Jira (Hızlı Analiz)";
      case "jira-management": return "Jira Proje Yönetimi";
      case "jira-ai-bot": return "Jira AI Bot";
      case "alm": return "ALM";
      case "testrail": return "TestRail";
      case "confluence": return "Confluence";
      case "visium": return "Visium Manage";
      default: return "Bilinmeyen Kaynak";
    }
  };

  // Kaynak simgesini seç
  const getSourceIcon = (source: DataSourceType | "unknown"): string => {
    switch (source) {
      case "document": return "fas fa-file-alt";
      case "text": return "fas fa-align-left";
      case "jira": return "fab fa-jira";
      case "jira-management": return "fas fa-project-diagram";
      case "jira-ai-bot": return "fas fa-robot";
      case "alm": return "fas fa-cubes";
      case "testrail": return "fas fa-vial";
      case "confluence": return "fab fa-confluence";
      case "visium": return "fas fa-project-diagram";
      default: return "fas fa-question-circle";
    }
  };

  // Modal kapatılırken state'i sıfırla
  const handleClose = () => {
    setStep("select-source");
    setSelectedSource(null);
    setFile(null);
    setTextContent("");
    setTextTitle("");
    // Jira state'lerini sıfırla
    setJiraUrl("");
    setJiraUsername("");
    setJiraApiToken("");
    setJiraStep("config");
    setSelectedProject("");
    setSelectedIssues([]);
    onClose();
  };

  if (!isOpen) return null;

  // Jira bağlantı formu
  const renderJiraForm = () => {
    if (jiraStep === "config") {
      return (
        <div className="p-6">
          <div className="mb-6">
            <i className="fab fa-jira text-4xl text-blue-500 block mx-auto mb-3"></i>
            <h3 className="text-center text-lg font-medium mb-1">Jira Bağlantısı</h3>
            <p className="text-center text-sm text-neutral-600 mb-4">Jira içerisindeki issue'ları ve yorumları analiz edin</p>
          </div>

          {/* Kayıtlı bağlantılar */}
          {savedConnections && savedConnections.length > 0 && (
            <div className="mb-6">
              <h4 className="text-sm font-medium text-neutral-700 mb-2">Kayıtlı Bağlantılar</h4>
              <div className="space-y-2">
                {savedConnections.map((connection: any) => (
                  <div
                    key={connection.id}
                    onClick={() => {
                      setJiraUrl(connection.baseUrl);
                      setJiraUsername(connection.username || "");
                      setJiraApiToken(connection.token || "");
                      setSelectedConnection(connection.id.toString());
                    }}
                    className={`p-3 border rounded cursor-pointer hover:bg-neutral-50 ${selectedConnection === connection.id.toString() ? 'border-blue-500 bg-blue-50' : 'border-neutral-300'}`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="font-medium">{connection.name}</h5>
                        <p className="text-sm text-neutral-600">{connection.baseUrl}</p>
                      </div>
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Kayıtlı</span>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-3 text-center">
                <button
                  onClick={() => {
                    setSelectedConnection("");
                    setJiraUrl("");
                    setJiraUsername("");
                    setJiraApiToken("");
                  }}
                  className="text-sm text-blue-600 hover:text-blue-700"
                >
                  + Yeni bağlantı ekle
                </button>
              </div>
            </div>
          )}

          {/* Yeni bağlantı formu */}
          {(!savedConnections || savedConnections.length === 0 || !selectedConnection) && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-1">Jira URL</label>
                <input
                  type="text"
                  value={jiraUrl}
                  onChange={(e) => setJiraUrl(e.target.value)}
                  className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
                  placeholder="https://your-domain.atlassian.net"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-1">Kullanıcı Adı / E-posta</label>
                <input
                  type="text"
                  value={jiraUsername}
                  onChange={(e) => setJiraUsername(e.target.value)}
                  className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
                  placeholder="Jira kullanıcı adınız veya e-posta adresiniz"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-1">API Token</label>
                <input
                  type="password"
                  value={jiraApiToken}
                  onChange={(e) => setJiraApiToken(e.target.value)}
                  className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
                  placeholder="API Token'ınızı girin"
                />
                <p className="text-xs text-neutral-500 mt-1">
                  API Token'ınızı Jira → Profile → Security → API tokens bölümünden oluşturabilirsiniz.
                </p>
              </div>

              {/* Bağlantıyı kaydetme seçeneği */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="saveConnection"
                  checked={saveConnection}
                  onChange={(e) => setSaveConnection(e.target.checked)}
                  className="rounded"
                />
                <label htmlFor="saveConnection" className="text-sm text-neutral-700">
                  Bu bağlantıyı gelecekte kullanmak üzere kaydet
                </label>
              </div>

              {saveConnection && (
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-1">Bağlantı Adı</label>
                  <input
                    type="text"
                    value={jiraConnectionName}
                    onChange={(e) => setJiraConnectionName(e.target.value)}
                    className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
                    placeholder="Örn: Ana Jira Sunucusu"
                  />
                </div>
              )}
            </div>
          )}

          <button
            onClick={() => jiraTestMutation.mutate()}
            disabled={!jiraUrl || !jiraUsername || !jiraApiToken || jiraTestMutation.isPending}
            className="w-full mt-4 bg-blue-500 text-white p-2 rounded hover:bg-blue-600 disabled:bg-neutral-300 disabled:cursor-not-allowed"
          >
            {jiraTestMutation.isPending ? "Bağlantı test ediliyor..." : "Bağlantıyı Test Et"}
          </button>
        </div>
      );
    }

    if (jiraStep === "projects") {
      return (
        <div className="p-6">
          <div className="mb-6">
            <i className="fab fa-jira text-4xl text-blue-500 block mx-auto mb-3"></i>
            <h3 className="text-center text-lg font-medium mb-1">Proje Seçimi</h3>
            <p className="text-center text-sm text-neutral-600 mb-4">Analiz etmek istediğiniz projeyi seçin</p>
          </div>

          <div className="space-y-3 max-h-64 overflow-y-auto">
            {jiraProjects?.map((project: any) => (
              <div
                key={project.id}
                onClick={() => {
                  setSelectedProject(project.key);
                  setJiraStep("issues");
                }}
                className="p-3 border border-neutral-300 rounded cursor-pointer hover:bg-neutral-50"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">{project.name}</h4>
                    <p className="text-sm text-neutral-600">{project.key}</p>
                  </div>
                  <span className="text-xs bg-neutral-100 px-2 py-1 rounded">{project.key}</span>
                </div>
                {project.description && (
                  <p className="text-sm text-neutral-500 mt-1 line-clamp-2">
                    {project.description}
                  </p>
                )}
              </div>
            ))}
          </div>

          <button
            onClick={() => setJiraStep("config")}
            className="w-full mt-4 bg-neutral-500 text-white p-2 rounded hover:bg-neutral-600"
          >
            Geri Dön
          </button>
        </div>
      );
    }

    if (jiraStep === "issues") {
      return (
        <div className="p-6">
          <div className="mb-6">
            <i className="fab fa-jira text-4xl text-blue-500 block mx-auto mb-3"></i>
            <h3 className="text-center text-lg font-medium mb-1">Issue Seçimi</h3>
            <p className="text-center text-sm text-neutral-600 mb-4">
              Proje: <span className="font-medium">{selectedProject}</span>
            </p>
          </div>

          {selectedIssues.length > 0 && (
            <div className="mb-4 p-3 bg-blue-50 rounded">
              <p className="text-sm text-blue-700">
                {selectedIssues.length} issue seçildi
              </p>
            </div>
          )}

          <div className="space-y-3 max-h-64 overflow-y-auto">
            {jiraIssues?.map((issue: any) => (
              <div
                key={issue.id}
                className="p-3 border border-neutral-300 rounded"
              >
                <div className="flex items-start gap-3">
                  <input
                    type="checkbox"
                    checked={selectedIssues.includes(issue.key)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedIssues([...selectedIssues, issue.key]);
                      } else {
                        setSelectedIssues(selectedIssues.filter(key => key !== issue.key));
                      }
                    }}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-xs bg-neutral-100 px-2 py-1 rounded">{issue.key}</span>
                      <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">{issue.issueType}</span>
                      <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">{issue.status}</span>
                    </div>
                    <h4 className="font-medium text-sm">{issue.summary}</h4>
                    {issue.description && (
                      <p className="text-xs text-neutral-600 mt-1 line-clamp-2">
                        {issue.description.substring(0, 100)}...
                      </p>
                    )}
                    {issue.comments && issue.comments.length > 0 && (
                      <p className="text-xs text-neutral-500 mt-1">
                        {issue.comments.length} yorum
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="flex gap-2 mt-4">
            <button
              onClick={() => setJiraStep("projects")}
              className="flex-1 bg-neutral-500 text-white p-2 rounded hover:bg-neutral-600"
            >
              Proje Değiştir
            </button>
            <button
              onClick={() => jiraAnalyzeMutation.mutate()}
              disabled={selectedIssues.length === 0 || jiraAnalyzeMutation.isPending}
              className="flex-1 bg-blue-500 text-white p-2 rounded hover:bg-blue-600 disabled:bg-neutral-300"
            >
              {jiraAnalyzeMutation.isPending ? "Analiz ediliyor..." : "Seçilen Issue'ları Analiz Et"}
            </button>
          </div>
        </div>
      );
    }
  };

  // ALM bağlantı formu
  const renderAlmForm = () => {
    return (
      <div className="p-6">
        <div className="mb-6">
          <i className="fas fa-cubes text-4xl text-orange-500 block mx-auto mb-3"></i>
          <h3 className="text-center text-lg font-medium mb-1">ALM Bağlantısı</h3>
          <p className="text-center text-sm text-neutral-600 mb-4">HP ALM / Quality Center test senaryolarını analiz edin</p>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">ALM Sunucu URL</label>
            <input
              type="text"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="https://alm-server/qcbin"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">Alan Adı</label>
            <input
              type="text"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="DEFAULT"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">Proje Adı</label>
            <input
              type="text"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="Proje adı"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">Kullanıcı Adı</label>
            <input
              type="text"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="ALM kullanıcı adınız"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">Şifre</label>
            <input
              type="password"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="ALM şifreniz"
            />
          </div>
        </div>
      </div>
    );
  };

  // TestRail bağlantı formu
  const renderTestRailForm = () => {
    return (
      <div className="p-6">
        <div className="mb-6">
          <i className="fas fa-vial text-4xl text-green-500 block mx-auto mb-3"></i>
          <h3 className="text-center text-lg font-medium mb-1">TestRail Bağlantısı</h3>
          <p className="text-center text-sm text-neutral-600 mb-4">TestRail test senaryolarını ve sonuçlarını analiz edin</p>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">TestRail URL</label>
            <input
              type="text"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="https://example.testrail.com"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">API Anahtarı</label>
            <input
              type="password"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="TestRail API anahtarınız"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">Kullanıcı Adı / E-posta</label>
            <input
              type="text"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="API kullanıcı adınız veya e-posta adresiniz"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">Proje ID</label>
            <input
              type="text"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="Proje ID (örn: P1)"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">Test Suite <span className="text-xs text-neutral-500">(Opsiyonel)</span></label>
            <input
              type="text"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="Test Suite ID (boş bırakılabilir)"
            />
          </div>
        </div>
      </div>
    );
  };

  // Confluence bağlantı formu
  const renderConfluenceForm = () => {
    return (
      <div className="p-6">
        <div className="mb-6">
          <i className="fab fa-confluence text-4xl text-blue-400 block mx-auto mb-3"></i>
          <h3 className="text-center text-lg font-medium mb-1">Confluence Bağlantısı</h3>
          <p className="text-center text-sm text-neutral-600 mb-4">Confluence wiki sayfalarını ve dokümanlarını analiz edin</p>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">Confluence URL</label>
            <input
              type="text"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="https://your-domain.atlassian.net/wiki"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">API Anahtarı</label>
            <input
              type="password"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="Confluence API anahtarınız"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">Kullanıcı Adı / E-posta</label>
            <input
              type="text"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="Confluence kullanıcı adınız veya e-posta adresiniz"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">Alan Adı</label>
            <input
              type="text"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="Confluence alan adı (örn: ~kullaniciadi veya alananahtari)"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">Sayfa ID veya Başlığı <span className="text-xs text-neutral-500">(Opsiyonel)</span></label>
            <input
              type="text"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="Sayfa ID veya başlık (boş bırakılabilir)"
            />
          </div>
        </div>
      </div>
    );
  };

  // Visium Manage bağlantı formu
  const renderVisiumForm = () => {
    return (
      <div className="p-6">
        <div className="mb-6">
          <i className="fas fa-project-diagram text-4xl text-purple-500 block mx-auto mb-3"></i>
          <h3 className="text-center text-lg font-medium mb-1">Visium Manage Bağlantısı</h3>
          <p className="text-center text-sm text-neutral-600 mb-4">Visium Manage test senaryolarını ve gereksinimlerini analiz edin</p>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">Visium Manage URL</label>
            <input
              type="text"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="https://visium-instance-url"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">API Anahtarı</label>
            <input
              type="password"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="Visium API anahtarınız"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">Kullanıcı Adı</label>
            <input
              type="text"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="Visium kullanıcı adınız"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">Şifre</label>
            <input
              type="password"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="Visium şifreniz"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">Proje Adı</label>
            <input
              type="text"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="Proje adı"
            />
          </div>
        </div>
      </div>
    );
  };

  // Metin giriş formu
  const renderTextInput = () => {
    return (
      <div className="p-6">
        <div className="mb-6">
          <i className="fas fa-align-left text-4xl text-teal-500 block mx-auto mb-3"></i>
          <h3 className="text-center text-lg font-medium mb-1">Metin Girişi</h3>
          <p className="text-center text-sm text-neutral-600 mb-4">Analiz etmek istediğiniz metni doğrudan girin</p>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">Belge Başlığı</label>
            <input
              type="text"
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="Belgenizin başlığını girin"
              value={textTitle}
              onChange={(e) => setTextTitle(e.target.value)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-1">Metin İçeriği</label>
            <p className="text-xs text-neutral-500 mb-2">En az bir sayfa uzunluğunda metin girişi yapılmalıdır</p>
            <textarea
              className="w-full p-2 border border-neutral-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 h-64"
              placeholder="Analiz edilecek metni buraya girin..."
              value={textContent}
              onChange={(e) => setTextContent(e.target.value)}
            />
          </div>

          <div className="mt-6 space-y-2">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="useAIText"
                className="rounded text-primary focus:ring-primary"
                checked={useAI}
                onChange={(e) => setUseAI(e.target.checked)}
              />
              <label htmlFor="useAIText" className="ml-2 text-sm text-neutral-700">
                Yapay Zeka analizi kullan
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="extractCodeText"
                className="rounded text-primary focus:ring-primary"
                checked={extractCode}
                onChange={(e) => setExtractCode(e.target.checked)}
              />
              <label htmlFor="extractCodeText" className="ml-2 text-sm text-neutral-700">
                Kod alanlarını otomatik tespit et
              </label>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Doküman upload alanı
  const renderDocumentUpload = () => {
    return (
      <div className="p-6">
        <div
          className={`border-2 ${dragActive ? 'border-primary' : 'border-dashed border-neutral-300'} rounded-lg p-8 text-center`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <i className="fas fa-file-upload text-4xl text-neutral-400 mb-4"></i>
          {file ? (
            <>
              <p className="text-neutral-700 mb-2">Seçilen dosya:</p>
              <p className="text-primary font-medium mb-4">{file.name}</p>
              <button
                className="text-sm text-neutral-600 hover:text-neutral-800"
                onClick={() => setFile(null)}
              >
                <i className="fas fa-times mr-1"></i> Seçimi Temizle
              </button>
            </>
          ) : (
            <>
              <p className="text-neutral-600 mb-4">Dosyanızı buraya sürükleyin ya da dosya seçmek için tıklayın</p>
              <p className="text-xs text-neutral-500">Desteklenen formatlar: .pdf, .docx, .doc (Resim & Diagram içerikleri desteklenmektedir)</p>
            </>
          )}
          <input
            type="file"
            className="hidden"
            id="fileInput"
            accept=".pdf,.docx,.doc"
            onChange={handleFileChange}
          />
          {!file && (
            <button
              className="mt-4 px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
              onClick={() => document.getElementById('fileInput')?.click()}
            >
              Dosya Seç
            </button>
          )}
        </div>

        <div className="mt-6 space-y-2">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="useAI"
              className="rounded text-primary focus:ring-primary"
              checked={useAI}
              onChange={(e) => setUseAI(e.target.checked)}
            />
            <label htmlFor="useAI" className="ml-2 text-sm text-neutral-700">AI ile gelişmiş analiz</label>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="extractCode"
              className="rounded text-primary focus:ring-primary"
              checked={extractCode}
              onChange={(e) => setExtractCode(e.target.checked)}
            />
            <label htmlFor="extractCode" className="ml-2 text-sm text-neutral-700">Otomatik kategorizasyon</label>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="includeVisuals"
              className="rounded text-primary focus:ring-primary"
              checked={includeVisuals}
              onChange={(e) => setIncludeVisuals(e.target.checked)}
            />
            <label htmlFor="includeVisuals" className="ml-2 text-sm text-neutral-700">Görsel içeriği analiz et</label>
          </div>

          {includeVisuals && (
            <div className="ml-6 p-2 bg-amber-50 border border-amber-200 rounded-md text-xs text-amber-700">
              <i className="fas fa-info-circle mr-1"></i>
              <span>Görsel içerik analizi işlemi daha uzun sürebilir. Dokümandaki resim, şekil, diagram ve tablolar AI tarafından analiz edilecektir.</span>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`bg-white rounded-lg shadow-xl ${(selectedSource === 'jira-management' || selectedSource === 'jira-ai-bot') ? 'max-w-7xl w-full h-[90vh]' : 'max-w-md w-full'}`}>
        <div className="p-4 border-b border-neutral-200">
          <h2 className="text-lg font-medium">
            {step === "select-source" ? "Kaynak Seç" : selectedSource ? getSourceDisplayName(selectedSource) : "Analiz Yükle"}
          </h2>
        </div>

        {step === "select-source" ? (
          // Kaynak seçim ekranı
          <div className="p-6">
            <p className="text-neutral-600 mb-4 text-center">Analiz için bir veri kaynağı seçin</p>
            <div className="grid grid-cols-2 gap-4">
              {/* Doküman Yükleme */}
              <div
                className="border rounded-lg p-4 text-center hover:border-primary hover:shadow-md cursor-pointer transition-all"
                onClick={() => handleSourceSelect("document")}
              >
                <i className="fas fa-file-alt text-3xl text-primary mb-3"></i>
                <p className="font-medium">Doküman Yükle</p>
                <p className="text-xs text-neutral-500 mt-1">PDF, DOCX (Resim & Diagram destekli)</p>
              </div>

              {/* Metin Girişi */}
              <div
                className="border rounded-lg p-4 text-center hover:border-primary hover:shadow-md cursor-pointer transition-all"
                onClick={() => handleSourceSelect("text")}
              >
                <i className="fas fa-align-left text-3xl text-teal-500 mb-3"></i>
                <p className="font-medium">Metin Girişi</p>
                <p className="text-xs text-neutral-500 mt-1">Doğrudan metin analizi</p>
              </div>

              {/* Jira Hızlı Analiz */}
              <div
                className="border rounded-lg p-4 text-center hover:border-primary hover:shadow-md cursor-pointer transition-all"
                onClick={() => handleSourceSelect("jira")}
              >
                <i className="fab fa-jira text-3xl text-blue-500 mb-3"></i>
                <p className="font-medium">Jira</p>
                <p className="text-xs text-neutral-500 mt-1">Hızlı Analiz</p>
              </div>

              {/* Jira Proje Yönetimi */}
              <div
                className="border rounded-lg p-4 text-center hover:border-primary hover:shadow-md cursor-pointer transition-all"
                onClick={() => handleSourceSelect("jira-management")}
              >
                <i className="fas fa-project-diagram text-3xl text-blue-600 mb-3"></i>
                <p className="font-medium">Jira Yönetimi</p>
                <p className="text-xs text-neutral-500 mt-1">Proje Yönetimi</p>
              </div>

              {/* Jira AI Bot */}
              <div
                className="border rounded-lg p-4 text-center hover:border-primary hover:shadow-md cursor-pointer transition-all"
                onClick={() => handleSourceSelect("jira-ai-bot")}
              >
                <i className="fas fa-robot text-3xl text-purple-600 mb-3"></i>
                <p className="font-medium">Jira AI Bot</p>
                <p className="text-xs text-neutral-500 mt-1">Otomasyonel Analiz</p>
              </div>

              {/* ALM Entegrasyonu */}
              <div
                className="border rounded-lg p-4 text-center hover:border-primary hover:shadow-md cursor-pointer transition-all"
                onClick={() => handleSourceSelect("alm")}
              >
                <i className="fas fa-cubes text-3xl text-orange-500 mb-3"></i>
                <p className="font-medium">ALM</p>
                <p className="text-xs text-neutral-500 mt-1">Konfigürasyonu</p>
              </div>

              {/* TestRail Entegrasyonu */}
              <div
                className="border rounded-lg p-4 text-center hover:border-primary hover:shadow-md cursor-pointer transition-all"
                onClick={() => handleSourceSelect("testrail")}
              >
                <i className="fas fa-vial text-3xl text-green-500 mb-3"></i>
                <p className="font-medium">TestRail</p>
                <p className="text-xs text-neutral-500 mt-1">Konfigürasyonu</p>
              </div>

              {/* Confluence Entegrasyonu */}
              <div
                className="border rounded-lg p-4 text-center hover:border-primary hover:shadow-md cursor-pointer transition-all"
                onClick={() => handleSourceSelect("confluence")}
              >
                <i className="fab fa-confluence text-3xl text-blue-400 mb-3"></i>
                <p className="font-medium">Confluence</p>
                <p className="text-xs text-neutral-500 mt-1">Wiki Dokümanları</p>
              </div>

              {/* Visium Manage Entegrasyonu */}
              <div
                className="border rounded-lg p-4 text-center hover:border-primary hover:shadow-md cursor-pointer transition-all col-span-2"
                onClick={() => handleSourceSelect("visium")}
              >
                <i className="fas fa-project-diagram text-3xl text-purple-500 mb-3"></i>
                <p className="font-medium">Visium Manage</p>
                <p className="text-xs text-neutral-500 mt-1">Konfigürasyonu</p>
              </div>
            </div>
          </div>
        ) : (
          // Seçilen kaynağa göre ilgili form
          <>
            {selectedSource === "document" && renderDocumentUpload()}
            {selectedSource === "text" && renderTextInput()}
            {selectedSource === "jira" && renderJiraForm()}
            {selectedSource === "jira-management" && (
              <div className="h-[calc(90vh-120px)]">
                <JiraManagement projectId={1} />
              </div>
            )}
            {selectedSource === "jira-ai-bot" && (
              <div className="h-[calc(90vh-120px)]">
                <JiraAIBot projectId={1} />
              </div>
            )}
            {selectedSource === "alm" && renderAlmForm()}
            {selectedSource === "testrail" && renderTestRailForm()}
            {selectedSource === "confluence" && renderConfluenceForm()}
            {selectedSource === "visium" && renderVisiumForm()}
          </>
        )}

        <div className="px-4 py-3 bg-neutral-50 flex justify-end space-x-2 rounded-b-lg">
          {step === "upload" && (
            <button
              className="px-4 py-2 text-neutral-700 hover:bg-neutral-200 rounded"
              onClick={() => setStep("select-source")}
            >
              Geri
            </button>
          )}
          <button
            className="px-4 py-2 text-neutral-700 hover:bg-neutral-200 rounded"
            onClick={handleClose}
          >
            İptal
          </button>
          {step === "upload" && (
            <button
              className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 flex items-center"
              onClick={handleSubmit}
              disabled={
                uploadMutation.isPending ||
                (selectedSource === "document" && !file) ||
                (selectedSource === "text" && (textContent.trim().length < 500 || !textTitle.trim()))
              }
            >
              {uploadMutation.isPending ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Yükleniyor...</span>
                </>
              ) : (
                <span>Analiz Et</span>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentUploadModal;
