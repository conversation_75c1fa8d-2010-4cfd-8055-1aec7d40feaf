
> rest-express@1.0.0 dev
11:26:47 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 6 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
11:26:50 PM [express] GET /api/ai-models 200 in 2ms :: {"models":{"o1":{"displayName":"GPT-o1","deplo…
11:26:50 PM [express] GET /api/documents 304 in 1ms :: [{"id":28,"name":"demo.docx","type":"docx","co…
11:26:58 PM [express] POST /api/ai-models/set 200 in 0ms :: {"success":true,"message":"AI modeli 'o4'…
OpenAI ile doküman analizi hatası: AuthenticationError: 401 Incorrect API key provided: 8RCCsKey************************************************************************wegj. You can find your API key at https://platform.openai.com/account/api-keys.
    at Function.generate (/home/<USER>/workspace/node_modules/openai/src/error.ts:76:14)
    at OpenAI.makeStatusError (/home/<USER>/workspace/node_modules/openai/src/core.ts:462:21)
    at OpenAI.makeRequest (/home/<USER>/workspace/node_modules/openai/src/core.ts:526:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Object.analyzeDocument (/home/<USER>/workspace/server/services/openai.ts:198:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:87:34) {
  status: 401,
  headers: {
    'alt-svc': 'h3=":443"; ma=86400',
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '92cd4b1cba22ead2-ORD',
    connection: 'keep-alive',
    'content-length': '334',
    'content-type': 'application/json; charset=utf-8',
    date: 'Mon, 07 Apr 2025 23:27:03 GMT',
    server: 'cloudflare',
    'set-cookie': '__cf_bm=WOgjX2GaZp4PZBH.Q1v_eNPlsE6.KzcPgTabsm40i04-**********-1.0.1.1-_pOaGQ0ChtLKV4Zk8tH_pmrORjMc6Z4u5UFJ4Uvj0zesdZ7M10a283rqLZ2EnaNMl43maA.pumOWzlMEfhfFEKu3enqgtaHFFK03zJ0.Vyk; path=/; expires=Mon, 07-Apr-25 23:57:03 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=eKwIRrVZaGMBVBpwsGBNjIvrPT4nqVQRgFr0c79DYNk-**********307-0.0.1.1-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=********; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_c62ac3b89781296d711760a22bcbb628'
  },
  request_id: 'req_c62ac3b89781296d711760a22bcbb628',
  error: {
    message: 'Incorrect API key provided: 8RCCsKey************************************************************************wegj. You can find your API key at https://platform.openai.com/account/api-keys.',
    type: 'invalid_request_error',
    param: null,
    code: 'invalid_api_key'
  },
  code: 'invalid_api_key',
  param: null,
  type: 'invalid_request_error'
}
Doküman analiz hatası: Error: Doküman analizi başarısız oldu: 401 Incorrect API key provided: 8RCCsKey************************************************************************wegj. You can find your API key at https://platform.openai.com/account/api-keys.
    at Object.analyzeDocument (/home/<USER>/workspace/server/services/openai.ts:208:11)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:87:34)
11:27:03 PM [express] POST /api/documents/upload 201 in 1601ms :: {"document":{"id":29,"name":"demo.d…
11:27:03 PM [express] GET /api/documents 200 in 1ms :: [{"id":29,"name":"demo.docx","type":"docx","co…