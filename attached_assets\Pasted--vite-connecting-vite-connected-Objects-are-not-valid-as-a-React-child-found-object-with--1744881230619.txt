[vite] connecting...
[vite] connected.
Objects are not valid as a React child (found: object with keys {comment, captain<PERSON>ate, firstOfficer<PERSON>ate}). If you meant to render a collection of children, use an array instead.
Objects are not valid as a React child (found: object with keys {comment, captainRate, firstOfficer<PERSON>ate}). If you meant to render a collection of children, use an array instead.
The above error occurred in the <span> component:

    at span
    at div
    at div
    at div
    at div
    at div
    at ExtractedInfo (https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/src/components/extracted-info.tsx:20:26)
    at div
    at main
    at div
    at div
    at Dashboard (https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/src/pages/dashboard.tsx:30:37)
    at Route (https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=83928b97:323:16)
    at ProtectedRoute (https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/src/lib/protected-route.tsx:22:3)
    at Switch (https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=83928b97:379:17)
    at Router
    at AuthProvider (https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/src/hooks/use-auth.tsx:26:32)
    at QueryClientProvider (https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@tanstack_react-query.js?v=83928b97:2805:3)
    at App

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.
Objects are not valid as a React child (found: object with keys {comment, captainRate, firstOfficerRate}). If you meant to render a collection of children, use an array 