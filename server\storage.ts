import {
  users, type User, type InsertUser,
  documents, type Document, type InsertDocument,
  components, type Component, type InsertComponent,
  requirements, type Requirement, type InsertRequirement,
  apiEndpoints, type ApiEndpoint, type InsertApiEndpoint,
  testScenarios, type TestScenario, type InsertTestScenario,
  aiAnalysis, type AiAnalysis, type InsertAiAnalysis,
  aiLearningMemory, type AiLearningMemory, type InsertAiLearningMemory,
  aiInteractionHistory, type AiInteractionHistory, type InsertAiInteractionHistory,
  coverageValidation, type CoverageValidation, type InsertCoverageValidation,
  projects, type Project, type InsertProject,
  userProjects, type UserProject, type InsertUserProject,
  permissions, type Permission, type InsertPermission,
  userPermissions,
  apiConnections, type ApiConnection, type InsertApiConnection,
  processStatus, type ProcessStatus, type InsertProcessStatus
} from "@shared/schema";
import { sqliteStorage } from './storage-sqlite';
import { postgresStorage } from './storage-postgres';
import session from "express-session";

// Veritabanı türünü seçme
// Postgres veritabanını kullanmak için bu değişkenleri düzenleyin
const USE_POSTGRES = process.env.DATABASE_URL ? true : false;
const USE_SQLITE = !USE_POSTGRES && (process.env.USE_SQLITE === 'true' || true);

// Storage interface
export interface IStorage {
  // Session store for authentication
  sessionStore?: session.Store;

  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  getAllUsers(): Promise<User[]>; // Tüm kullanıcıları getir
  updateUser(id: number, data: Partial<User>): Promise<User | undefined>; // Kullanıcı bilgilerini güncelle
  
  // Document operations
  getDocuments(): Promise<Document[]>;
  getDocumentsByUserId(userId: number): Promise<Document[]>;
  getDocumentsByProjectId(projectId: number): Promise<Document[]>;
  getDocument(id: number): Promise<Document | undefined>;
  createDocument(document: InsertDocument): Promise<Document>;
  updateDocument(id: number, document: Partial<InsertDocument>): Promise<Document | undefined>;
  deleteDocument(id: number): Promise<boolean>;
  
  // Project operations
  getProjects(): Promise<Project[]>;
  getProjectsByUserId(userId: number): Promise<Project[]>;
  getProject(id: number): Promise<Project | undefined>;
  createProject(project: InsertProject): Promise<Project>;
  updateProject(id: number, data: Partial<InsertProject>): Promise<Project | undefined>;
  deleteProject(id: number): Promise<boolean>;
  
  // User-Project operations
  getUserProjects(userId: number): Promise<UserProject[]>;
  getProjectUsers(projectId: number): Promise<UserProject[]>;
  getUserProject(userId: number, projectId: number): Promise<UserProject | undefined>;
  addUserToProject(data: InsertUserProject): Promise<UserProject>;
  updateUserProjectRole(userId: number, projectId: number, role: string): Promise<UserProject | undefined>;
  removeUserFromProject(userId: number, projectId: number): Promise<boolean>;
  hasProjectAccess(userId: number, projectId: number, requiredRole?: string): Promise<boolean>;
  
  // Permission operations
  getUserPermissions(userId: number): Promise<Permission[]>;
  getAllPermissions(): Promise<Permission[]>;
  hasPermission(userId: number, permissionName: string): Promise<boolean>;
  grantPermission(userId: number, permissionId: number): Promise<void>;
  revokePermission(userId: number, permissionId: number): Promise<void>;
  
  // Component operations
  getComponentsByDocumentId(documentId: number): Promise<Component[]>;
  createComponent(component: InsertComponent): Promise<Component>;
  
  // Requirement operations
  getRequirements(): Promise<Requirement[]>;
  getRequirementsByDocumentId(documentId: number): Promise<Requirement[]>;
  createRequirement(requirement: InsertRequirement): Promise<Requirement>;
  
  // API Endpoint operations
  getApiEndpointsByDocumentId(documentId: number): Promise<ApiEndpoint[]>;
  createApiEndpoint(apiEndpoint: InsertApiEndpoint): Promise<ApiEndpoint>;
  
  // Test Scenario operations
  getTestScenariosByDocumentId(documentId: number): Promise<TestScenario[]>;
  createTestScenario(testScenario: InsertTestScenario): Promise<TestScenario>;
  updateTestScenario(id: number, testScenario: Partial<InsertTestScenario>): Promise<TestScenario | undefined>;
  
  // AI Analysis operations
  getAiAnalysisByDocumentId(documentId: number): Promise<AiAnalysis | undefined>;
  createAiAnalysis(aiAnalysis: InsertAiAnalysis): Promise<AiAnalysis>;
  saveAIAnalysis(documentId: number, analysisResult: any): Promise<void>;
  
  // Coverage Validation operations
  getCoverageValidationByDocumentId(documentId: number): Promise<CoverageValidation | undefined>;
  createCoverageValidation(data: InsertCoverageValidation): Promise<CoverageValidation>;
  deleteCoverageValidation(documentId: number): Promise<boolean>;
  
  // AI Learning Memory operations
  searchAiLearningMemory(queryPattern: string): Promise<AiLearningMemory[]>;
  getAiLearningMemoryById(id: number): Promise<AiLearningMemory | undefined>;
  createAiLearningMemory(memory: InsertAiLearningMemory): Promise<AiLearningMemory>;
  updateAiLearningMemory(id: number, memory: Partial<InsertAiLearningMemory>): Promise<AiLearningMemory | undefined>;
  incrementMemoryUsage(id: number): Promise<AiLearningMemory | undefined>;
  
  // AI Interaction History operations
  getAiInteractionHistory(userId?: number, limit?: number): Promise<AiInteractionHistory[]>;
  createAiInteractionHistory(interaction: InsertAiInteractionHistory): Promise<AiInteractionHistory>;
  updateInteractionFeedback(id: number, rating: number): Promise<AiInteractionHistory | undefined>;
  
  // API Connection operations
  getApiConnectionsByProjectId(projectId: number): Promise<ApiConnection[]>;
  getApiConnection(id: number): Promise<ApiConnection | undefined>;
  createApiConnection(data: InsertApiConnection): Promise<ApiConnection>;
  updateApiConnection(id: number, data: Partial<InsertApiConnection>): Promise<ApiConnection | undefined>;
  deleteApiConnection(id: number): Promise<boolean>;
  
  // Process Status operations - İşlem durumu takibi
  getProcessStatusById(id: number): Promise<ProcessStatus | undefined>;
  getProcessStatusByDocumentId(documentId: number): Promise<ProcessStatus[]>;
  getProcessStatusByProjectId(projectId: number): Promise<ProcessStatus[]>;
  createProcessStatus(data: InsertProcessStatus): Promise<ProcessStatus>;
  updateProcessStatus(id: number, data: Partial<InsertProcessStatus>): Promise<ProcessStatus | undefined>;
  deleteProcessStatus(id: number): Promise<boolean>;
}

export class MemStorage implements IStorage {
  // Process Status operations
  async getProcessStatusById(id: number): Promise<ProcessStatus | undefined> {
    return this.processStatuses.get(id);
  }
  
  async getProcessStatusByDocumentId(documentId: number): Promise<ProcessStatus[]> {
    return Array.from(this.processStatuses.values()).filter(
      process => process.documentId === documentId
    );
  }
  
  async getProcessStatusByProjectId(projectId: number): Promise<ProcessStatus[]> {
    return Array.from(this.processStatuses.values()).filter(
      process => process.projectId === projectId
    );
  }
  
  async createProcessStatus(data: InsertProcessStatus): Promise<ProcessStatus> {
    const id = this.processStatusIdCounter++;
    const now = new Date();
    
    const processStatus: ProcessStatus = {
      ...data,
      id,
      startedAt: now,
      completedAt: null
    };
    
    this.processStatuses.set(id, processStatus);
    return processStatus;
  }
  
  async updateProcessStatus(id: number, data: Partial<InsertProcessStatus>): Promise<ProcessStatus | undefined> {
    const processStatus = this.processStatuses.get(id);
    
    if (!processStatus) {
      return undefined;
    }
    
    const updatedProcessStatus = {
      ...processStatus,
      ...data
    };
    
    this.processStatuses.set(id, updatedProcessStatus);
    return updatedProcessStatus;
  }
  
  async deleteProcessStatus(id: number): Promise<boolean> {
    return this.processStatuses.delete(id);
  }
  sessionStore?: session.Store;
  
  private users: Map<number, User>;
  private documents: Map<number, Document>;
  private components: Map<number, Component>;
  private requirements: Map<number, Requirement>;
  private apiEndpoints: Map<number, ApiEndpoint>;
  private testScenarios: Map<number, TestScenario>;
  private aiAnalysis: Map<number, AiAnalysis>;
  private coverageValidations: Map<number, CoverageValidation>;
  private aiLearningMemory: Map<number, AiLearningMemory>;
  private aiInteractionHistory: Map<number, AiInteractionHistory>;
  private projects: Map<number, Project>;
  private userProjects: Map<string, UserProject>; // format: "userId-projectId"
  private userPermissions: Map<string, string>; // format: "userId-permissionName"
  private apiConnections: Map<number, ApiConnection>; // API bağlantıları
  private processStatuses: Map<number, ProcessStatus>; // İşlem durumları
  
  private userIdCounter: number;
  private documentIdCounter: number;
  private componentIdCounter: number;
  private requirementIdCounter: number;
  private apiEndpointIdCounter: number;
  private testScenarioIdCounter: number;
  private aiAnalysisIdCounter: number;
  private coverageValidationIdCounter: number;
  private aiLearningMemoryIdCounter: number;
  private aiInteractionHistoryIdCounter: number;
  private projectIdCounter: number;
  private apiConnectionIdCounter: number;
  private processStatusIdCounter: number;
  
  constructor() {
    // MemoryStore oluştur
    const MemoryStore = require('memorystore')(session);
    this.sessionStore = new MemoryStore({
      checkPeriod: 86400000 // 24 saatte bir süresi dolan oturumları temizle
    });
    
    this.users = new Map();
    this.documents = new Map();
    this.components = new Map();
    this.requirements = new Map();
    this.apiEndpoints = new Map();
    this.testScenarios = new Map();
    this.aiAnalysis = new Map();
    this.coverageValidations = new Map();
    this.aiLearningMemory = new Map();
    this.aiInteractionHistory = new Map();
    this.projects = new Map();
    this.userProjects = new Map();
    this.userPermissions = new Map();
    this.apiConnections = new Map();
    this.processStatuses = new Map();
    
    this.userIdCounter = 1;
    this.documentIdCounter = 1;
    this.componentIdCounter = 1;
    this.requirementIdCounter = 1;
    this.apiEndpointIdCounter = 1;
    this.testScenarioIdCounter = 1;
    this.aiAnalysisIdCounter = 1;
    this.coverageValidationIdCounter = 1;
    this.aiLearningMemoryIdCounter = 1;
    this.aiInteractionHistoryIdCounter = 1;
    this.projectIdCounter = 1;
    this.apiConnectionIdCounter = 1;
    this.processStatusIdCounter = 1;
  }
  
  // User operations
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }
  
  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }
  
  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.userIdCounter++;
    const now = new Date();
    const user: User = { 
      ...insertUser, 
      id, 
      lastLogin: null,
      createdAt: now
    };
    this.users.set(id, user);
    return user;
  }
  
  async getAllUsers(): Promise<User[]> {
    return Array.from(this.users.values());
  }
  
  async updateUser(id: number, data: Partial<User>): Promise<User | undefined> {
    const user = this.users.get(id);
    if (!user) {
      return undefined;
    }
    const updatedUser = {
      ...user,
      ...data
    };
    this.users.set(id, updatedUser);
    return updatedUser;
  }
  
  // Document operations
  async getDocuments(): Promise<Document[]> {
    return Array.from(this.documents.values());
  }
  
  async getDocumentsByUserId(userId: number): Promise<Document[]> {
    return Array.from(this.documents.values()).filter(doc => doc.createdBy === userId);
  }
  
  async getDocumentsByProjectId(projectId: number): Promise<Document[]> {
    return Array.from(this.documents.values()).filter(doc => doc.projectId === projectId);
  }
  
  async getDocument(id: number): Promise<Document | undefined> {
    return this.documents.get(id);
  }
  
  async createDocument(insertDocument: InsertDocument): Promise<Document> {
    const id = this.documentIdCounter++;
    const now = new Date();
    
    const document: Document = {
      ...insertDocument,
      id,
      analyzedAt: null,
      createdAt: now
    };
    
    this.documents.set(id, document);
    return document;
  }
  
  async updateDocument(id: number, updateData: Partial<InsertDocument>): Promise<Document | undefined> {
    const document = this.documents.get(id);
    
    if (!document) {
      return undefined;
    }
    
    const updatedDocument = {
      ...document,
      ...updateData,
    };
    
    this.documents.set(id, updatedDocument);
    return updatedDocument;
  }
  
  async deleteDocument(id: number): Promise<boolean> {
    // Önce ilişkili verileri siliyoruz
    // Belgeye ait tüm test senaryolarını bul ve sil
    const testScenarios = await this.getTestScenariosByDocumentId(id);
    testScenarios.forEach(scenario => this.testScenarios.delete(scenario.id));
    
    // Belgeye ait tüm API uç noktalarını bul ve sil
    const apiEndpoints = await this.getApiEndpointsByDocumentId(id);
    apiEndpoints.forEach(endpoint => this.apiEndpoints.delete(endpoint.id));
    
    // Belgeye ait tüm gereksinimleri bul ve sil
    const requirements = await this.getRequirementsByDocumentId(id);
    requirements.forEach(requirement => this.requirements.delete(requirement.id));
    
    // Belgeye ait tüm bileşenleri bul ve sil
    const components = await this.getComponentsByDocumentId(id);
    components.forEach(component => this.components.delete(component.id));
    
    // Belgeye ait AI analiz verilerini sil
    const aiAnalysis = await this.getAiAnalysisByDocumentId(id);
    if (aiAnalysis) {
      this.aiAnalysis.delete(aiAnalysis.id);
    }
    
    // Son olarak belgenin kendisini sil
    const result = this.documents.delete(id);
    console.log(`Doküman ve ilişkili tüm veriler silindi: ID=${id}, Sonuç: ${result}`);
    return result;
  }
  
  // Component operations
  async getComponentsByDocumentId(documentId: number): Promise<Component[]> {
    return Array.from(this.components.values()).filter(
      component => component.documentId === documentId
    );
  }
  
  async createComponent(insertComponent: InsertComponent): Promise<Component> {
    const id = this.componentIdCounter++;
    const component: Component = { ...insertComponent, id };
    this.components.set(id, component);
    return component;
  }
  
  // Requirement operations
  async getRequirements(): Promise<Requirement[]> {
    return Array.from(this.requirements.values());
  }
  
  async getRequirementsByDocumentId(documentId: number): Promise<Requirement[]> {
    return Array.from(this.requirements.values()).filter(
      requirement => requirement.documentId === documentId
    );
  }
  
  async createRequirement(insertRequirement: InsertRequirement): Promise<Requirement> {
    const id = this.requirementIdCounter++;
    const requirement: Requirement = { ...insertRequirement, id };
    this.requirements.set(id, requirement);
    return requirement;
  }
  
  // API Endpoint operations
  async getApiEndpointsByDocumentId(documentId: number): Promise<ApiEndpoint[]> {
    return Array.from(this.apiEndpoints.values()).filter(
      apiEndpoint => apiEndpoint.documentId === documentId
    );
  }
  
  async createApiEndpoint(insertApiEndpoint: InsertApiEndpoint): Promise<ApiEndpoint> {
    const id = this.apiEndpointIdCounter++;
    const apiEndpoint: ApiEndpoint = { ...insertApiEndpoint, id };
    this.apiEndpoints.set(id, apiEndpoint);
    return apiEndpoint;
  }
  
  // Test Scenario operations
  async getTestScenariosByDocumentId(documentId: number): Promise<TestScenario[]> {
    return Array.from(this.testScenarios.values()).filter(
      testScenario => testScenario.documentId === documentId
    );
  }
  
  async createTestScenario(insertTestScenario: InsertTestScenario): Promise<TestScenario> {
    const id = this.testScenarioIdCounter++;
    const testScenario: TestScenario = { ...insertTestScenario, id };
    this.testScenarios.set(id, testScenario);
    return testScenario;
  }
  
  async updateTestScenario(id: number, updateData: Partial<InsertTestScenario>): Promise<TestScenario | undefined> {
    const testScenario = this.testScenarios.get(id);
    
    if (!testScenario) {
      return undefined;
    }
    
    const updatedTestScenario = {
      ...testScenario,
      ...updateData,
    };
    
    this.testScenarios.set(id, updatedTestScenario);
    return updatedTestScenario;
  }
  
  // AI Analysis operations
  async getAiAnalysisByDocumentId(documentId: number): Promise<AiAnalysis | undefined> {
    return Array.from(this.aiAnalysis.values()).find(
      analysis => analysis.documentId === documentId
    );
  }
  
  async createAiAnalysis(insertAiAnalysis: InsertAiAnalysis): Promise<AiAnalysis> {
    const id = this.aiAnalysisIdCounter++;
    const now = new Date();
    
    const aiAnalysis: AiAnalysis = {
      ...insertAiAnalysis,
      id,
      analyzedAt: now
    };
    
    this.aiAnalysis.set(id, aiAnalysis);
    return aiAnalysis;
  }

  async saveAIAnalysis(documentId: number, analysisResult: any): Promise<void> {
    try {
      // AI analiz sonuçlarını veritabanına kaydet
      await this.createAiAnalysis({
        documentId: documentId,
        observations: analysisResult
      });
    } catch (error) {
      console.error('AI analiz sonuçları kaydedilirken hata:', error);
      throw error;
    }
  }
  
  // Coverage Validation operations
  async getCoverageValidationByDocumentId(documentId: number): Promise<CoverageValidation | undefined> {
    return Array.from(this.coverageValidations.values()).find(
      validation => validation.documentId === documentId
    );
  }
  
  async createCoverageValidation(insertCoverageValidation: InsertCoverageValidation): Promise<CoverageValidation> {
    const id = this.coverageValidationIdCounter++;
    const now = new Date();
    
    const coverageValidation: CoverageValidation = {
      ...insertCoverageValidation,
      id,
      createdAt: now
    };
    
    this.coverageValidations.set(id, coverageValidation);
    return coverageValidation;
  }
  
  async deleteCoverageValidation(documentId: number): Promise<boolean> {
    const validation = await this.getCoverageValidationByDocumentId(documentId);
    
    if (!validation) {
      return false;
    }
    
    return this.coverageValidations.delete(validation.id);
  }
  
  // AI Learning Memory operations
  async searchAiLearningMemory(queryPattern: string): Promise<AiLearningMemory[]> {
    return Array.from(this.aiLearningMemory.values()).filter(
      memory => memory.queryPattern.includes(queryPattern) || queryPattern.includes(memory.queryPattern)
    ).sort((a, b) => b.successMetric - a.successMetric); // En başarılı sonuçları ilk sıraya koy
  }
  
  async getAiLearningMemoryById(id: number): Promise<AiLearningMemory | undefined> {
    return this.aiLearningMemory.get(id);
  }
  
  async createAiLearningMemory(insertMemory: InsertAiLearningMemory): Promise<AiLearningMemory> {
    const id = this.aiLearningMemoryIdCounter++;
    const now = new Date();
    
    const aiLearningMemory: AiLearningMemory = {
      ...insertMemory,
      id,
      lastUsed: now,
      createdAt: now
    };
    
    this.aiLearningMemory.set(id, aiLearningMemory);
    return aiLearningMemory;
  }
  
  async updateAiLearningMemory(id: number, updateData: Partial<InsertAiLearningMemory>): Promise<AiLearningMemory | undefined> {
    const memory = this.aiLearningMemory.get(id);
    
    if (!memory) {
      return undefined;
    }
    
    const updatedMemory = {
      ...memory,
      ...updateData,
      lastUsed: new Date()
    };
    
    this.aiLearningMemory.set(id, updatedMemory);
    return updatedMemory;
  }
  
  async incrementMemoryUsage(id: number): Promise<AiLearningMemory | undefined> {
    const memory = this.aiLearningMemory.get(id);
    
    if (!memory) {
      return undefined;
    }
    
    const updatedMemory = {
      ...memory,
      frequencyUsed: memory.frequencyUsed + 1,
      lastUsed: new Date()
    };
    
    this.aiLearningMemory.set(id, updatedMemory);
    return updatedMemory;
  }
  
  // AI Interaction History operations
  async getAiInteractionHistory(userId?: number, limit?: number): Promise<AiInteractionHistory[]> {
    let interactions = Array.from(this.aiInteractionHistory.values());
    
    if (userId) {
      interactions = interactions.filter(interaction => interaction.userId === userId);
    }
    
    // En son etkileşimler ilk sırada olacak şekilde sırala
    interactions.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    
    return limit ? interactions.slice(0, limit) : interactions;
  }
  
  async createAiInteractionHistory(insertInteraction: InsertAiInteractionHistory): Promise<AiInteractionHistory> {
    const id = this.aiInteractionHistoryIdCounter++;
    const now = new Date();
    
    const aiInteractionHistory: AiInteractionHistory = {
      ...insertInteraction,
      id,
      createdAt: now
    };
    
    this.aiInteractionHistory.set(id, aiInteractionHistory);
    return aiInteractionHistory;
  }
  
  async updateInteractionFeedback(id: number, rating: number): Promise<AiInteractionHistory | undefined> {
    const interaction = this.aiInteractionHistory.get(id);
    
    if (!interaction) {
      return undefined;
    }
    
    const updatedInteraction = {
      ...interaction,
      feedbackRating: rating
    };
    
    this.aiInteractionHistory.set(id, updatedInteraction);
    return updatedInteraction;
  }
  
  // API Connection operations
  async getApiConnectionsByProjectId(projectId: number): Promise<ApiConnection[]> {
    return Array.from(this.apiConnections.values()).filter(
      connection => connection.projectId === projectId
    );
  }
  
  async getApiConnection(id: number): Promise<ApiConnection | undefined> {
    return this.apiConnections.get(id);
  }
  
  async createApiConnection(data: InsertApiConnection): Promise<ApiConnection> {
    const id = this.apiConnectionIdCounter++;
    const now = new Date();
    
    const apiConnection: ApiConnection = {
      ...data,
      id,
      lastSyncAt: null,
      createdAt: now,
      updatedAt: now
    };
    
    this.apiConnections.set(id, apiConnection);
    return apiConnection;
  }
  
  async updateApiConnection(id: number, data: Partial<InsertApiConnection>): Promise<ApiConnection | undefined> {
    const connection = this.apiConnections.get(id);
    
    if (!connection) {
      return undefined;
    }
    
    const updatedConnection = {
      ...connection,
      ...data,
      updatedAt: new Date()
    };
    
    this.apiConnections.set(id, updatedConnection);
    return updatedConnection;
  }
  
  async deleteApiConnection(id: number): Promise<boolean> {
    return this.apiConnections.delete(id);
  }
  
  // Project operations
  async getProjects(): Promise<Project[]> {
    return Array.from(this.projects.values());
  }
  
  async getProjectsByUserId(userId: number): Promise<Project[]> {
    const userProjects = await this.getUserProjects(userId);
    const projectIds = userProjects.map(up => up.projectId);
    return Array.from(this.projects.values()).filter(project => projectIds.includes(project.id));
  }
  
  async getProject(id: number): Promise<Project | undefined> {
    return this.projects.get(id);
  }
  
  async createProject(project: InsertProject): Promise<Project> {
    const id = this.projectIdCounter++;
    const now = new Date();
    const newProject: Project = {
      ...project,
      id,
      createdAt: now
    };
    this.projects.set(id, newProject);
    return newProject;
  }
  
  async updateProject(id: number, data: Partial<InsertProject>): Promise<Project | undefined> {
    const project = this.projects.get(id);
    if (!project) {
      return undefined;
    }
    const updatedProject = {
      ...project,
      ...data
    };
    this.projects.set(id, updatedProject);
    return updatedProject;
  }
  
  async deleteProject(id: number): Promise<boolean> {
    // İlk olarak bu projeye ait tüm dokümanları sil
    const documents = await this.getDocumentsByProjectId(id);
    for (const doc of documents) {
      await this.deleteDocument(doc.id);
    }
    
    // Proje-kullanıcı ilişkilerini sil
    const projectUsers = Array.from(this.userProjects.values()).filter(up => up.projectId === id);
    for (const userProject of projectUsers) {
      this.userProjects.delete(`${userProject.userId}-${userProject.projectId}`);
    }
    
    // Son olarak projeyi sil
    return this.projects.delete(id);
  }
  
  // User-Project operations
  async getUserProjects(userId: number): Promise<UserProject[]> {
    return Array.from(this.userProjects.values()).filter(up => up.userId === userId);
  }
  
  async getProjectUsers(projectId: number): Promise<UserProject[]> {
    return Array.from(this.userProjects.values()).filter(up => up.projectId === projectId);
  }
  
  async getUserProject(userId: number, projectId: number): Promise<UserProject | undefined> {
    return this.userProjects.get(`${userId}-${projectId}`);
  }
  
  async addUserToProject(data: InsertUserProject): Promise<UserProject> {
    const key = `${data.userId}-${data.projectId}`;
    const userProject: UserProject = {
      ...data
    };
    this.userProjects.set(key, userProject);
    return userProject;
  }
  
  async updateUserProjectRole(userId: number, projectId: number, role: string): Promise<UserProject | undefined> {
    const key = `${userId}-${projectId}`;
    const userProject = this.userProjects.get(key);
    if (!userProject) {
      return undefined;
    }
    const updatedUserProject = {
      ...userProject,
      role
    };
    this.userProjects.set(key, updatedUserProject);
    return updatedUserProject;
  }
  
  async removeUserFromProject(userId: number, projectId: number): Promise<boolean> {
    const key = `${userId}-${projectId}`;
    return this.userProjects.delete(key);
  }
  
  async hasProjectAccess(userId: number, projectId: number, requiredRole?: string): Promise<boolean> {
    const userProject = await this.getUserProject(userId, projectId);
    if (!userProject) {
      return false;
    }
    
    if (!requiredRole) {
      return true;
    }
    
    // Role-based permission checking
    // owner > editor > viewer
    const roles: Record<string, number> = { 'owner': 3, 'editor': 2, 'viewer': 1 };
    const userRoleLevel = userProject.role ? (roles[userProject.role] || 0) : 0;
    const requiredRoleLevel = roles[requiredRole] || 0;
    
    return userRoleLevel >= requiredRoleLevel;
  }
  
  // Permission operations
  async getUserPermissions(userId: number): Promise<Permission[]> {
    const userPermissionKeys = Array.from(this.userPermissions.keys())
      .filter(key => key.startsWith(`${userId}-`));
    
    const permissionNames = userPermissionKeys.map(key => {
      const parts = key.split('-');
      return parts[1];
    });
    
    // MemStorage için varsayılan izinler kullanılıyor
    // Gerçek uygulamada burada veritabanından çekilmeli
    const permissionList: Permission[] = [
      { id: 1, name: 'user_management', description: 'Kullanıcı yönetimi', createdAt: new Date() },
      { id: 2, name: 'project_management', description: 'Proje yönetimi', createdAt: new Date() },
      { id: 3, name: 'prompt_edit', description: 'Prompt düzenleme', createdAt: new Date() },
      { id: 4, name: 'api_settings', description: 'API ayarları', createdAt: new Date() },
      { id: 5, name: 'maintenance', description: 'Bakım modu', createdAt: new Date() },
      { id: 6, name: 'backup', description: 'Yedekleme', createdAt: new Date() },
      { id: 7, name: 'statistics', description: 'İstatistikler', createdAt: new Date() }
    ];
    
    return permissionList.filter(p => permissionNames.includes(p.name));
  }
  
  async getAllPermissions(): Promise<Permission[]> {
    // Sistemde tanımlı tüm izinler
    const permissionList: Permission[] = [
      { id: 1, name: 'user_management', description: 'Kullanıcı yönetimi', createdAt: new Date() },
      { id: 2, name: 'project_management', description: 'Proje yönetimi', createdAt: new Date() },
      { id: 3, name: 'prompt_edit', description: 'Prompt düzenleme', createdAt: new Date() },
      { id: 4, name: 'api_settings', description: 'API ayarları', createdAt: new Date() },
      { id: 5, name: 'maintenance', description: 'Bakım modu', createdAt: new Date() },
      { id: 6, name: 'backup', description: 'Yedekleme', createdAt: new Date() },
      { id: 7, name: 'statistics', description: 'İstatistikler', createdAt: new Date() }
    ];
    
    return permissionList;
  }
  
  async hasPermission(userId: number, permissionName: string): Promise<boolean> {
    // Admin kullanıcılar için her zaman true dön
    const user = await this.getUser(userId);
    if (user && user.role === 'admin') {
      return true;
    }
    
    // Normal kullanıcılar için izin kontrolü yap
    const key = `${userId}-${permissionName}`;
    return this.userPermissions.has(key);
  }
  
  async grantPermission(userId: number, permissionId: number): Promise<void> {
    // MemStorage için varsayılan izinler
    const permissionList: Permission[] = [
      { id: 1, name: 'user_management', description: 'Kullanıcı yönetimi', createdAt: new Date() },
      { id: 2, name: 'project_management', description: 'Proje yönetimi', createdAt: new Date() },
      { id: 3, name: 'prompt_edit', description: 'Prompt düzenleme', createdAt: new Date() },
      { id: 4, name: 'api_settings', description: 'API ayarları', createdAt: new Date() },
      { id: 5, name: 'maintenance', description: 'Bakım modu', createdAt: new Date() },
      { id: 6, name: 'backup', description: 'Yedekleme', createdAt: new Date() },
      { id: 7, name: 'statistics', description: 'İstatistikler', createdAt: new Date() }
    ];
    
    const permission = permissionList.find(p => p.id === permissionId);
    
    if (!permission) {
      throw new Error(`İzin ID'si ${permissionId} bulunamadı`);
    }
    
    const key = `${userId}-${permission.name}`;
    this.userPermissions.set(key, permission.name);
  }
  
  async revokePermission(userId: number, permissionId: number): Promise<void> {
    // MemStorage için varsayılan izinler
    const permissionList: Permission[] = [
      { id: 1, name: 'user_management', description: 'Kullanıcı yönetimi', createdAt: new Date() },
      { id: 2, name: 'project_management', description: 'Proje yönetimi', createdAt: new Date() },
      { id: 3, name: 'prompt_edit', description: 'Prompt düzenleme', createdAt: new Date() },
      { id: 4, name: 'api_settings', description: 'API ayarları', createdAt: new Date() },
      { id: 5, name: 'maintenance', description: 'Bakım modu', createdAt: new Date() },
      { id: 6, name: 'backup', description: 'Yedekleme', createdAt: new Date() },
      { id: 7, name: 'statistics', description: 'İstatistikler', createdAt: new Date() }
    ];
    
    const permission = permissionList.find(p => p.id === permissionId);
    
    if (!permission) {
      throw new Error(`İzin ID'si ${permissionId} bulunamadı`);
    }
    
    const key = `${userId}-${permission.name}`;
    this.userPermissions.delete(key);
  }
}

// Storage seçimini yap ve uygun storage örneğini dışa aktar
// PostgreSQL veritabanını öncelikli olarak kullanıyoruz
export const storage: IStorage = USE_POSTGRES 
  ? postgresStorage 
  : (USE_SQLITE ? sqliteStorage : new MemStorage());
