import { type Document } from "@shared/schema";
import { format } from "date-fns";
import { useState, useEffect, useMemo, useCallback, memo } from "react";
import { deleteDocument, reanalyzeDocument, fetchAIModels, setAIModel } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import { queryClient } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  FileText,
  FilePlus,
  RefreshCw,
  Trash2,
  Clock,
  Settings,
  Zap,
  BarChart,
  Brain,
  Check,
  FileIcon,
  FileType,
  FileSpreadsheet,
  History,
  Folder,
  FolderOpen,
  FileCode,
  Cpu,
  MoreHorizontal,
  ChevronDown,
  ChevronUp,
  RefreshCcw,
  Search
} from "lucide-react";

interface SidebarProps {
  documents: Document[];
  activeDocumentId: number | null;
  onDocumentSelect: (documentId: number) => void;
  onUploadClick: () => void;
  documentsLoaded?: boolean;
}

const Sidebar = ({ documents, activeDocumentId, onDocumentSelect, onUploadClick, documentsLoaded = false }: SidebarProps) => {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<Document | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isLoadingDocuments, setIsLoadingDocuments] = useState(true);
  const [aiModelMenuOpen, setAiModelMenuOpen] = useState(false);
  const [showAllDocuments, setShowAllDocuments] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentModel, setCurrentModel] = useState<{
    modelType: string;
    displayName: string;
  }>({ modelType: "o4", displayName: "GPT-4o" });
  const [availableModels, setAvailableModels] = useState<Record<string, { displayName: string }>>({
    o1: { displayName: "GPT-o1" },
    o3: { displayName: "GPT-o3-mini" },
    o4: { displayName: "GPT-4o" }
  });
  const { toast } = useToast();

  // Dökümanların yüklenme durumunu izle
  useEffect(() => {
    if (documents && documents.length > 0) {
      setIsLoadingDocuments(false);
    }
  }, [documents]);

  // AI modellerini yükle - sadece bir kez
  useEffect(() => {
    let isMounted = true;

    async function loadAIModels() {
      try {
        const response = await fetchAIModels();
        const modelInfo = await response.json();

        if (!isMounted) return;

        if (modelInfo && modelInfo.current) {
          setCurrentModel({
            modelType: modelInfo.current.modelType || "o1",
            displayName: modelInfo.current.displayName || "GPT-o1"
          });

          // Mevcut modelleri set et
          if (modelInfo.models) {
            const models: Record<string, { displayName: string }> = {};
            Object.entries(modelInfo.models).forEach(([key, value]: [string, any]) => {
              if (value && value.displayName) {
                models[key] = { displayName: value.displayName };
              }
            });

            if (Object.keys(models).length > 0) {
              setAvailableModels(models);
            }
          }
        }
      } catch (error) {
        console.error("AI modelleri yüklenemedi:", error);
        // Hata durumunda varsayılan modeller kullanılır
      }
    }

    loadAIModels();

    return () => {
      isMounted = false;
    };
  }, []);

  // Sort documents by created date (newest first) - memoized
  const sortedDocuments = useMemo(() => {
    if (!documents || documents.length === 0) return [];
    return [...documents].sort((a, b) => {
      return new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime();
    });
  }, [documents]);

  // Filter documents based on search term - memoized
  const filteredDocuments = useMemo(() => {
    if (!searchTerm.trim()) return sortedDocuments;

    const term = searchTerm.toLowerCase();
    return sortedDocuments.filter(doc =>
      doc.name.toLowerCase().includes(term) ||
      (doc.type && doc.type.toLowerCase().includes(term))
    );
  }, [sortedDocuments, searchTerm]);

  // Get recent documents (last 3) - memoized
  const recentDocuments = useMemo(() => {
    return sortedDocuments.slice(0, 3);
  }, [sortedDocuments]);

  // Handle document deletion - memoized
  const handleDeleteDocument = useCallback(async () => {
    if (!documentToDelete) return;

    setIsProcessing(true);
    try {
      const response = await deleteDocument(documentToDelete.id);
      const result = await response.json();

      if (result.success) {
        // Önce aktif doküman silindiyse seçimi kaldır
        if (activeDocumentId === documentToDelete.id) {
          onDocumentSelect(0); // 0 değeri ile doküman seçimini sıfırlıyoruz
        }

        // Görsel olarak anında kaldırmak için dokümanı listeden manuel olarak filtrele
        // Bu satır önemli: documents prop'unu doğrudan güncelleyemeyiz, bu yüzden
        // kullanıcı arayüzünde silinmiş gibi göstermek için queryClient'daki verileri değiştiriyoruz
        const deletedId = documentToDelete.id;

        // API'den gelen belgeleri kullanarak önbelleği güncelle
        queryClient.setQueryData<Document[]>(["/api/documents"], (oldData) => {
          return oldData ? oldData.filter(doc => doc.id !== deletedId) : [];
        });

        // Ardından veritabanındaki güncel durumu çek
        queryClient.invalidateQueries({ queryKey: ["/api/documents"] });

        toast({
          title: "Doküman silindi",
          description: result.message || "Doküman başarıyla silindi.",
          variant: "default",
        });
      } else {
        toast({
          title: "Doküman silinemedi",
          description: result.message || "Bir hata oluştu.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Doküman silinemedi",
        description: error instanceof Error ? error.message : "Bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
      setDeleteDialogOpen(false);
      setDocumentToDelete(null);
    }
  }, [documentToDelete, activeDocumentId, onDocumentSelect, toast]);

  // Dokümanı yeniden analiz etme - memoized
  const handleReanalyzeDocument = useCallback(async (doc: Document) => {
    setIsProcessing(true);
    try {
      const response = await reanalyzeDocument(doc.id);
      const result = await response.json();

      if (result.success) {
        toast({
          title: "Doküman yeniden analiz edildi",
          description: result.message || "Doküman başarıyla yeniden analiz edildi.",
          variant: "default",
        });

        // İlgili dokümanın verilerini yenile
        queryClient.invalidateQueries({ queryKey: [`/api/documents/${doc.id}`] });
        queryClient.invalidateQueries({ queryKey: [`/api/documents/${doc.id}/components`] });
        queryClient.invalidateQueries({ queryKey: [`/api/documents/${doc.id}/requirements`] });
        queryClient.invalidateQueries({ queryKey: [`/api/documents/${doc.id}/api-endpoints`] });
        queryClient.invalidateQueries({ queryKey: [`/api/documents/${doc.id}/ai-analysis`] });
      } else {
        toast({
          title: "Doküman analiz edilemedi",
          description: result.message || "Bir hata oluştu.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Doküman analiz edilemedi",
        description: error instanceof Error ? error.message : "Bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  }, [toast]);

  // Memoized icon getter
  const getDocumentIcon = useCallback((type?: string) => {
    if (!type) return <FileIcon size={16} className="mr-2 text-neutral-600" />;

    switch (type.toLowerCase()) {
      case 'pdf':
        return <FileText size={16} className="mr-2 text-red-600" />;
      case 'docx':
      case 'doc':
        return <FileType size={16} className="mr-2 text-blue-600" />;
      case 'xlsx':
      case 'xls':
        return <FileSpreadsheet size={16} className="mr-2 text-green-600" />;
      default:
        return <FileIcon size={16} className="mr-2 text-neutral-600" />;
    }
  }, []);

  // Memoized time ago calculator
  const getTimeAgo = useCallback((date?: string | Date | null) => {
    if (!date) return '';

    const now = new Date();
    const documentDate = new Date(date);
    const diffMinutes = Math.floor((now.getTime() - documentDate.getTime()) / (1000 * 60));

    if (diffMinutes < 60) {
      return `${diffMinutes} dakika önce`;
    } else if (diffMinutes < 24 * 60) {
      const hours = Math.floor(diffMinutes / 60);
      return `${hours} saat önce`;
    } else {
      const days = Math.floor(diffMinutes / (60 * 24));
      return `${days} gün önce`;
    }
  }, []);

  // AI Modelini değiştirme - memoized
  const handleChangeAIModel = useCallback(async (modelType: string) => {
    if (isProcessing) return;

    setIsProcessing(true);
    try {
      const response = await setAIModel(modelType);
      const result = await response.json();

      if (result.success) {
        setCurrentModel({
          modelType: modelType,
          displayName: result.model?.displayName || availableModels[modelType].displayName
        });

        toast({
          title: "AI Modeli değiştirildi",
          description: result.message || `Model başarıyla ${availableModels[modelType].displayName} olarak değiştirildi.`,
          variant: "default",
        });

        // Eğer uyarı mesajı varsa göster
        if (result.warningMessage) {
          console.warn("AI Model uyarısı:", result.warningMessage);

          // UI'da uyarı göster
          toast({
            title: "Model uyarısı",
            description: result.warningMessage,
            variant: "destructive",
          });
        }
      } else {
        toast({
          title: "Model değiştirilemedi",
          description: result.message || "Bir hata oluştu.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Model değiştirilemedi",
        description: error instanceof Error ? error.message : "Bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
      setAiModelMenuOpen(false);
    }
  }, [isProcessing, availableModels, toast]);

  return (
    <aside className="w-64 bg-white border-r border-neutral-200 flex flex-col">
      {/* Silme onay dialogu */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Dokümanı silmek istediğinize emin misiniz?</AlertDialogTitle>
            <AlertDialogDescription>
              <div>
                <p className="font-medium text-neutral-900 mb-2">{documentToDelete?.name}</p>
                <p>Bu işlem geri alınamaz. Bu doküman ve ilişkili tüm veriler (gereksinimler, test senaryoları, analiz raporları) kalıcı olarak silinecektir.</p>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isProcessing}>İptal</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteDocument}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Siliniyor...</span>
                </>
              ) : (
                "Evet, dokümanı sil"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Header */}
      <div className="p-4 border-b border-neutral-200">
        <Button
          variant="default"
          size="sm"
          className="w-full py-2.5 px-4 rounded-md flex items-center justify-center shadow-sm transition-all hover:shadow-md gap-2"
          onClick={onUploadClick}
        >
          <FilePlus size={18} />
          <span className="font-medium">Kaynak Ekle</span>
        </Button>
      </div>

      {/* Content area with scroll */}
      <ScrollArea className="flex-1">
        <div className="p-4">
          <h2 className="text-sm font-semibold text-neutral-500 uppercase tracking-wider mb-3">Kaynaklar</h2>

          {/* Search input */}
          <div className="relative mb-4">
            <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400" />
            <Input
              type="text"
              placeholder="Kaynak ara..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-9 text-sm border-neutral-200 focus:border-primary"
            />
          </div>
          <ul className="space-y-1.5">
            {isLoadingDocuments && !documentsLoaded ? (
              <li className="text-neutral-500 text-sm px-3 py-6 text-center bg-neutral-50 rounded-lg border border-dashed border-neutral-200 my-4 fade-in">
                <div className="flex flex-col items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mb-2"></div>
                  <p>Kaynaklar yükleniyor...</p>
                  <p className="text-xs mt-1">Lütfen bekleyin</p>
                </div>
              </li>
            ) : (
              <>
                {filteredDocuments.slice(0, showAllDocuments ? filteredDocuments.length : 5).map((doc, index) => (
                  <li
                    key={doc.id}
                    style={{ animationDelay: `${index * 50}ms` }}
                    className={`group slide-in-left shadow-card ${activeDocumentId === doc.id ? 'bg-primary/10 text-primary shadow-sm' : 'hover:bg-neutral-100 text-neutral-700'} rounded-md px-3 py-2.5 cursor-pointer transition-all duration-200`}
                    onClick={() => onDocumentSelect(doc.id)}
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center overflow-hidden">
                        <div className="flex-shrink-0 mr-2">
                          {getDocumentIcon(doc.type)}
                        </div>
                        <div className="truncate">
                          <span className={`truncate ${activeDocumentId === doc.id ? 'font-semibold' : 'font-medium'}`}>{doc.name}</span>
                          <div className="text-xs text-neutral-500 truncate mt-0.5">
                            {doc.type ? doc.type.toUpperCase() : 'TXT'} · {getTimeAgo(doc.createdAt)}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center ml-2">
                        <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-200">


                          <button
                            className="p-1.5 rounded-full hover:bg-red-100 text-red-600 hover:text-red-700 border border-red-200 transition-all duration-200"
                            onClick={(e) => {
                              e.stopPropagation();
                              setDocumentToDelete(doc);
                              setDeleteDialogOpen(true);
                            }}
                            disabled={isProcessing}
                            title="Sil"
                          >
                            <Trash2 size={14} />
                          </button>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
                {filteredDocuments.length > 5 && (
                  <li className="my-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full text-xs justify-center py-1.5 text-primary hover:text-primary/80 gap-1 border border-dashed border-primary/30 hover:border-primary/50"
                      onClick={() => setShowAllDocuments(!showAllDocuments)}
                    >
                      {showAllDocuments ? (
                        <>
                          <ChevronUp size={14} />
                          <span>Daha az göster</span>
                        </>
                      ) : (
                        <>
                          <ChevronDown size={14} />
                          <span>Daha fazla göster ({filteredDocuments.length - 5} kaynak daha)</span>
                        </>
                      )}
                    </Button>
                  </li>
                )}

                {filteredDocuments.length === 0 && !isLoadingDocuments && searchTerm && (
                  <li className="text-neutral-500 text-sm px-3 py-6 text-center bg-neutral-50 rounded-lg border border-dashed border-neutral-200 my-4 fade-in">
                    <Search size={32} className="mx-auto mb-2 text-neutral-400" />
                    <p>"{searchTerm}" için sonuç bulunamadı</p>
                    <p className="text-xs mt-1">Farklı anahtar kelimeler deneyin</p>
                  </li>
                )}

                {sortedDocuments.length === 0 && !isLoadingDocuments && !searchTerm && (
                  <li className="text-neutral-500 text-sm px-3 py-6 text-center bg-neutral-50 rounded-lg border border-dashed border-neutral-200 my-4 fade-in">
                    <FolderOpen size={32} className="mx-auto mb-2 text-neutral-400" />
                    <p>Henüz doküman yok</p>
                    <p className="text-xs mt-1">Yeni doküman yüklemek için butona tıklayın</p>
                  </li>
                )}
              </>
            )}
          </ul>
        </div>

        <div className="p-4 border-t border-neutral-200">
          <h2 className="text-sm font-semibold text-neutral-500 uppercase tracking-wider mb-3">Son İşlemler</h2>
          <ul className="space-y-1">
            {isLoadingDocuments && !documentsLoaded ? (
              <li className="text-neutral-500 text-sm px-3 py-4 text-center bg-neutral-50 rounded-md border border-dashed border-neutral-200 my-2 fade-in">
                <div className="flex flex-col items-center justify-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary mb-2"></div>
                  <p className="text-xs">Yükleniyor...</p>
                </div>
              </li>
            ) : (
              <>
                {recentDocuments.map((doc, index) => (
                  <li
                    key={`recent-${doc.id}`}
                    onClick={() => onDocumentSelect(doc.id)}
                    style={{ animationDelay: `${index * 100 + 200}ms` }}
                    className="slide-in-right hover:bg-neutral-100 rounded-md px-3 py-2 cursor-pointer group transition-all"
                  >
                    <div className="flex items-center text-neutral-700">
                      <History size={14} className="mr-2 text-neutral-500" />
                      <span className="truncate">{doc.name}</span>
                    </div>
                    <div className="text-xs text-neutral-500 ml-6">
                      {getTimeAgo(doc.createdAt)}
                    </div>
                  </li>
                ))}
                {recentDocuments.length === 0 && !isLoadingDocuments && (
                  <li className="text-neutral-500 text-sm px-3 py-4 text-center bg-neutral-50 rounded-md border border-dashed border-neutral-200 my-2 fade-in">
                    <Clock size={20} className="mx-auto mb-2 text-neutral-400" />
                    <p>Son işlem yok</p>
                  </li>
                )}
              </>
            )}
          </ul>
        </div>
      </ScrollArea>

      {/* Footer with AI model selection */}
      <div className="p-4 border-t border-neutral-200 bg-neutral-50">
        <div className="flex items-center justify-between">
          <div className="text-xs text-neutral-500">AI Durumu:</div>
          <div className="text-xs font-medium text-emerald-600 flex items-center">
            <div className="h-2 w-2 rounded-full bg-emerald-500 animate-pulse mr-1.5"></div>
            <span>Bağlı</span>
          </div>
        </div>

        <DropdownMenu open={aiModelMenuOpen} onOpenChange={setAiModelMenuOpen}>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="mt-3 text-sm flex items-center justify-between w-full px-3 py-2 transition-all"
              disabled={isProcessing}
            >
              <div className="flex items-center gap-2">
                {currentModel.modelType === 'o1' && <Zap size={16} className="text-amber-500" />}
                {currentModel.modelType === 'o3' && <BarChart size={16} className="text-green-500" />}
                {currentModel.modelType === 'o4' && <Brain size={16} className="text-indigo-500" />}
                <span>{currentModel.displayName}</span>
              </div>
              <div className="bg-primary/10 text-primary text-xs px-1.5 py-0.5 rounded-full">
                Model
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-64 p-1">
            <div className="px-3 py-2 text-xs text-neutral-700 font-medium">
              AI Model Seçimi
            </div>

            {Object.entries(availableModels).map(([modelType, model]) => (
              <DropdownMenuItem
                key={modelType}
                className={`py-2.5 px-3 rounded-md my-1 ${currentModel.modelType === modelType ? 'bg-primary/10 font-medium text-primary' : ''}`}
                disabled={isProcessing}
                onClick={() => handleChangeAIModel(modelType)}
              >
                <div className="flex items-center w-full">
                  <div className="flex items-center gap-2">
                    {modelType === 'o1' && <Zap size={16} className="text-amber-500" />}
                    {modelType === 'o3' && <BarChart size={16} className="text-green-500" />}
                    {modelType === 'o4' && <Brain size={16} className="text-indigo-500" />}
                    <span>{model.displayName}</span>
                  </div>
                  {currentModel.modelType === modelType && (
                    <Check size={16} className="ml-auto text-primary" />
                  )}
                </div>
              </DropdownMenuItem>
            ))}

            <DropdownMenuSeparator className="my-1" />

            <div className="px-3 py-2 text-xs text-neutral-500 bg-neutral-50 rounded-md mx-1 my-1">
              Her model farklı özellik ve token limitine sahiptir. Görselli analizler için GPT-4o modeli kullanılır.
            </div>

            <DropdownMenuItem
              onClick={() => window.location.href = '/prompt-editor'}
              className="py-2.5 px-3 rounded-md my-1"
            >
              <Settings size={16} className="mr-2 text-purple-500" />
              <span>Promptları Düzenle</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </aside>
  );
};

export default memo(Sidebar);