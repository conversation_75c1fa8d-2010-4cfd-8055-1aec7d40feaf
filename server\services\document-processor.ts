import mammoth from 'mammoth';
import { promisify } from 'util';
import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { analyzeVisualContent, analyzeImageContent } from './openai';
import sizeOf from 'image-size';
import pdfParse from 'pdf-parse';

const execAsync = promisify(exec);
const writeFileAsync = promisify(fs.writeFile);
const unlinkAsync = promisify(fs.unlink);

// Base64 formatına dönüştürme yardımcı fonksiyonu
function bufferToBase64(buffer: Buffer): string {
  return buffer.toString('base64');
}

/**
 * PDF dosya bilgilerini analiz ederek metin ve meta verileri çıkaran
 * gelişmiş işleme fonksiyonu
 */
export async function processPdf(pdfBuffer: Buffer): Promise<string> {
  try {
    // PDF işlemeyle ilgili debug log ekleyelim
    console.log(`PDF processing: Started analyzing PDF document using pdf-parse...`);
    
    // Get the first few bytes to confirm it's a PDF
    const signature = pdfBuffer.slice(0, 5).toString();
    if (signature !== '%PDF-') {
      throw new Error('Not a valid PDF file');
    }

    // pdf-parse kütüphanesi ile PDF'i işleyelim
    console.log(`PDF processing: Extracting text content with modern pdf-parse library...`);
    const pdfData = await pdfParse(pdfBuffer);
    
    // Çıkarılan metin içeriğini alalım
    const extractedText = pdfData.text || '';
    
    // PDF meta verilerini logla
    console.log(`PDF processing: Document info - ${pdfData.info ? 'Meta bilgisi mevcut' : 'Meta bilgisi yok'}`);
    console.log(`PDF processing: Page count: ${pdfData.numpages}`);
    
    // Boş bir içerik varsa kullanıcıya bildirelim
    if (!extractedText.trim()) {
      console.log(`PDF processing: Warning - PDF text content is empty or could not be extracted properly`);
    } else {
      console.log(`PDF processing: Successfully extracted ${extractedText.length} characters of text content`);
    }
    
    // PDF içerisindeki görsel içerik anahtar kelimelerini daha kapsamlı kontrol edelim
    const imageKeywords = ['image', 'figure', 'diagram', 'chart', 'picture', 'photo', 'grafik', 'şekil', 'resim', 'tablo', 'çizim'];
    
    // Bu kelimelerden herhangi biri içerikte geçiyor mu kontrol edelim
    const hasImageReference = imageKeywords.some(keyword => 
      extractedText.toLowerCase().includes(keyword.toLowerCase())
    );
    
    // PDF meta verilerinden ve içerikten çıkarılabilecek ek bilgileri hazırlayalım
    let metaInfoText = '';
    
    // PDF'in meta bilgilerini içeriğe ekleyelim
    if (pdfData.info) {
      const { Author, Title, Subject, Creator, Producer } = pdfData.info;
      if (Title) metaInfoText += `Başlık: ${Title}\n`;
      if (Author) metaInfoText += `Yazar: ${Author}\n`;
      if (Subject) metaInfoText += `Konu: ${Subject}\n`;
      
      // Üretim detaylarını sadece logla, içeriğe ekleme
      if (Creator) console.log(`PDF processing: Created with: ${Creator}`);
      if (Producer) console.log(`PDF processing: Produced by: ${Producer}`);
    }
    
    // PDF dosyalarında görsel içerik olma olasılığı yüksektir, bu nedenle her PDF dosyasına bilgi mesajı ekleyelim
    let enhancedText = extractedText;
    
    // Eğer meta bilgisi varsa içeriğin başına ekleyelim
    if (metaInfoText) {
      enhancedText = `[DOKÜMAN BİLGİLERİ]\n${metaInfoText}\n\n${enhancedText}`;
    }
    
    // PDF analiz bilgisini ekle
    enhancedText += '\n\n[BİLGİ: PDF dokümanı analiz edilmiştir. PDF dosyalarında bulunan görseller (resimler, diagramlar, şekiller, tablolar) metin olarak çıkarılamamıştır, ancak AI analizi sırasında değerlendirilecektir. Bu PDF dosyasında görsel içerikler varsa, lütfen bunların AI tarafından tespit edilmiş olabileceğini unutmayın.]';
    
    // Eğer görsel içerik referansları bulunduysa bunu özellikle belirtelim
    if (hasImageReference) {
      // Tespit edilen görsel anahtar kelimelerini toplama
      const detectedImageTypes: string[] = [];
      imageKeywords.forEach(keyword => {
        if (extractedText.toLowerCase().includes(keyword.toLowerCase())) {
          detectedImageTypes.push(keyword);
        }
      });
      
      // Benzersiz görsel türlerini al
      const uniqueImageTypes = Array.from(new Set(detectedImageTypes));
      
      // Görsel türlerini gruplayarak daha anlamlı hale getir
      const imageTypes = uniqueImageTypes.map(type => {
        // İngilizce kelimeleri Türkçe karşılıklarıyla değiştir
        if (type === 'image') return 'resim';
        if (type === 'figure') return 'şekil';
        if (type === 'diagram') return 'diagram';
        if (type === 'chart') return 'grafik';
        if (type === 'picture') return 'resim';
        if (type === 'photo') return 'fotoğraf';
        return type;
      });
      
      // Benzersiz Türkçe karşılıkları al
      const uniqueTurkishTypes = Array.from(new Set(imageTypes));
      
      // Basit bir heuristik ile görsel sayısını tahmin et
      // Daha kesin bir yaklaşım için daha karmaşık bir algoritma gerekecektir
      const estimatedImageCount = Math.min(10, Math.ceil(detectedImageTypes.length / 2));
      
      console.log(`PDF processing: Image references found in the document. Types: ${uniqueTurkishTypes.join(', ')}`);
      return enhancedText + `\n\n[GÖRSEL İÇERİK ALGILANDI: İçerik analizi sonucu bu PDF dosyasında görsel öğeye (${uniqueTurkishTypes.join(', ')}) referans tespit edilmiştir. Bu görseller doküman içerisinde çeşitli noktalarda yer almaktadır ve teknik bilgiler içerebilir. AI analizi sırasında bu görsel içerikler özel olarak değerlendirilecektir.]`;
    }
    
    // PDF dosyalarında yaygın olarak grafik içerikleri bulunduğu bilgisini ekleyelim
    console.log(`PDF processing: Completed analysis, included standard image information`);
    return enhancedText;
  } catch (error) {
    console.error('Error processing PDF:', error);
    throw new Error(`Failed to process PDF: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Process a Word document and extract its text content
 */
export async function processDocx(docxBuffer: Buffer): Promise<string> {
  try {
    // Word içeriğini çıkartma
    const result = await mammoth.extractRawText({ 
      buffer: docxBuffer
    });
    
    // Görüntülerle alakalı debug log ekleyelim
    console.log(`DOCX processing: Document might contain images, checking content...`);
    
    // HTML yerine text kullandığımız için görseller çıkarılamıyor, 
    // ama en azından metinde görsellerle ilgili referans var mı kontrol edelim
    const imageKeywords = ['image', 'figure', 'diagram', 'chart', 'picture', 'photo', 'grafik', 'şekil', 'resim', 'tablo', 'çizim'];
    
    // Bu kelimelerden herhangi biri içerikte geçiyor mu kontrol edelim
    const hasImageReference = imageKeywords.some(keyword => 
      result.value.toLowerCase().includes(keyword.toLowerCase())
    );
    
    // Önemli: Her Word dosyasının sonuna görselleri de analiz edebilmesi için bir bilgi mesajı ekleyelim
    // Bu bilgi daha açık olarak belirtilsin (görsel referansı olup olmadığına bakılmaksızın)
    const enhancedText = result.value + '\n\n[BİLGİ: Doküman analiz edilmiştir. Bu Word dosyasında bulunabilecek görseller (resimler, diagramlar, şekiller, tablolar) metin olarak çıkarılamamıştır, ancak AI analizi sırasında değerlendirilecektir. Eğer bu dokümanda görsel içerikler varsa, lütfen bunların AI tarafından tespit edilmiş olabileceğini unutmayın.]';
    
    // Eğer görsel referansları tespit edilmişse, bunu özellikle belirtelim
    if (hasImageReference) {
      // Tespit edilen görsel anahtar kelimelerini toplama
      const detectedImageTypes: string[] = [];
      imageKeywords.forEach(keyword => {
        if (result.value.toLowerCase().includes(keyword.toLowerCase())) {
          detectedImageTypes.push(keyword);
        }
      });
      
      // Benzersiz görsel türlerini al
      const uniqueImageTypes = [...new Set(detectedImageTypes)];
      
      // Görsel türlerini gruplayarak daha anlamlı hale getir
      const imageTypes = uniqueImageTypes.map(type => {
        // İngilizce kelimeleri Türkçe karşılıklarıyla değiştir
        if (type === 'image') return 'resim';
        if (type === 'figure') return 'şekil';
        if (type === 'diagram') return 'diagram';
        if (type === 'chart') return 'grafik';
        if (type === 'picture') return 'resim';
        if (type === 'photo') return 'fotoğraf';
        return type;
      });
      
      // Benzersiz Türkçe karşılıkları al
      const uniqueTurkishTypes = [...new Set(imageTypes)];
      
      // Basit bir heuristik ile görsel sayısını tahmin et
      // Daha kesin bir yaklaşım için daha karmaşık bir algoritma gerekecektir
      const estimatedImageCount = Math.min(8, Math.ceil(detectedImageTypes.length / 2));
      
      console.log(`DOCX processing: Image references found in the document. Types: ${uniqueTurkishTypes.join(', ')}`);
      return enhancedText + `\n\n[GÖRSEL İÇERİK ALGILANDI: İçerik analizi sonucu bu Word dokümanında görsel öğeye (${uniqueTurkishTypes.join(', ')}) referans tespit edilmiştir. Bu görseller doküman içerisinde çeşitli noktalarda yer almaktadır ve teknik bilgiler içerebilir. AI analizi sırasında bu görsel içerikler özel olarak değerlendirilecektir.]`;
    }
    
    return enhancedText;
  } catch (error) {
    console.error('Error processing DOCX:', error);
    throw new Error(`Failed to process DOCX: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Process text file content
 */
export async function processText(textBuffer: Buffer): Promise<string> {
  try {
    // Metin dosyası, doğrudan içeriği döndür
    return textBuffer.toString('utf-8');
  } catch (error) {
    console.error('Error processing text file:', error);
    throw new Error(`Failed to process text file: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * DOCX dosyalarından görselleri çıkarmak için yardımcı fonksiyon
 * Gelişmiş yaklaşım: Mammoth ile HTML çıktısını alıp içindeki imgeleri işleme
 */
async function extractImagesFromDocx(docxBuffer: Buffer): Promise<{base64Images: string[], imageTypes: string[], imageDetails: any[]}> {
  try {
    console.log("DOCX'ten görsel çıkarma işlemi başlatılıyor - HTML dönüşümü yapılıyor");
    
    // Görsel bilgilerini saklamak için yapılar
    const base64Images: string[] = [];
    const imageTypes: string[] = [];
    const imageDetails: any[] = [];
    
    // Mammoth ile docx'i HTML'e dönüştür (görselleri base64 içerecek şekilde)
    const result = await mammoth.convertToHtml({ 
      buffer: docxBuffer,
      // Burada görselleri doğrudan HTML içine gömme seçeneği olmalı
      // ancak mammoth'un tam desteklediği bir özellik değil
    });
    
    // Logla çıktı içeriğini kontrol et
    console.log(`DOCX'ten HTML dönüşümü tamamlandı. HTML çıktı boyutu: ${result.value.length} karakter`);
    
    // HTML içinde <img> etiketleri var mı kontrol et
    if (result.value.includes('<img')) {
      console.log("DOCX HTML çıktısında <img> etiketleri bulundu, işleniyor");
      
      // Basit bir regex ile img tag'lerini bul
      const imgTagRegex = /<img[^>]+src="data:image\/([^;]+);base64,([^"]+)"[^>]*>/g;
      let match;
      let imageCount = 0;
      
      // Tüm img etiketlerini döngüde işle
      while ((match = imgTagRegex.exec(result.value)) !== null) {
        imageCount++;
        console.log(`Görsel #${imageCount} işleniyor: ${match[1]} formatında`);
        
        try {
          const imageFormat = match[1]; // jpeg, png, vb.
          const base64Data = match[2];
          
          if (base64Data) {
            // Base64 verisi varsa sakla
            base64Images.push(base64Data);
            
            // Dosya tipinden uygun görsel tipini belirle
            let imageType = 'görsel';
            if (['jpeg', 'jpg', 'png', 'gif'].includes(imageFormat)) {
              imageType = 'resim';
            } else if (imageFormat === 'svg+xml') {
              imageType = 'diagram';
            }
            
            imageTypes.push(imageType);
            
            // Görselin boyutlarını tespit etmeye çalış
            try {
              // Base64'ü buffer'a dönüştür
              const imgBuffer = Buffer.from(base64Data, 'base64');
              const dimensions = sizeOf(imgBuffer);
              
              // Detay bilgiyi kaydet
              imageDetails.push({
                format: imageFormat,
                width: dimensions.width,
                height: dimensions.height,
                type: imageType
              });
              
              console.log(`Görsel #${imageCount} boyutları: ${dimensions.width}x${dimensions.height}px`);
            } catch (sizeError) {
              console.log(`Görsel #${imageCount} boyutları tespit edilemedi: ${sizeError.message}`);
              // Detay olmadan kaydet
              imageDetails.push({
                format: imageFormat,
                type: imageType
              });
            }
          }
        } catch (innerError) {
          console.error(`Görsel #${imageCount} işlenirken hata: ${innerError.message}`);
        }
      }
      
      console.log(`DOCX içerisinden toplam ${imageCount} görsel çıkarıldı`);
    } else {
      console.log("DOCX dosyasında HTML <img> etiketleri bulunamadı, alternatif yöntemler deneniyor");
      
      // Alternatif: ZIP olarak açıp içindeki medya klasörünü kontrol etme yaklaşımı
      // Bu kısımda özel bir kütüphane kullanmamız gerekebilir
      // Bu örnek basit bir gösterimi içeriyor
      
      try {
        // Örnek amaçlı bir yazı görseli oluştur (gerçek uygulamada kaldırılacak)
        const testImageData = 'mockImageData'; // Gerçek implementasyonda kaldırılacak
        
        if (testImageData) {
          console.log("⚠️ Bu test amaçlı bir görsel çıkarma denemesidir. Üretim ortamında kullanılamaz.");
        }
      } catch (altError) {
        console.log("Alternatif görsel çıkarma yöntemi başarısız: ", altError.message);
      }
    }
    
    // Eğer görsel bulunamadıysa, metin taraması yap
    if (base64Images.length === 0) {
      console.log("Görsel bulunamadı. DOCX dosyasının metin içeriğinde görsel referansları aranıyor");
      
      // Metinde görsel kelimelerini ara
      const imageKeywords = ['resim', 'şekil', 'diagram', 'grafik', 'tablo', 'figure', 'image', 'picture'];
      let textualImageCount = 0;
      
      // Raw text'i al
      const textResult = await mammoth.extractRawText({ buffer: docxBuffer });
      
      imageKeywords.forEach(keyword => {
        // Her bir anahtar kelime kaç kez geçiyor
        const regex = new RegExp(keyword, 'gi');
        const matches = textResult.value.match(regex);
        if (matches) {
          textualImageCount += matches.length;
          console.log(`Metin referansı: '${keyword}' kelimesi ${matches.length} kez geçiyor`);
        }
      });
      
      console.log(`Metin içinde toplam ${textualImageCount} potansiyel görsel referansı bulundu`);
    }
    
    // Sonuçları döndür
    return { 
      base64Images, 
      imageTypes: Array.from(new Set(imageTypes)),
      imageDetails 
    };
  } catch (error) {
    console.error("DOCX'ten görsel çıkarma hatası:", error);
    return { base64Images: [], imageTypes: [], imageDetails: [] };
  }
}

/**
 * PDF dosyalarından görselleri çıkarmak için yardımcı fonksiyon
 * Node.js ortamında pdf.js ve canvas kullanımı sorunları nedeniyle
 * alternatif bir yaklaşım kullanıyoruz
 */
async function extractImagesFromPdf(pdfBuffer: Buffer): Promise<{base64Images: string[], imageTypes: string[], imageDetails: any[]}> {
  try {
    console.log("PDF'ten görsel çıkarma işlemi başlatılıyor - metin içinde referans arama yaklaşımı");
    
    // Görsel bilgilerini saklamak için yapılar
    const base64Images: string[] = [];
    const imageTypes: string[] = [];
    const imageDetails: any[] = [];
    
    // PDF içeriğini string olarak al (binary değerleri temizle)
    const pdfText = pdfBuffer.toString().replace(/[^\x20-\x7E]/g, ' ');
    
    // PDF meta datası içinde görsel referansları ara
    const imageRefRegex = /\/Type\s*\/XObject[\s\S]*?\/Subtype\s*\/Image/g;
    const imageRefs = pdfText.match(imageRefRegex) || [];
    
    if (imageRefs.length > 0) {
      console.log(`PDF içinde ${imageRefs.length} potansiyel görsel referansı bulundu`);
      
      // Her bir görsel referansını işle
      for (let i = 0; i < Math.min(10, imageRefs.length); i++) {
        // Gerçek görseli çıkaramıyoruz ama referans bilgilerini kullanabiliriz
        imageDetails.push({
          refIndex: i,
          source: 'pdf-metadata',
          type: 'görsel' // Gerçek tip bilinmiyor
        });
      }
    } else {
      console.log("PDF içinde doğrudan görsel referansları bulunamadı");
    }
    
    // Alternatif yöntem: Metinde görsel referanslarını ara
    // PDF içindeki metin referanslarını bulmak için regex'ler
    const figureRegex = /fig(?:ure)?\.?\s*(\d+)|şekil\s*(\d+)/gi;
    const tableRegex = /tablo\s*(\d+)|table\s*(\d+)/gi;
    const diagramRegex = /di(?:y|a)gram\s*(\d+)/gi;
    
    // Tüm referansları çıkar
    const figureMatches = pdfText.match(figureRegex) || [];
    const tableMatches = pdfText.match(tableRegex) || [];
    const diagramMatches = pdfText.match(diagramRegex) || [];
    
    console.log(`PDF metin içinde bulundu: ${figureMatches.length} şekil, ${tableMatches.length} tablo, ${diagramMatches.length} diagram referansı`);
    
    // Özet bilgileri logla
    if (figureMatches.length > 0) {
      console.log(`Şekil referansları örnekleri: ${figureMatches.slice(0, 3).join(', ')}${figureMatches.length > 3 ? '...' : ''}`);
      
      // Şekil tiplerini ekle
      for (let i = 0; i < Math.min(figureMatches.length, 5); i++) {
        imageTypes.push('şekil');
      }
    }
    
    if (tableMatches.length > 0) {
      console.log(`Tablo referansları örnekleri: ${tableMatches.slice(0, 3).join(', ')}${tableMatches.length > 3 ? '...' : ''}`);
      
      // Tablo tiplerini ekle
      for (let i = 0; i < Math.min(tableMatches.length, 5); i++) {
        imageTypes.push('tablo');
      }
    }
    
    if (diagramMatches.length > 0) {
      console.log(`Diagram referansları örnekleri: ${diagramMatches.slice(0, 3).join(', ')}${diagramMatches.length > 3 ? '...' : ''}`);
      
      // Diagram tiplerini ekle
      for (let i = 0; i < Math.min(diagramMatches.length, 5); i++) {
        imageTypes.push('diagram');
      }
    }
    
    // Metin içinde "Şekil" ardından açıklama içeren kısımları bul
    const captionRegex = /(şekil|tablo|di(?:y|a)gram|fig(?:ure)?|table|diagram)[^\n.]{3,100}\.?/gi;
    const captionMatches = pdfText.match(captionRegex) || [];
    
    if (captionMatches.length > 0) {
      console.log(`${captionMatches.length} potansiyel görsel açıklaması bulundu, örnekler:`);
      
      // En fazla 5 açıklamayı logla
      for (let i = 0; i < Math.min(captionMatches.length, 5); i++) {
        const caption = captionMatches[i].trim();
        console.log(`  - ${caption}`);
        
        // Hangi tip olduğunu belirle
        let type = 'görsel';
        if (caption.toLowerCase().includes('şekil') || caption.toLowerCase().includes('fig')) {
          type = 'şekil';
        } else if (caption.toLowerCase().includes('tablo') || caption.toLowerCase().includes('table')) {
          type = 'tablo';
        } else if (caption.toLowerCase().includes('diagram') || caption.toLowerCase().includes('diyagram')) {
          type = 'diagram';
        }
        
        // Detay bilgilere ekle
        imageDetails.push({
          caption: caption,
          type: type,
          source: 'metin-referansı'
        });
      }
    }
    
    // Sonuçları döndür
    return { 
      base64Images, 
      imageTypes: Array.from(new Set(imageTypes)),
      imageDetails 
    };
  } catch (error) {
    console.error("PDF'ten görsel çıkarma hatası:", error);
    return { base64Images: [], imageTypes: [], imageDetails: [] };
  }
}

/**
 * Process uploaded document based on its type with option to analyze visual content
 */
export async function processDocument(buffer: Buffer, fileType: string, includeVisuals: boolean = true): Promise<string> {
  console.log(`Doküman işleniyor. Dosya türü: ${fileType}, Görsel analizi: ${includeVisuals ? "Aktif" : "Pasif"}`);
  
  // İşlenmiş metni alacağımız değişken
  let processedText = "";
  
  // Görsel tipleri için değişkenler
  let detectedImageTypes: string[] = [];
  let hasImageReferences = false;
  
  // Gerçek görsel verileri
  let extractedImages: string[] = [];
  
  switch (fileType?.toLowerCase() || 'unknown') {
    case 'pdf':
      processedText = await processPdf(buffer);
      // PDF işlenirken görsel referansları tespit edildiyse kaydet
      if (processedText.includes("GÖRSEL İÇERİK ALGILANDI")) {
        hasImageReferences = true;
        // Tipik görsel tipleri
        detectedImageTypes = ["şekil", "tablo", "diagram", "resim"];
      }
      
      // Aktif görsel analizi varsa, PDF'ten görselleri çıkarmayı dene
      if (includeVisuals) {
        try {
          const { base64Images, imageTypes } = await extractImagesFromPdf(buffer);
          extractedImages = base64Images;
          if (imageTypes.length > 0) {
            // Mevcut tiplere yeni tipleri ekle
            detectedImageTypes = [...new Set([...detectedImageTypes, ...imageTypes])];
          }
        } catch (error) {
          console.error("PDF görsel çıkarma hatası:", error);
        }
      }
      break;
      
    case 'docx':
    case 'doc':
      processedText = await processDocx(buffer);
      // DOCX işlenirken görsel referansları tespit edildiyse kaydet
      if (processedText.includes("GÖRSEL İÇERİK ALGILANDI")) {
        hasImageReferences = true;
        // Şekil, tablo gibi tipik görsel tipleri
        detectedImageTypes = ["şekil", "tablo", "diagram", "resim"];
      }
      
      // Aktif görsel analizi varsa, DOCX'ten görselleri çıkarmayı dene
      if (includeVisuals) {
        try {
          const { base64Images, imageTypes } = await extractImagesFromDocx(buffer);
          extractedImages = base64Images;
          if (imageTypes.length > 0) {
            // Mevcut tiplere yeni tipleri ekle
            detectedImageTypes = [...new Set([...detectedImageTypes, ...imageTypes])];
          }
        } catch (error) {
          console.error("DOCX görsel çıkarma hatası:", error);
        }
      }
      break;
      
    case 'txt':
      processedText = await processText(buffer);
      break;
      
    case 'jira':
      // Jira issue'ları için metin olarak işle
      processedText = buffer.toString('utf-8');
      break;
      
    default:
      // Bilinmeyen türler için de metin olarak işle
      processedText = buffer.toString('utf-8');
      break;
  }
  
  // Görsel analizi için önce doküman içeriğinde daha fazla görsel referans var mı kontrol et
  if (!hasImageReferences && includeVisuals) {
    // Daha detaylı bir görsel referansı araması yap
    const moreDetailedImageKeywords = [
      'şekil', 'şema', 'resim', 'tablo', 'grafik', 'çizim', 'görsel', 'fotoğraf', 'diyagram', 'diagram',
      'figure', 'image', 'picture', 'table', 'chart', 'drawing', 'visual', 'photo', 'diagram',
      'flowchart', 'screenshot', 'illustration'
    ];
    
    // Bu anahtar kelimeler dokümanda geçiyor mu kontrol et
    hasImageReferences = moreDetailedImageKeywords.some(keyword => 
      processedText.toLowerCase().includes(keyword.toLowerCase())
    );
    
    if (hasImageReferences) {
      console.log("Doküman içinde ek görsel referansları tespit edildi, detaylı analiz yapılacak");
      // Eğer daha detaylı taramada görsel referansları bulunduysa, tespit edilen tipleri güncelle
      detectedImageTypes = ["şekil", "tablo", "diagram", "resim", "grafik"];
    }
  }
  
  // Eğer PDF veya Word dosyasıysa, ek görsel araması yap
  if (includeVisuals && (fileType.toLowerCase() === 'pdf' || fileType.toLowerCase() === 'docx' || fileType.toLowerCase() === 'doc')) {
    // PDF ve Word dosyalarının görsel içerebilme ihtimali her zaman vardır
    // Bu nedenle görsel referansı tespit edilememiş olsa bile analiz yap
    hasImageReferences = true;
    if (detectedImageTypes.length === 0) {
      detectedImageTypes = ["şekil", "tablo", "diagram", "resim"];
    }
  }
  
  // Görsel içerik analizi yapılacaksa işleme devam et
  if (includeVisuals && hasImageReferences) {
    try {
      console.log("Görsel içerik analizi başlatılıyor. Doküman metni analiz ediliyor...");
      
      // Önce metin tabanlı görsel analizi yap
      const visualAnalysis = await analyzeVisualContent(processedText, detectedImageTypes);
      
      // Görsel analiz sonuçlarını dokümanın başına ekleyelim
      let visualAnalysisText = `[GÖRSEL ANALİZ: Doküman içerisinde toplam ${visualAnalysis.imageCount} adet görsel öğe tespit edilmiştir. `;
      visualAnalysisText += `Görsel türleri: ${visualAnalysis.detectedVisualTypes.join(', ')}]\n\n`;
      
      // Çıkarılmış gerçek görseller varsa, onları da analiz et
      if (extractedImages.length > 0) {
        visualAnalysisText += `[ÇIKARILAN GÖRSEL ANALİZİ: Doküman içerisinden toplam ${extractedImages.length} adet görsel başarıyla çıkarılmıştır.]\n\n`;
        
        // En fazla 3 görseli analiz et (performans için)
        const maxImageToAnalyze = Math.min(3, extractedImages.length);
        
        // Gerçek görselleri AI ile analiz et
        visualAnalysisText += `[GERÇEK GÖRSEL İÇERİKLERİ:]\n`;
        
        for (let i = 0; i < maxImageToAnalyze; i++) {
          try {
            // Görseli o4 modeli ile analiz et
            const imageAnalysisResult = await analyzeImageContent(extractedImages[i]);
            visualAnalysisText += `[Görsel ${i+1}]: ${imageAnalysisResult}\n\n`;
          } catch (error) {
            console.error(`Görsel ${i+1} analiz hatası:`, error);
            visualAnalysisText += `[Görsel ${i+1}]: Analiz sırasında hata oluştu.\n\n`;
          }
        }
        
        // Eğer daha fazla görsel varsa, analiz edilmediklerini belirt
        if (extractedImages.length > maxImageToAnalyze) {
          visualAnalysisText += `[NOT: Toplamda ${extractedImages.length} görsel tespit edildi, ancak performans nedeniyle sadece ilk ${maxImageToAnalyze} tanesi analiz edildi.]\n\n`;
        }
      }
      
      // Metinden çıkarılan görsel açıklamalarını da ekleyelim
      if (visualAnalysis.visualContentDescriptions && visualAnalysis.visualContentDescriptions.length > 0) {
        visualAnalysisText += `[METİN TABANLI GÖRSEL TESPİTLERİ:]\n`;
        
        // Maksimum 5 görsel detayı gösterelim (daha fazlası gereksiz olabilir)
        const maxDescriptions = Math.min(5, visualAnalysis.visualContentDescriptions.length);
        for (let i = 0; i < maxDescriptions; i++) {
          const item = visualAnalysis.visualContentDescriptions[i];
          visualAnalysisText += `- ${item.type}: ${item.description}\n`;
        }
        
        // Gösterilen görsel sayısı toplam görsel sayısından azsa, kalan görsellerin sayısını belirtelim
        if (maxDescriptions < visualAnalysis.visualContentDescriptions.length) {
          const remaining = visualAnalysis.visualContentDescriptions.length - maxDescriptions;
          visualAnalysisText += `- ... ve ${remaining} görsel daha\n`;
        }
        
        visualAnalysisText += `\n`;
      }
      
      // Analiz sonuçlarını içeriğin BAŞINA ekle (orijinal içeriğin önüne)
      processedText = visualAnalysisText + processedText;
    } catch (error) {
      console.error("Görsel içerik AI analizi hatası:", error);
      // Hata durumunda basit bir not ekle ama işlemin devam etmesine izin ver
      processedText += "\n\n[NOT: Görsel içerik için detaylı AI analizi tamamlanamadı. Basit analiz sonuçları kullanılacaktır.]";
    }
  } else if (!includeVisuals) {
    // Görsel içerikle ilgili özel bilgi notlarını temizle
    processedText = processedText.replace(/\[GÖRSEL İÇERİK ALGILANDI:.*?\]/g, "");
    processedText = processedText.replace(/\[BİLGİ: .*?(görseller|diagram|resim|tablo|şekil).*?\]/g, "");
    
    // Kullanıcının görsel analizi istemediğini belirten bir not ekle
    processedText += "\n\n[NOT: Görsel içerik analizi kullanıcı tarafından devre dışı bırakılmıştır.]";
  } else {
    // Görsel içerik analizi aktif - işleme ekstra zaman alabilir bilgisini ekle
    processedText += "\n\n[NOT: Görsel içerik analizi aktiftir. Dokümandaki görseller, diagramlar ve tablolar analiz edilecektir.]";
  }
  
  return processedText;
}

/**
 * Analyze document structure to identify headings, sections, etc.
 * This is a simple implementation that can be enhanced for more accurate structure detection
 */
export function analyzeDocumentStructure(text: string): { sections: { title: string; content: string }[] } {
  const lines = text.split('\n');
  const sections: { title: string; content: string }[] = [];
  let currentSection: { title: string; content: string } | null = null;
  
  // Simple heuristic to detect headings
  const isHeading = (line: string): boolean => {
    // Clean the line
    const trimmedLine = line.trim();
    
    // Skip empty lines
    if (!trimmedLine) return false;
    
    // Check for common heading patterns
    const isNumberedHeading = /^(\d+\.)+\s+\w+/.test(trimmedLine);
    const isShortLine = trimmedLine.length < 80 && trimmedLine.endsWith(':');
    const hasAllCapitalLetters = trimmedLine === trimmedLine.toUpperCase() && trimmedLine.length > 3;
    
    return isNumberedHeading || isShortLine || hasAllCapitalLetters;
  };
  
  // Iterate through lines to identify sections
  for (const line of lines) {
    if (isHeading(line)) {
      // If we found a heading and already have a current section, save it
      if (currentSection) {
        sections.push(currentSection);
      }
      
      // Start a new section
      currentSection = { title: line.trim(), content: '' };
    } else if (currentSection) {
      // Add content to current section
      currentSection.content += line + '\n';
    }
  }
  
  // Add the last section if any
  if (currentSection) {
    sections.push(currentSection);
  }
  
  return { sections };
}

export default {
  processPdf,
  processDocx,
  processText,
  processDocument,
  analyzeDocumentStructure
};
