import { apiRequest } from "@/lib/queryClient";
import { type Document, type User } from "@shared/schema";

// Promptları getiren fonksiyon
export async function fetchPrompts() {
  return apiRequest("GET", "/api/prompts");
}

// Prompt güncelleme fonksiyonu
export async function updatePrompt(key: string, value: string) {
  return apiRequest("POST", "/api/prompts/update", { key, value });
}

// AI modellerini getiren fonksiyon
export async function fetchAIModels() {
  return apiRequest("GET", "/api/ai-models");
}

// AI modelini değiştirme fonksiyonu
export async function setAIModel(modelType: string) {
  return apiRequest("POST", "/api/ai-models/set", { modelType });
}

// Dökümanı silen fonksiyon
export async function deleteDocument(documentId: number) {
  return apiRequest("DELETE", `/api/documents/${documentId}`);
}

// Dökümanı yeniden analiz eden fonksiyon
export async function reanalyzeDocument(documentId: number) {
  return apiRequest("POST", `/api/documents/${documentId}/reanalyze`);
}

// Test senaryosu doğrulama fonksiyonu
export async function validateTestScenario(scenarioId: number, feedback: any) {
  return apiRequest("POST", `/api/test-scenarios/${scenarioId}/validate`, feedback);
}

// Kapsam analizi getirme fonksiyonu
export async function getCoverageValidation(documentId: number) {
  return apiRequest("GET", `/api/documents/${documentId}/coverage-validation`);
}

// Doküman kapsama doğrulama fonksiyonu
export async function validateDocumentCoverage(documentId: number) {
  return apiRequest("POST", `/api/documents/${documentId}/validate-coverage`);
}

// Kapsama analizini silme fonksiyonu
export async function deleteCoverageValidation(documentId: number) {
  return apiRequest("DELETE", `/api/documents/${documentId}/coverage-validation`);
}

// AI asistanı mesaj gönderme fonksiyonu
export async function sendAIAssistantMessage(message: string, documents: Document[]) {
  return apiRequest("POST", "/api/ai-assistant", {
    message,
    documents: documents.map(doc => doc.id)
  });
}

// Test senaryosu güncelleme fonksiyonu
export async function updateTestScenario(scenarioId: number, updateData: any) {
  return apiRequest("PUT", `/api/test-scenarios/${scenarioId}`, updateData);
}

// Test senaryosu adımlarını AI ile iyileştirme fonksiyonu
export async function improveTestScenarioSteps(scenarioId: number) {
  return apiRequest("POST", `/api/test-scenarios/${scenarioId}/improve-steps`);
}

// Dokümanın tüm test senaryolarını silme fonksiyonu
export async function deleteAllTestScenarios(documentId: number) {
  return apiRequest("DELETE", `/api/documents/${documentId}/test-scenarios`);
}

// Kullanıcıları getiren fonksiyon
export async function fetchUsers() {
  return apiRequest("GET", "/api/users");
}

// Belirli bir kullanıcının bilgilerini getiren fonksiyon
export async function fetchUser(userId: number) {
  return apiRequest("GET", `/api/users/${userId}`);
}

// Yeni kullanıcı oluşturma fonksiyonu
export async function createUser(userData: {
  username: string;
  password: string;
  name?: string;
  email?: string;
  role?: string;
  department?: string;
  jobTitle?: string;
}) {
  return apiRequest("POST", "/api/users", userData);
}

// Kullanıcı şifresini sıfırlama fonksiyonu (admin için)
export async function resetUserPassword(userId: number, password: string) {
  return apiRequest("PUT", `/api/users/${userId}/reset-password`, { password });
}

// Kullanıcı durumunu değiştirme fonksiyonu (aktif/deaktif)
export async function updateUserStatus(userId: number, status: "active" | "inactive") {
  return apiRequest("PUT", `/api/users/${userId}/status`, { status });
}

// Kullanıcı izinlerini getiren fonksiyon
export async function fetchUserPermissions(userId: number) {
  return apiRequest("GET", `/api/users/${userId}/permissions`);
}

// Kullanıcıya izin verme/kaldırma fonksiyonu
export async function updateUserPermission(
  userId: number, 
  permissionId: number, 
  action: "grant" | "revoke"
) {
  return apiRequest("POST", `/api/users/${userId}/permissions`, { 
    permissionId, 
    action 
  });
}

// Tüm izinleri getiren fonksiyon
export async function fetchAllPermissions() {
  return apiRequest("GET", "/api/permissions/all");
}

// AI'a gereksinimler hakkında bilgi öğretme fonksiyonu
export async function teachAiAboutRequirement(data: {
  documentId: number;
  requirementCode: string;
  requirementDescription: string;
  missingInformation: string;
  solution: string;
}) {
  return apiRequest("POST", "/api/coverage/teach", data);
}

// AI öğrenme belleğinden bilgileri getirme fonksiyonu
export async function getCoverageLearnings(query?: string) {
  const url = query ? `/api/coverage/learnings?query=${encodeURIComponent(query)}` : '/api/coverage/learnings';
  return apiRequest("GET", url);
}