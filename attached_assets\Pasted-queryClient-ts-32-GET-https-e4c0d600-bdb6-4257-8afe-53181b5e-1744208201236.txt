queryClient.ts:32 
            
            
           GET https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/api/documents 401 (Unauthorized)
(anonymous) @ queryClient.ts:32
fetchFn @ @tanstack_react-query.js?v=e5d07da8:840
run @ @tanstack_react-query.js?v=e5d07da8:494
start @ @tanstack_react-query.js?v=e5d07da8:536
fetch @ @tanstack_react-query.js?v=e5d07da8:926
executeFetch_fn @ @tanstack_react-query.js?v=e5d07da8:2211
onSubscribe @ @tanstack_react-query.js?v=e5d07da8:1898
subscribe @ @tanstack_react-query.js?v=e5d07da8:24
(anonymous) @ @tanstack_react-query.js?v=e5d07da8:3022
subscribeToStore @ chunk-RPCDYKBN.js?v=e5d07da8:11984
commitHookEffectListMount @ chunk-RPCDYKBN.js?v=e5d07da8:16915
commitPassiveMountOnFiber @ chunk-RPCDYKBN.js?v=e5d07da8:18156
commitPassiveMountEffects_complete @ chunk-RPCDYKBN.js?v=e5d07da8:18129
commitPassiveMountEffects_begin @ chunk-RPCDYKBN.js?v=e5d07da8:18119
commitPassiveMountEffects @ chunk-RPCDYKBN.js?v=e5d07da8:18109
flushPassiveEffectsImpl @ chunk-RPCDYKBN.js?v=e5d07da8:19490
flushPassiveEffects @ chunk-RPCDYKBN.js?v=e5d07da8:19447
performSyncWorkOnRoot @ chunk-RPCDYKBN.js?v=e5d07da8:18868
flushSyncCallbacks @ chunk-RPCDYKBN.js?v=e5d07da8:9119
commitRootImpl @ chunk-RPCDYKBN.js?v=e5d07da8:19432
commitRoot @ chunk-RPCDYKBN.js?v=e5d07da8:19277
finishConcurrentRender @ chunk-RPCDYKBN.js?v=e5d07da8:18805
performConcurrentWorkOnRoot @ chunk-RPCDYKBN.js?v=e5d07da8:18718
workLoop @ chunk-RPCDYKBN.js?v=e5d07da8:197
flushWork @ chunk-RPCDYKBN.js?v=e5d07da8:176
performWorkUntilDeadline @ chunk-RPCDYKBN.js?v=e5d07da8:384Understand this errorAI
queryClient.ts:32 Fetch failed loading: GET "https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/api/documents".
(anonymous) @ queryClient.ts:32
fetchFn @ @tanstack_react-query.js?v=e5d07da8:840
run @ @tanstack_react-query.js?v=e5d07da8:494
start @ @tanstack_react-query.js?v=e5d07da8:536
fetch @ @tanstack_react-query.js?v=e5d07da8:926
executeFetch_fn @ @tanstack_react-query.js?v=e5d07da8:2211
onSubscribe @ @tanstack_react-query.js?v=e5d07da8:1898
subscribe @ @tanstack_react-query.js?v=e5d07da8:24
(anonymous) @ @tanstack_react-query.js?v=e5d07da8:3022
subscribeToStore @ chunk-RPCDYKBN.js?v=e5d07da8:11984
commitHookEffectListMount @ chunk-RPCDYKBN.js?v=e5d07da8:16915
commitPassiveMountOnFiber @ chunk-RPCDYKBN.js?v=e5d07da8:18156
commitPassiveMountEffects_complete @ chunk-RPCDYKBN.js?v=e5d07da8:18129
commitPassiveMountEffects_begin @ chunk-RPCDYKBN.js?v=e5d07da8:18119
commitPassiveMountEffects @ chunk-RPCDYKBN.js?v=e5d07da8:18109
flushPassiveEffectsImpl @ chunk-RPCDYKBN.js?v=e5d07da8:19490
flushPassiveEffects @ chunk-RPCDYKBN.js?v=e5d07da8:19447
performSyncWorkOnRoot @ chunk-RPCDYKBN.js?v=e5d07da8:18868
flushSyncCallbacks @ chunk-RPCDYKBN.js?v=e5d07da8:9119
commitRootImpl @ chunk-RPCDYKBN.js?v=e5d07da8:19432
commitRoot @ chunk-RPCDYKBN.js?v=e5d07da8:19277
finishConcurrentRender @ chunk-RPCDYKBN.js?v=e5d07da8:18805
performConcurrentWorkOnRoot @ chunk-RPCDYKBN.js?v=e5d07da8:18718
workLoop @ chunk-RPCDYKBN.js?v=e5d07da8:197
flushWork @ chunk-RPCDYKBN.js?v=e5d07da8:176
performWorkUntilDeadline @ chunk-RPCDYKBN.js?v=e5d07da8:384
queryClient.ts:15 
            
            
           GET https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/api/ai-models 401 (Unauthorized)
apiRequest @ queryClient.ts:15
fetchAIModels @ api.ts:16
loadAIModels @ sidebar.tsx:53
(anonymous) @ sidebar.tsx:82
commitHookEffectListMount @ chunk-RPCDYKBN.js?v=e5d07da8:16915
commitPassiveMountOnFiber @ chunk-RPCDYKBN.js?v=e5d07da8:18156
commitPassiveMountEffects_complete @ chunk-RPCDYKBN.js?v=e5d07da8:18129
commitPassiveMountEffects_begin @ chunk-RPCDYKBN.js?v=e5d07da8:18119
commitPassiveMountEffects @ chunk-RPCDYKBN.js?v=e5d07da8:18109
flushPassiveEffectsImpl @ chunk-RPCDYKBN.js?v=e5d07da8:19490
flushPassiveEffects @ chunk-RPCDYKBN.js?v=e5d07da8:19447
performSyncWorkOnRoot @ chunk-RPCDYKBN.js?v=e5d07da8:18868
flushSyncCallbacks @ chunk-RPCDYKBN.js?v=e5d07da8:9119
commitRootImpl @ chunk-RPCDYKBN.js?v=e5d07da8:19432
commitRoot @ chunk-RPCDYKBN.js?v=e5d07da8:19277
finishConcurrentRender @ chunk-RPCDYKBN.js?v=e5d07da8:18805
performConcurrentWorkOnRoot @ chunk-RPCDYKBN.js?v=e5d07da8:18718
workLoop @ chunk-RPCDYKBN.js?v=e5d07da8:197
flushWork @ chunk-RPCDYKBN.js?v=e5d07da8:176
performWorkUntilDeadline @ chunk-RPCDYKBN.js?v=e5d07da8:384Understand this errorAI
sidebar.tsx:77 AI modelleri yüklenemedi: Error: 401: {"error":"Bu işlem için oturum açmanız gerekiyor"}
    at throwIfResNotOk (queryClient.ts:6:11)
    at async apiRequest (queryClient.ts:22:3)
    at async loadAIModels (sidebar.tsx:53:26)
overrideMethod @ hook.js:608
loadAIModels @ sidebar.tsx:77
await in loadAIModels
(anonymous) @ sidebar.tsx:82
commitHookEffectListMount @ chunk-RPCDYKBN.js?v=e5d07da8:16915
commitPassiveMountOnFiber @ chunk-RPCDYKBN.js?v=e5d07da8:18156
commitPassiveMountEffects_complete @ chunk-RPCDYKBN.js?v=e5d07da8:18129
commitPassiveMountEffects_begin @ chunk-RPCDYKBN.js?v=e5d07da8:18119
commitPassiveMountEffects @ chunk-RPCDYKBN.js?v=e5d07da8:18109
flushPassiveEffectsImpl @ chunk-RPCDYKBN.js?v=e5d07da8:19490
flushPassiveEffects @ chunk-RPCDYKBN.js?v=e5d07da8:19447
performSyncWorkOnRoot @ chunk-RPCDYKBN.js?v=e5d07da8:18868
flushSyncCallbacks @ chunk-RPCDYKBN.js?v=e5d07da8:9119
commitRootImpl @ chunk-RPCDYKBN.js?v=e5d07da8:19432
commitRoot @ chunk-RPCDYKBN.js?v=e5d07da8:19277
finishConcurrentRender @ chunk-RPCDYKBN.js?v=e5d07da8:18805
performConcurrentWorkOnRoot @ chunk-RPCDYKBN.js?v=e5d07da8:18718
workLoop @ chunk-RPCDYKBN.js?v=e5d07da8:197
flushWork @ chunk-RPCDYKBN.js?v=e5d07da8:176
performWorkUntilDeadline @ chunk-RPCDYKBN.js?v=e5d07da8:384Understand this errorAI
queryClient.ts:15 Fetch failed loading: GET "https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/api/ai-models".