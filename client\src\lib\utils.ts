import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { format, formatDistance, formatRelative, subDays } from 'date-fns';
import { tr } from 'date-fns/locale';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getTimeAgo(date: Date | string) {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isToday(dateObj)) {
    return formatTime(dateObj);
  }
  
  try {
    return formatDistance(dateObj, new Date(), { 
      addSuffix: true,
      locale: tr 
    });
  } catch (e) {
    return format(dateObj, 'dd.MM.yyyy HH:mm');
  }
}

function isToday(date: Date) {
  const today = new Date();
  return date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear();
}

function formatTime(date: Date) {
  return format(date, 'HH:mm');
}
