import { format } from "date-fns";

interface StatusBarProps {
  componentsCount: number;
  requirementsCount: number;
  apiEndpointsCount: number;
  lastUpdated: string | Date | null;
}

const StatusBar = ({ 
  componentsCount, 
  requirementsCount, 
  apiEndpointsCount,
  lastUpdated
}: StatusBarProps) => {
  
  const getTimeAgo = (date: string | Date | null) => {
    if (!date) return 'Hiç';
    
    try {
      const now = new Date();
      const updatedDate = new Date(date);
      const diffMinutes = Math.floor((now.getTime() - updatedDate.getTime()) / (1000 * 60));
      
      if (diffMinutes < 60) {
        return `${diffMinutes} dakika önce`;
      } else if (diffMinutes < 24 * 60) {
        const hours = Math.floor(diffMinutes / 60);
        return `${hours} saat önce`;
      } else {
        const days = Math.floor(diffMinutes / (60 * 24));
        return `${days} gün önce`;
      }
    } catch (error) {
      return 'Bilinmiyor';
    }
  };

  return (
    <footer className="bg-neutral-100 border-t border-neutral-200 px-4 py-1.5 text-xs text-neutral-600 flex items-center justify-between">
      <div className="flex items-center">
        <span className="flex items-center">
          <i className="fas fa-check-circle text-success mr-1"></i>
          Analiz Tamamlandı
        </span>
        <span className="mx-2">|</span>
        <span>
          {requirementsCount} Gereksinim, {componentsCount} Bileşen, {apiEndpointsCount} API Entegrasyonu
        </span>
      </div>
      <div className="flex items-center">
        <span>Hafıza Kullanımı: 24%</span>
        <span className="mx-2">|</span>
        <span>Son Güncelleme: {getTimeAgo(lastUpdated)}</span>
      </div>
    </footer>
  );
};

export default StatusBar;
