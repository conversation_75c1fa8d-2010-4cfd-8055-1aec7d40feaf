import type { Express, Request, Response, NextFunction } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import multer from "multer";
import { z } from "zod";
import documentProcessor from "./services/document-processor";
import openaiService, { AI_MODELS, setAIModel, getCurrentModel, initializeDefaultSettings } from "./services/openai";
// Auth işlemleri
import { setupAuth, isAuthenticated, isAdmin, hashPassword } from "./auth";
// Prompts'ları doğrudan import etmek yerine, dinamik olarak yönetmek için bir servis oluşturuyoruz
import * as sharedPrompts from "../shared/prompts";
// Drizzle import'ları
import { db } from "./db";
import { eq } from "drizzle-orm";
import { testScenarios } from "@shared/schema";

// Runtime'da değiştirilebilir prompts nesnesi
const prompts: Record<string, string> = {
  DOCUMENT_ANALYSIS_PROMPT: sharedPrompts.DOCUMENT_ANALYSIS_PROMPT,
  TEST_SCENARIOS_GENERATION_PROMPT: sharedPrompts.TEST_SCENARIOS_GENERATION_PROMPT,
  TEST_SCENARIO_VALIDATION_PROMPT: sharedPrompts.TEST_SCENARIO_VALIDATION_PROMPT,
  DOCUMENT_COVERAGE_VALIDATION_PROMPT: sharedPrompts.DOCUMENT_COVERAGE_VALIDATION_PROMPT,
  AI_ASSISTANT_PROMPT: sharedPrompts.AI_ASSISTANT_PROMPT
};
import fs from "fs";
import path from "path";
import {
  insertDocumentSchema,
  insertComponentSchema,
  insertRequirementSchema,
  insertApiEndpointSchema,
  insertTestScenarioSchema,
  insertAiAnalysisSchema,
  insertApiConnectionSchema,
  Document,
  ApiConnection
} from "@shared/schema";

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
});

export async function registerRoutes(app: Express): Promise<Server> {
  // Kimlik doğrulama sistemini kur
  setupAuth(app);

  // Varsayılan OpenAI ayarlarını yükle
  await initializeDefaultSettings();

  // Koruma middleware'i - Tüm belge işlemleri için kimlik doğrulama gerektir
  const protectRoute = (req: Request, res: Response, next: NextFunction) => {
    if (req.isAuthenticated()) {
      return next();
    }
    return res.status(401).json({ error: "Bu işlem için oturum açmanız gerekiyor" });
  };

  // Admin koruma middleware'i - Sadece admin rolündeki kullanıcılar için izin verir
  const adminRoute = (req: Request, res: Response, next: NextFunction) => {
    if (req.isAuthenticated() && req.user?.role === 'admin') {
      return next();
    }
    return res.status(403).json({ error: "Bu işlem için yönetici yetkisi gerekiyor" });
  };

  // Kullanıcı bilgilerini güncelleme
  app.put("/api/users/:id", protectRoute, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.id);

      // Kendi profilini mi yoksa başka bir kullanıcıyı mı güncelliyor?
      if (userId !== req.user?.id && req.user?.role !== 'admin') {
        return res.status(403).json({ error: "Başka kullanıcıları düzenleme yetkiniz yok" });
      }

      // Sadece izin verilen alanları güncelle
      const allowedFields = ['name', 'email', 'department', 'jobTitle'];
      const updateData: Record<string, any> = {};

      for (const field of allowedFields) {
        if (req.body[field] !== undefined) {
          updateData[field] = req.body[field];
        }
      }

      // Sadece admin kullanıcı rolü ve durumu değiştirebilir
      if (req.user?.role === 'admin') {
        if (req.body.role !== undefined) {
          updateData.role = req.body.role;
        }

        if (req.body.status !== undefined) {
          // Admin kendisini deaktif edemez
          if (userId === req.user.id && req.body.status === 'inactive') {
            return res.status(400).json({ error: "Kendi hesabınızı deaktif edemezsiniz." });
          }
          updateData.status = req.body.status;
        }
      }

      // Eğer giriş yapıyorsa, son giriş zamanını güncelle
      if (req.body.updateLastLogin) {
        updateData.lastLogin = new Date();
      }

      // Kullanıcı bilgilerini güncelle
      const updatedUser = await storage.updateUser(userId, updateData);

      if (!updatedUser) {
        return res.status(404).json({ error: "Kullanıcı bulunamadı" });
      }

      // Giriş yapmış kullanıcının kendi bilgileri güncellendiyse oturum bilgilerini de güncelle
      if (userId === req.user?.id) {
        req.login(updatedUser, (err) => {
          if (err) {
            console.error("Oturum güncelleme hatası:", err);
          }
        });
      }

      res.json(updatedUser);
    } catch (error) {
      console.error("Kullanıcı güncelleme hatası:", error);
      res.status(500).json({
        error: `Kullanıcı güncellenemedi: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  });

  // Tüm kullanıcıları listele (sadece admin için)
  app.get("/api/users", protectRoute, isAdmin, async (req: Request, res: Response) => {
    try {
      const users = await storage.getAllUsers();
      // Şifreleri gizleyen güvenli bir kopya oluştur
      const safeUsers = users.map(user => {
        const { password, ...safeUser } = user;
        return safeUser;
      });
      res.json(safeUsers);
    } catch (error) {
      console.error("Kullanıcı listeleme hatası:", error);
      res.status(500).json({
        error: `Kullanıcılar getirilemedi: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  });

  // Yeni kullanıcı ekle (sadece admin için)
  app.post("/api/users", protectRoute, isAdmin, async (req: Request, res: Response) => {
    try {
      const { username, password, name, email, role, department, jobTitle } = req.body;

      // Zorunlu alanları kontrol et
      if (!username || !password) {
        return res.status(400).json({ error: "Kullanıcı adı ve şifre zorunludur" });
      }

      // Kullanıcı adının mevcut olup olmadığını kontrol et
      const existingUser = await storage.getUserByUsername(username);
      if (existingUser) {
        return res.status(400).json({ error: "Bu kullanıcı adı zaten kullanılıyor" });
      }

      // Şifreyi hashle
      const hashedPassword = await hashPassword(password);

      // Yeni kullanıcıyı oluştur
      const newUser = await storage.createUser({
        username,
        password: hashedPassword,
        role: role || 'user',
        name: name || null,
        email: email || null,
        department: department || null,
        jobTitle: jobTitle || null
      });

      // Şifreyi gizleyen güvenli bir kopya döndür
      const { password: _, ...safeUser } = newUser;

      res.status(201).json(safeUser);
    } catch (error) {
      console.error("Kullanıcı oluşturma hatası:", error);
      res.status(500).json({
        error: `Kullanıcı oluşturulamadı: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  });

  // Belirli bir kullanıcının bilgilerini getir (sadece admin için)
  app.get("/api/users/:userId", protectRoute, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.userId);

      // İstenen kullanıcı oturum açan kullanıcı değilse, admin yetkisi kontrolü yap
      if (userId !== req.user!.id && req.user!.role !== 'admin') {
        return res.status(403).json({ error: "Yetkiniz yok" });
      }

      const user = await storage.getUser(userId);

      if (!user) {
        return res.status(404).json({ error: "Kullanıcı bulunamadı" });
      }

      // Şifreyi gizleyen güvenli bir kopya döndür
      const { password, ...safeUser } = user;

      res.json(safeUser);
    } catch (error) {
      console.error("Kullanıcı getirme hatası:", error);
      res.status(500).json({
        error: `Kullanıcı getirilemedi: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  });

  // Kullanıcı şifresini sıfırla (admin tarafından)
  app.put("/api/users/:userId/reset-password", protectRoute, isAdmin, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.userId);
      const { password } = req.body;

      if (!password || typeof password !== 'string' || password.length < 6) {
        return res.status(400).json({ error: "Geçersiz şifre. En az 6 karakter olmalıdır." });
      }

      // Kullanıcıyı kontrol et
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ error: "Kullanıcı bulunamadı" });
      }

      // Şifreyi hashle
      const hashedPassword = await hashPassword(password);

      // Kullanıcı şifresini güncelle
      const updatedUser = await storage.updateUser(userId, { password: hashedPassword });

      if (!updatedUser) {
        return res.status(404).json({ error: "Kullanıcı bulunamadı" });
      }

      res.json({ success: true, message: "Kullanıcı şifresi başarıyla sıfırlandı" });
    } catch (error) {
      console.error("Şifre sıfırlama hatası:", error);
      res.status(500).json({
        error: `Şifre sıfırlanırken bir hata oluştu: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  });

  // Kullanıcı durumunu değiştir (aktif/deaktif)
  app.put("/api/users/:userId/status", protectRoute, isAdmin, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.userId);
      const { status } = req.body;

      if (status !== "active" && status !== "inactive") {
        return res.status(400).json({ error: "Geçersiz durum. 'active' veya 'inactive' olmalıdır." });
      }

      // Kullanıcıyı kontrol et
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ error: "Kullanıcı bulunamadı" });
      }

      // Admin kendisini deaktif edemez
      if (userId === req.user!.id && status === "inactive") {
        return res.status(400).json({ error: "Kendi hesabınızı deaktif edemezsiniz." });
      }

      const updatedUser = await storage.updateUser(userId, { status });

      if (!updatedUser) {
        return res.status(404).json({ error: "Kullanıcı bulunamadı" });
      }

      const { password, ...safeUser } = updatedUser;
      res.json({ success: true, user: safeUser });
    } catch (error) {
      console.error("Kullanıcı durumu değiştirme hatası:", error);
      res.status(500).json({
        error: `Kullanıcı durumu değiştirilirken bir hata oluştu: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  });

  // Kullanıcı izinlerini getir veya değiştir (sadece admin için)
  app.post("/api/users/:userId/permissions", protectRoute, isAdmin, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.userId);
      const { permissionId, action } = req.body;

      if (!permissionId || !action) {
        return res.status(400).json({ error: "İzin ID ve işlem tipi (grant/revoke) gereklidir" });
      }

      if (action === 'grant') {
        await storage.grantPermission(userId, permissionId);
      } else if (action === 'revoke') {
        await storage.revokePermission(userId, permissionId);
      } else {
        return res.status(400).json({ error: "Geçersiz işlem. 'grant' veya 'revoke' olmalıdır" });
      }

      // Güncellenmiş izinleri döndür
      const userPermissions = await storage.getUserPermissions(userId);
      res.json(userPermissions);
    } catch (error) {
      console.error("İzin değiştirme hatası:", error);
      res.status(500).json({
        error: `İzin değiştirilemedi: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  });

  // Sistemdeki tüm izinleri getir (sadece admin için)
  app.get("/api/permissions/all", protectRoute, isAdmin, async (req: Request, res: Response) => {
    try {
      // Veritabanından tüm izinleri al
      const dbPermissions = await storage.getAllPermissions();

      // Veritabanından izinler gelmezse varsayılan izinleri kullan
      if (!dbPermissions || dbPermissions.length === 0) {
        // Varsayılan izinler
        const permissions = [
          { id: 1, name: 'user_management', description: 'Kullanıcı Yönetimi' },
          { id: 2, name: 'project_management', description: 'Proje Yönetimi' },
          { id: 3, name: 'prompt_edit', description: 'Prompt Düzenleme' },
          { id: 4, name: 'api_settings', description: 'API Ayarları' },
          { id: 5, name: 'maintenance', description: 'Bakım Modu' },
          { id: 6, name: 'backup', description: 'Yedekleme' },
          { id: 7, name: 'statistics', description: 'İstatistikler' }
        ];

        return res.json(permissions);
      }

      // Veritabanından gelen izinleri döndür
      res.json(dbPermissions);
    } catch (error) {
      console.error("İzinleri getirme hatası:", error);
      res.status(500).json({ error: "İzinler getirilirken bir hata oluştu" });
    }
  });

  // Belirli bir kullanıcının izinlerini getir (admin için)
  app.get("/api/users/:userId/permissions", protectRoute, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.userId);

      // İstenen kullanıcı oturum açan kullanıcı değilse, admin yetkisi kontrolü yap
      if (userId !== req.user!.id && req.user!.role !== 'admin') {
        return res.status(403).json({ error: "Yetkiniz yok" });
      }

      const userPermissions = await storage.getUserPermissions(userId);
      res.json(userPermissions);
    } catch (error) {
      console.error("Kullanıcı izinleri getirme hatası:", error);
      res.status(500).json({ error: "Kullanıcı izinleri getirilirken bir hata oluştu" });
    }
  });

  // Kullanıcının izinlerini getir
  app.get("/api/permissions", protectRoute, async (req: Request, res: Response) => {
    try {
      // Admin kullanıcılarına özel işlem - tüm izinleri ver
      if (req.user!.role === 'admin') {
        // Tüm izinleri getir
        const allPermissions = await storage.getAllPermissions();
        return res.json(allPermissions.map(p => p.name));
      }

      // Normal kullanıcılar için veritabanı izinlerini getir
      const userId = req.user!.id;
      const userPermissions = await storage.getUserPermissions(userId);
      res.json(userPermissions.map(p => p.name));
    } catch (error) {
      console.error("İzinleri getirme hatası:", error);
      res.status(500).json({ error: "İzinler getirilirken bir hata oluştu" });
    }
  });

  // Projeleri getir
  app.get("/api/projects", protectRoute, async (req: Request, res: Response) => {
    try {
      // Admin tüm projeleri görebilir
      if (req.user?.role === 'admin') {
        const projects = await storage.getProjects();
        return res.json(projects);
      }

      // Normal kullanıcılar sadece erişimleri olan projeleri görebilir
      const userProjects = await storage.getProjectsByUserId(req.user!.id);
      res.json(userProjects);
    } catch (error) {
      console.error("Projeleri getirme hatası:", error);
      res.status(500).json({ error: "Projeler getirilirken bir hata oluştu" });
    }
  });

  // Proje detaylarını getir
  app.get("/api/projects/:id", protectRoute, async (req: Request, res: Response) => {
    try {
      const projectId = parseInt(req.params.id);
      if (isNaN(projectId)) {
        return res.status(400).json({ error: "Geçersiz proje ID'si" });
      }

      const project = await storage.getProject(projectId);
      if (!project) {
        return res.status(404).json({ error: "Proje bulunamadı" });
      }

      // Kullanıcının projeye erişim yetkisi var mı kontrol et
      const hasAccess = await storage.hasProjectAccess(req.user!.id, projectId);

      if (!hasAccess && req.user?.role !== 'admin') {
        return res.status(403).json({ error: "Bu projeye erişim yetkiniz yok" });
      }

      // Proje sahibi bilgilerini getir
      const creator = await storage.getUser(project.createdBy);

      res.json({
        ...project,
        creator: creator ? {
          id: creator.id,
          username: creator.username,
          name: creator.name
        } : undefined
      });
    } catch (error) {
      console.error("Proje detayları getirme hatası:", error);
      res.status(500).json({ error: "Proje detayları getirilirken bir hata oluştu" });
    }
  });

  // Yeni proje oluştur
  app.post("/api/projects", protectRoute, async (req: Request, res: Response) => {
    try {
      // Proje oluşturma yetkisi var mı kontrol et
      const hasPermission = await storage.hasPermission(req.user!.id, 'PROJECT_CREATE');

      if (!hasPermission && req.user?.role !== 'admin') {
        return res.status(403).json({ error: "Proje oluşturma yetkiniz yok" });
      }

      const project = await storage.createProject({
        ...req.body,
        createdBy: req.user!.id
      });

      // Projeyi oluşturan kullanıcıyı projeye sahip olarak ekle
      await storage.addUserToProject({
        userId: req.user!.id,
        projectId: project.id,
        role: 'owner'
      });

      res.status(201).json(project);
    } catch (error) {
      console.error("Proje oluşturma hatası:", error);
      res.status(500).json({ error: "Proje oluşturulurken bir hata oluştu" });
    }
  });

  // Proje güncelle
  app.put("/api/projects/:id", protectRoute, async (req: Request, res: Response) => {
    try {
      const projectId = parseInt(req.params.id);
      if (isNaN(projectId)) {
        return res.status(400).json({ error: "Geçersiz proje ID'si" });
      }

      // Kullanıcının projeye sahip rolü var mı kontrol et
      const userProject = await storage.getUserProject(req.user!.id, projectId);

      if (!userProject && req.user?.role !== 'admin') {
        return res.status(403).json({ error: "Bu projeyi güncelleme yetkiniz yok" });
      }

      if (userProject?.role !== 'owner' && req.user?.role !== 'admin') {
        return res.status(403).json({ error: "Bu projeyi güncelleme yetkiniz yok" });
      }

      const project = await storage.updateProject(projectId, {
        ...req.body,
        updatedAt: new Date()
      });

      if (!project) {
        return res.status(404).json({ error: "Proje bulunamadı" });
      }

      res.json(project);
    } catch (error) {
      console.error("Proje güncelleme hatası:", error);
      res.status(500).json({ error: "Proje güncellenirken bir hata oluştu" });
    }
  });

  // Proje sil
  app.delete("/api/projects/:id", protectRoute, async (req: Request, res: Response) => {
    try {
      const projectId = parseInt(req.params.id);
      if (isNaN(projectId)) {
        return res.status(400).json({ error: "Geçersiz proje ID'si" });
      }

      // Projeyi kontrol et
      const project = await storage.getProject(projectId);
      if (!project) {
        return res.status(404).json({ error: "Proje bulunamadı" });
      }

      // Admin veya proje sahibi mi kontrol et
      if (project.createdBy !== req.user!.id && req.user?.role !== 'admin') {
        const userProject = await storage.getUserProject(req.user!.id, projectId);

        if (!userProject || userProject.role !== 'owner') {
          return res.status(403).json({ error: "Bu projeyi silme yetkiniz yok" });
        }
      }

      // Proje silme yetkisi var mı kontrol et
      const hasPermission = await storage.hasPermission(req.user!.id, 'PROJECT_DELETE');

      if (!hasPermission && req.user?.role !== 'admin') {
        return res.status(403).json({ error: "Proje silme yetkiniz yok" });
      }

      const result = await storage.deleteProject(projectId);

      if (!result) {
        return res.status(500).json({ error: "Proje silinemedi" });
      }

      res.json({ success: true, message: "Proje başarıyla silindi" });
    } catch (error) {
      console.error("Proje silme hatası:", error);
      res.status(500).json({ error: "Proje silinirken bir hata oluştu" });
    }
  });

  // Projeye ait dokümanları getir
  app.get("/api/projects/:id/documents", protectRoute, async (req: Request, res: Response) => {
    try {
      const projectId = parseInt(req.params.id);
      if (isNaN(projectId)) {
        return res.status(400).json({ error: "Geçersiz proje ID'si" });
      }

      // Kullanıcının projeye erişim yetkisi var mı kontrol et
      const hasAccess = await storage.hasProjectAccess(req.user!.id, projectId);

      if (!hasAccess && req.user?.role !== 'admin') {
        return res.status(403).json({ error: "Bu projeye erişim yetkiniz yok" });
      }

      const documents = await storage.getDocumentsByProjectId(projectId);
      res.json(documents);
    } catch (error) {
      console.error("Proje dokümanları getirme hatası:", error);
      res.status(500).json({ error: "Proje dokümanları getirilirken bir hata oluştu" });
    }
  });

  // Projeye ait kullanıcıları getir
  app.get("/api/projects/:id/users", protectRoute, async (req: Request, res: Response) => {
    try {
      const projectId = parseInt(req.params.id);
      if (isNaN(projectId)) {
        return res.status(400).json({ error: "Geçersiz proje ID'si" });
      }

      // Kullanıcının projeye erişim yetkisi var mı kontrol et
      const hasAccess = await storage.hasProjectAccess(req.user!.id, projectId);

      if (!hasAccess && req.user?.role !== 'admin') {
        return res.status(403).json({ error: "Bu projeye erişim yetkiniz yok" });
      }

      const projectUsers = await storage.getProjectUsers(projectId);

      // Her kullanıcı için detayları getir
      const detailedUsers = await Promise.all(
        projectUsers.map(async (pu) => {
          const user = await storage.getUser(pu.userId);
          return {
            ...pu,
            user: user ? {
              id: user.id,
              username: user.username,
              name: user.name,
              email: user.email
            } : undefined
          };
        })
      );

      res.json(detailedUsers);
    } catch (error) {
      console.error("Proje kullanıcıları getirme hatası:", error);
      res.status(500).json({ error: "Proje kullanıcıları getirilirken bir hata oluştu" });
    }
  });

  // Projeye kullanıcı ekle
  app.post("/api/projects/:id/users", protectRoute, async (req: Request, res: Response) => {
    try {
      const projectId = parseInt(req.params.id);
      if (isNaN(projectId)) {
        return res.status(400).json({ error: "Geçersiz proje ID'si" });
      }

      // Kullanıcının projeye sahip rolü var mı kontrol et
      const userProject = await storage.getUserProject(req.user!.id, projectId);

      if ((!userProject || userProject.role !== 'owner') && req.user?.role !== 'admin') {
        return res.status(403).json({ error: "Bu projeye kullanıcı ekleme yetkiniz yok" });
      }

      // Eklenecek kullanıcıyı bul
      const userToAdd = await storage.getUserByUsername(req.body.username);
      if (!userToAdd) {
        return res.status(404).json({ error: "Kullanıcı bulunamadı" });
      }

      // Kullanıcı zaten projede mi kontrol et
      const existingUserProject = await storage.getUserProject(userToAdd.id, projectId);
      if (existingUserProject) {
        return res.status(400).json({ error: "Kullanıcı zaten bu projede" });
      }

      // Kullanıcıyı projeye ekle
      const newUserProject = await storage.addUserToProject({
        userId: userToAdd.id,
        projectId,
        role: req.body.role || 'viewer'
      });

      res.status(201).json({
        ...newUserProject,
        user: {
          id: userToAdd.id,
          username: userToAdd.username,
          name: userToAdd.name
        }
      });
    } catch (error) {
      console.error("Projeye kullanıcı ekleme hatası:", error);
      res.status(500).json({ error: "Projeye kullanıcı eklenirken bir hata oluştu" });
    }
  });

  // Projede kullanıcı rolünü güncelle
  app.put("/api/projects/:projectId/users/:userId", protectRoute, async (req: Request, res: Response) => {
    try {
      const projectId = parseInt(req.params.projectId);
      const userId = parseInt(req.params.userId);

      if (isNaN(projectId) || isNaN(userId)) {
        return res.status(400).json({ error: "Geçersiz proje veya kullanıcı ID'si" });
      }

      // Kullanıcının projeye sahip rolü var mı kontrol et
      const userProject = await storage.getUserProject(req.user!.id, projectId);

      if ((!userProject || userProject.role !== 'owner') && req.user?.role !== 'admin') {
        return res.status(403).json({ error: "Bu projede kullanıcı rolü güncelleme yetkiniz yok" });
      }

      // Güncellenecek kullanıcı projede mi kontrol et
      const existingUserProject = await storage.getUserProject(userId, projectId);
      if (!existingUserProject) {
        return res.status(404).json({ error: "Kullanıcı bu projede bulunamadı" });
      }

      // Kullanıcı rolünü güncelle
      const updatedUserProject = await storage.updateUserProjectRole(userId, projectId, req.body.role);

      if (!updatedUserProject) {
        return res.status(500).json({ error: "Kullanıcı rolü güncellenemedi" });
      }

      // Kullanıcı bilgilerini getir
      const user = await storage.getUser(userId);

      res.json({
        ...updatedUserProject,
        user: user ? {
          id: user.id,
          username: user.username,
          name: user.name
        } : undefined
      });
    } catch (error) {
      console.error("Kullanıcı rolü güncelleme hatası:", error);
      res.status(500).json({ error: "Kullanıcı rolü güncellenirken bir hata oluştu" });
    }
  });

  // Projeden kullanıcı çıkar
  app.delete("/api/projects/:projectId/users/:userId", protectRoute, async (req: Request, res: Response) => {
    try {
      const projectId = parseInt(req.params.projectId);
      const userId = parseInt(req.params.userId);

      if (isNaN(projectId) || isNaN(userId)) {
        return res.status(400).json({ error: "Geçersiz proje veya kullanıcı ID'si" });
      }

      // Kullanıcının projeye sahip rolü var mı kontrol et
      const userProject = await storage.getUserProject(req.user!.id, projectId);

      if ((!userProject || userProject.role !== 'owner') && req.user?.role !== 'admin') {
        return res.status(403).json({ error: "Bu projeden kullanıcı çıkarma yetkiniz yok" });
      }

      // Çıkarılacak kullanıcı projede mi kontrol et
      const existingUserProject = await storage.getUserProject(userId, projectId);
      if (!existingUserProject) {
        return res.status(404).json({ error: "Kullanıcı bu projede bulunamadı" });
      }

      // Proje sahibi çıkarılamaz
      const project = await storage.getProject(projectId);
      if (project && project.createdBy === userId) {
        return res.status(403).json({ error: "Proje sahibi projeden çıkarılamaz" });
      }

      // Kullanıcıyı projeden çıkar
      const result = await storage.removeUserFromProject(userId, projectId);

      if (!result) {
        return res.status(500).json({ error: "Kullanıcı projeden çıkarılamadı" });
      }

      res.json({ success: true, message: "Kullanıcı projeden çıkarıldı" });
    } catch (error) {
      console.error("Kullanıcı projeden çıkarma hatası:", error);
      res.status(500).json({ error: "Kullanıcı projeden çıkarılırken bir hata oluştu" });
    }
  });

  // Kullanıcının projelerini getir
  app.get("/api/user-projects", protectRoute, async (req: Request, res: Response) => {
    try {
      const userProjects = await storage.getUserProjects(req.user!.id);
      res.json(userProjects);
    } catch (error) {
      console.error("Kullanıcı projeleri getirme hatası:", error);
      res.status(500).json({ error: "Kullanıcı projeleri getirilirken bir hata oluştu" });
    }
  });
  // Document endpoints
  app.get("/api/documents", protectRoute, async (req: Request, res: Response) => {
    try {
      // Kullanıcı admin ise tüm dokümanları getir, değilse sadece kullanıcının dokümanlarını getir
      let documents;
      if (req.user?.role === 'admin') {
        documents = await storage.getDocuments();
      } else {
        documents = await storage.getDocumentsByUserId(req.user!.id);
      }
      res.json(documents);
    } catch (error) {
      console.error("Dokümanlar getirilirken hata:", error);
      res.status(500).json({ error: "Dokümanlar getirilirken bir hata oluştu" });
    }
  });

  app.get("/api/documents/:id", protectRoute, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ error: "Geçersiz doküman ID'si" });
      }

      const document = await storage.getDocument(id);
      if (!document) {
        return res.status(404).json({ error: "Doküman bulunamadı" });
      }

      // Admin olmayan kullanıcılar sadece kendi dokümanlarını görüntüleyebilir
      if (req.user?.role !== 'admin' && document.createdBy !== req.user?.id) {
        return res.status(403).json({ error: "Bu dokümanı görüntüleme yetkiniz bulunmamaktadır" });
      }

      res.json(document);
    } catch (error) {
      res.status(500).json({ error: "Doküman getirilirken bir hata oluştu" });
    }
  });

  app.post(
    "/api/documents/upload",
    protectRoute,
    upload.single("file"),
    async (req: Request, res: Response) => {
      try {
        if (!req.file) {
          return res.status(400).json({ error: "Dosya yüklenmedi" });
        }

        const file = req.file;
        const fileType = file.originalname.split(".").pop()?.toLowerCase() || "";

        // sourceType kontrolü - doğrudan metin girişi için özel işleme
        const sourceType = req.body.sourceType || "";

        // Eğer metin girişi ise veya desteklenen bir dosya türü ise işleme devam et
        if (sourceType !== "text" && !["pdf", "docx", "doc", "txt"].includes(fileType)) {
          return res
            .status(400)
            .json({ error: "Desteklenmeyen dosya türü. Sadece PDF, Word dokümanları ve metin dosyaları desteklenir." });
        }

        // Projeden yüklenen dokümanları işaretlemek için projectId kontrolü
        const projectId = req.body.projectId ? parseInt(req.body.projectId as string, 10) : null;
        console.log(`Doküman yükleniyor. Proje ID: ${projectId || 'Proje seçilmedi'}`);

        // Görsel içerik analizi seçeneği
        const shouldIncludeVisuals = req.body.includeVisuals !== "false";

        // Process the document
        const extractedText = await documentProcessor.processDocument(
          file.buffer,
          fileType,
          shouldIncludeVisuals
        );

        // Create document in storage with current user as owner
        const document = await storage.createDocument({
          name: file.originalname,
          type: fileType,
          content: extractedText,
          originalContent: file.buffer.toString("base64"),
          createdBy: req.user?.id, // Mevcut kullanıcıyı doküman sahibi olarak ayarla
          projectId: projectId, // Proje ID'sini ekle
        });

        // Analyze the document with OpenAI
        try {
          const analysisResult = await openaiService.analyzeDocument(extractedText);

          // Store components
          for (const component of analysisResult.components) {
            await storage.createComponent({
              documentId: document.id,
              name: component.name,
              description: component.description || null,
              type: component.type || null,
              isNew: component.isNew === true ? 1 : 0, // SQLite boolean değerleri için 1/0 kullan
            });
          }

          // Store requirements
          for (const requirement of analysisResult.requirements) {
            await storage.createRequirement({
              documentId: document.id,
              code: requirement.code,
              description: requirement.description,
              category: requirement.category || null,
            });
          }

          // Store API endpoints
          for (const endpoint of analysisResult.apiEndpoints) {
            await storage.createApiEndpoint({
              documentId: document.id,
              url: endpoint.url,
              method: endpoint.method,
              parameters: endpoint.parameters || {},
              requirementCode: endpoint.requirementCode || null,
            });
          }

          // Store AI analysis
          await storage.createAiAnalysis({
            documentId: document.id,
            observations: analysisResult.observations || [],
          });

          // Dokümanı güncelle - analizin tamamlandığını işaretle
          // Veritabanında analyzed_at sütunu yoksa bu sorunu atla
          try {
            await storage.updateDocument(document.id, {
              // Bu alanın veritabanında olup olmadığını kontrol etmek için doğrudan ham SQL kullanıyoruz
              content: document.content // Sadece içeriği güncelle, timestamp güncellemesini geç
            });
          } catch (updateError) {
            console.error("Doküman güncellenirken hata:", updateError);
            // Bu hatayı görmezden gel, hala doküman analiz sonuçlarını dönelim
          }

          res.status(201).json({ document, analysis: analysisResult });
        } catch (analysisError) {
          console.error("Doküman analiz hatası:", analysisError);
          // Still return the document even if analysis fails
          res.status(201).json({
            document,
            warning: "Doküman yüklendi ancak analiz başarısız oldu. Lütfen daha sonra tekrar analiz etmeyi deneyin."
          });
        }
      } catch (error) {
        console.error("Doküman yükleme hatası:", error);
        res.status(500).json({
          error: `Doküman yüklenirken hata oluştu: ${error instanceof Error ? error.message : String(error)}`
        });
      }
    }
  );

  // Components endpoints
  app.get("/api/documents/:documentId/components", protectRoute, async (req: Request, res: Response) => {
    try {
      const documentId = parseInt(req.params.documentId);
      if (isNaN(documentId)) {
        return res.status(400).json({ error: "Invalid document ID" });
      }

      const components = await storage.getComponentsByDocumentId(documentId);
      res.json(components);
    } catch (error) {
      res.status(500).json({ error: "Bileşenler getirilirken bir hata oluştu" });
    }
  });

  // Requirements endpoints
  app.get("/api/documents/:documentId/requirements", protectRoute, async (req: Request, res: Response) => {
    try {
      const documentId = parseInt(req.params.documentId);
      if (isNaN(documentId)) {
        return res.status(400).json({ error: "Invalid document ID" });
      }

      const requirements = await storage.getRequirementsByDocumentId(documentId);
      res.json(requirements);
    } catch (error) {
      res.status(500).json({ error: "Gereksinimler getirilirken bir hata oluştu" });
    }
  });

  // API endpoints
  app.get("/api/documents/:documentId/api-endpoints", protectRoute, async (req: Request, res: Response) => {
    try {
      const documentId = parseInt(req.params.documentId);
      if (isNaN(documentId)) {
        return res.status(400).json({ error: "Invalid document ID" });
      }

      const apiEndpoints = await storage.getApiEndpointsByDocumentId(documentId);
      res.json(apiEndpoints);
    } catch (error) {
      res.status(500).json({ error: "API uç noktaları getirilirken bir hata oluştu" });
    }
  });

  // Test scenarios endpoints
  app.get("/api/documents/:documentId/test-scenarios", protectRoute, async (req: Request, res: Response) => {
    try {
      const documentId = parseInt(req.params.documentId);
      if (isNaN(documentId)) {
        return res.status(400).json({ error: "Invalid document ID" });
      }

      const testScenarios = await storage.getTestScenariosByDocumentId(documentId);
      res.json(testScenarios);
    } catch (error) {
      res.status(500).json({ error: "Test senaryoları getirilirken bir hata oluştu" });
    }
  });

  app.post("/api/documents/:documentId/generate-test-scenarios", protectRoute, async (req: Request, res: Response) => {
    try {
      const documentId = parseInt(req.params.documentId);
      if (isNaN(documentId)) {
        return res.status(400).json({ error: "Invalid document ID" });
      }

      // Maksimum token sayısını request'ten al veya varsayılan değeri kullan
      const maxTokens = req.body.maxTokens ? parseInt(req.body.maxTokens) : 32000; // Token sınırını maksimuma çıkardık

      // Get document and requirements for context
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ error: "Doküman bulunamadı" });
      }

      // Get requirements for this document
      const requirements = await storage.getRequirementsByDocumentId(documentId);
      if (requirements.length === 0) {
        return res.status(400).json({ error: "Bu doküman için gereksinim bulunamadı" });
      }

      // Generate test scenarios using OpenAI with the specified max tokens
      // Dokümanın tam içeriğini ve gereksinimleri birlikte gönderiyoruz
      const testScenariosResult = await openaiService.generateTestScenarios(
        requirements.map(r => ({
          code: r.code,
          description: r.description,
          category: r.category || ""
        })),
        maxTokens,
        document.content // Doküman içeriğini tam olarak gönderiyoruz
      );

      console.log("OpenAI API'den alınan test senaryoları:", JSON.stringify(testScenariosResult, null, 2));

      // Save test scenarios to storage
      const savedScenarios = [];
      for (const scenario of testScenariosResult.scenarios) {
        // Format değerini kontrol et ve default olarak ayarla
        console.log(`Test senaryosu formatı: ${scenario.format || "default"}, Gereksinim: ${scenario.requirementCode}`);

        // Format artık her zaman "default" olmalı
        const savedScenario = await storage.createTestScenario({
          documentId,
          title: scenario.title,
          preconditions: scenario.preconditions || null,
          steps: scenario.steps || [],
          expectedResults: scenario.expectedResults || null,
          requirementCode: scenario.requirementCode || null,
          format: "default" // Her zaman default format
        });
        savedScenarios.push(savedScenario);
      }

      res.json({
        scenarios: savedScenarios,
        coverageRate: testScenariosResult.coverageRate,
        missingRequirements: testScenariosResult.missingRequirements
      });
    } catch (error) {
      console.error("Test senaryoları oluşturulurken hata:", error);
      res.status(500).json({
        error: `Test senaryoları oluşturulamadı: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  });

  // AI Analysis endpoints
  app.get("/api/documents/:documentId/ai-analysis", protectRoute, async (req: Request, res: Response) => {
    try {
      const documentId = parseInt(req.params.documentId);
      if (isNaN(documentId)) {
        return res.status(400).json({ error: "Invalid document ID" });
      }

      const aiAnalysis = await storage.getAiAnalysisByDocumentId(documentId);
      if (!aiAnalysis) {
        return res.status(404).json({ error: "Bu doküman için AI analizi bulunamadı" });
      }

      res.json(aiAnalysis);
    } catch (error) {
      res.status(500).json({ error: "AI analizi getirilirken bir hata oluştu" });
    }
  });

  // Clarify requirement endpoint - Kaldırıldı (Bu endpoint şu anda kullanılmıyor)

  // Validate test scenario endpoint
  app.post("/api/test-scenarios/:scenarioId/validate", protectRoute, async (req: Request, res: Response) => {
    try {
      const scenarioId = parseInt(req.params.scenarioId);
      if (isNaN(scenarioId)) {
        return res.status(400).json({ error: "Geçersiz senaryo ID'si" });
      }

      // Test senaryosunu bul
      const scenarios = await storage.getTestScenariosByDocumentId(null); // tüm test senaryolarını al
      const scenario = scenarios.find(s => s.id === scenarioId);

      if (!scenario) {
        return res.status(404).json({ error: "Test senaryosu bulunamadı" });
      }

      // Gereksinimi bul
      if (!scenario.requirementCode) {
        return res.status(400).json({ error: "Bu senaryonun ilişkilendirilmiş bir gereksinimi yok" });
      }

      const allRequirements = await storage.getRequirements();
      const requirement = allRequirements.find(r => r.code === scenario.requirementCode);

      if (!requirement) {
        return res.status(404).json({ error: "İlişkili gereksinim bulunamadı" });
      }

      // Senaryo doğrulamasını yap
      const validation = await openaiService.validateTestScenario(scenario, requirement);
      res.json(validation);
    } catch (error) {
      console.error("Test senaryosu doğrulama hatası:", error);
      res.status(500).json({
        error: `Test senaryosu doğrulanamadı: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  });

  // Test senaryosu güncelleme endpoint'i
  app.put("/api/test-scenarios/:scenarioId", protectRoute, async (req: Request, res: Response) => {
    try {
      const scenarioId = parseInt(req.params.scenarioId);
      if (isNaN(scenarioId)) {
        return res.status(400).json({ error: "Geçersiz senaryo ID'si" });
      }

      // Gelen verileri doğrula
      const updateData = req.body;

      // Senaryonun var olup olmadığını kontrol et
      const scenarios = await storage.getTestScenariosByDocumentId(null);
      const scenario = scenarios.find(s => s.id === scenarioId);

      if (!scenario) {
        return res.status(404).json({ error: "Test senaryosu bulunamadı" });
      }

      // Test senaryosunu güncelle
      const updatedScenario = await storage.updateTestScenario(scenarioId, updateData);

      if (!updatedScenario) {
        return res.status(404).json({ error: "Test senaryosu güncellenemedi" });
      }

      res.json(updatedScenario);
    } catch (error) {
      console.error("Test senaryosu güncelleme hatası:", error);
      res.status(500).json({
        error: `Test senaryosu güncellenemedi: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  });

  // Test senaryosu adımlarını AI ile iyileştirme endpoint'i
  app.post("/api/test-scenarios/:scenarioId/improve-steps", protectRoute, async (req: Request, res: Response) => {
    try {
      const scenarioId = parseInt(req.params.scenarioId);
      if (isNaN(scenarioId)) {
        return res.status(400).json({ error: "Geçersiz senaryo ID'si" });
      }

      // Test senaryosunu bul
      const scenarios = await storage.getTestScenariosByDocumentId(null);
      const scenario = scenarios.find(s => s.id === scenarioId);

      if (!scenario) {
        return res.status(404).json({ error: "Test senaryosu bulunamadı" });
      }

      // Gereksinime göre senaryoyu iyileştirecek ise gereksinimi bul
      let requirement = null;
      if (scenario.requirementCode) {
        const allRequirements = await storage.getRequirements();
        requirement = allRequirements.find(r => r.code === scenario.requirementCode);
      }

      // AI ile senaryoyu iyileştir
      const improvedSteps = await openaiService.improveTestScenarioSteps(scenario, requirement);

      res.json({ improvedSteps });
    } catch (error) {
      console.error("Test senaryosu adımlarını iyileştirme hatası:", error);
      res.status(500).json({
        error: `Test senaryosu adımları iyileştirilemedi: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  });

  // Get coverage validation for a document
  app.get("/api/documents/:documentId/coverage-validation", protectRoute, async (req: Request, res: Response) => {
    try {
      const documentId = parseInt(req.params.documentId);
      if (isNaN(documentId)) {
        return res.status(400).json({ error: "Geçersiz doküman ID'si" });
      }

      // Var olan kapsam analizini getir
      const existingValidation = await storage.getCoverageValidationByDocumentId(documentId);

      res.json(existingValidation || null);
    } catch (error) {
      console.error("Kapsam analizi getirme hatası:", error);
      res.status(500).json({
        error: `Kapsam analizi getirilemedi: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  });

  // Validate document coverage endpoint
  app.post("/api/documents/:documentId/validate-coverage", protectRoute, async (req: Request, res: Response) => {
    try {
      const documentId = parseInt(req.params.documentId);
      if (isNaN(documentId)) {
        return res.status(400).json({ error: "Geçersiz doküman ID'si" });
      }

      // Gereksinimleri ve senaryoları al
      const requirements = await storage.getRequirementsByDocumentId(documentId);
      if (requirements.length === 0) {
        return res.status(400).json({ error: "Bu doküman için gereksinim bulunamadı" });
      }

      const testScenarios = await storage.getTestScenariosByDocumentId(documentId);
      if (testScenarios.length === 0) {
        return res.status(400).json({ error: "Bu doküman için test senaryosu bulunamadı" });
      }

      // Önce var olan analizi sil (eğer varsa)
      await storage.deleteCoverageValidation(documentId);

      // Kapsama doğrulamasını yap
      const coverageResult = await openaiService.validateDocumentCoverage(
        requirements.map(r => ({
          code: r.code,
          description: r.description,
          category: r.category || ""
        })),
        testScenarios
      );

      // Veritabanına kaydet
      const coverageValidation = await storage.createCoverageValidation({
        documentId,
        coverageRate: coverageResult.coverageRate,
        missingRequirements: coverageResult.missingRequirements,
        analysisDetails: coverageResult.analysisDetails,
        recommendations: coverageResult.recommendations,
        weakPoints: coverageResult.weakPoints,
        strongPoints: coverageResult.strongPoints
      });

      res.json(coverageValidation);
    } catch (error) {
      console.error("Doküman kapsama doğrulama hatası:", error);
      res.status(500).json({
        error: `Doküman kapsama doğrulanamadı: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  });

  // Delete coverage validation endpoint
  app.delete("/api/documents/:documentId/coverage-validation", protectRoute, async (req: Request, res: Response) => {
    try {
      const documentId = parseInt(req.params.documentId);
      if (isNaN(documentId)) {
        return res.status(400).json({ error: "Geçersiz doküman ID'si" });
      }

      const result = await storage.deleteCoverageValidation(documentId);
      if (!result) {
        return res.status(404).json({ error: "Kapsam analizi bulunamadı" });
      }

      res.json({ success: true, message: "Kapsam analizi başarıyla silindi" });
    } catch (error) {
      console.error("Kapsam analizi silme hatası:", error);
      res.status(500).json({
        error: `Kapsam analizi silinemedi: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  });

  // Tüm test senaryolarını silme endpoint'i
  app.delete("/api/documents/:documentId/test-scenarios", protectRoute, async (req: Request, res: Response) => {
    try {
      const documentId = parseInt(req.params.documentId);
      if (isNaN(documentId)) {
        return res.status(400).json({ error: "Geçersiz doküman ID'si" });
      }

      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ error: "Doküman bulunamadı" });
      }

      // Dokümanın test senaryolarını al
      const existingScenarios = await storage.getTestScenariosByDocumentId(documentId);

      // Doküman ID'sine göre toplu silme işlemi - "testScenarios" schema'dan referans ile
      await db.delete(testScenarios).where(eq(testScenarios.documentId, documentId));

      res.json({
        success: true,
        message: `${existingScenarios.length} test senaryosu başarıyla silindi`,
        count: existingScenarios.length
      });
    } catch (error) {
      console.error("Test senaryoları silme hatası:", error);
      res.status(500).json({
        error: `Test senaryoları silinemedi: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  });

  // Delete document endpoint - Doküman silme izni gerektiren endpoint
  app.delete("/api/documents/:id", protectRoute, async (req: Request, res: Response) => {
    // İzin kontrolü
    if (!await storage.hasPermission(req.user.id, 'document_delete')) {
      return res.status(403).json({ error: "Bu işlem için yetkiniz bulunmamaktadır" });
    }
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ error: "Geçersiz doküman ID'si" });
      }

      const document = await storage.getDocument(id);
      if (!document) {
        return res.status(404).json({ error: "Doküman bulunamadı" });
      }

      const result = await storage.deleteDocument(id);
      if (!result) {
        return res.status(500).json({ error: "Doküman silinemedi" });
      }

      res.json({ success: true, message: "Doküman başarıyla silindi" });
    } catch (error) {
      console.error("Doküman silme hatası:", error);
      res.status(500).json({
        error: `Doküman silinemedi: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  });

  // Prompt tekstlerini alma/güncelleme endpointleri
  app.get("/api/prompts", protectRoute, (req: Request, res: Response) => {
    try {
      // Tüm prompt değişkenlerini döndür
      const promptList = Object.entries(prompts).map(([key, value]) => ({
        key,
        value: String(value)
      }));

      res.json(promptList);
    } catch (error) {
      console.error("Prompt'lar getirilirken hata:", error);
      res.status(500).json({
        error: `Prompt'lar getirilemedi: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  });

  app.post("/api/prompts/update", protectRoute, async (req: Request, res: Response) => {
    // Prompt yönetim izni kontrolü
    if (!await storage.hasPermission(req.user.id, 'prompt_management')) {
      return res.status(403).json({ error: "Bu işlem için prompt düzenleme yetkiniz bulunmamaktadır" });
    }
    try {
      const { key, value } = req.body;

      if (!key || !value) {
        return res.status(400).json({
          error: "Prompt anahtarı ve değeri gereklidir"
        });
      }

      if (!Object.keys(prompts).includes(key)) {
        return res.status(400).json({
          error: `Geçersiz prompt anahtarı: ${key}. Geçerli anahtarlar: ${Object.keys(prompts).join(', ')}`
        });
      }

      // Shared/prompts.ts dosyasının içeriğini oku
      const promptsFilePath = path.join(process.cwd(), 'shared', 'prompts.ts');
      let promptsContent = fs.readFileSync(promptsFilePath, 'utf8');

      // Prompt değişkeninin tanımını bul ve yeni değerle değiştir
      const promptVarName = key;

      // Düzenli ifade ile prompt değişkeninin değerini bul
      const promptRegex = new RegExp(`export const ${promptVarName} = \`([\\s\\S]*?)\`;`, 'g');
      const match = promptRegex.exec(promptsContent);

      if (!match) {
        return res.status(400).json({
          error: `Prompt değişkeni bulunamadı: ${key}`
        });
      }

      // Değerdeki backtick'leri escape et
      const escapedValue = value.replace(/`/g, '\\`');

      // Eski prompt değerini yeni değerle değiştir
      const updatedPromptContent = promptsContent.replace(
        promptRegex,
        `export const ${promptVarName} = \`${escapedValue}\`;`
      );

      // Dosyayı güncelle
      fs.writeFileSync(promptsFilePath, updatedPromptContent, 'utf8');

      // Değişkeni runtime'da da güncelle - artık doğrudan değiştirebiliriz çünkü prompts bir saf nesnedir
      prompts[key] = value;

      console.log(`Prompt "${key}" başarıyla güncellendi (dosya ve bellek)`);

      // OpenAI servisine de yeni promptları bildir
      try {
        openaiService.updatePrompt(key, value);
      } catch (e) {
        console.warn(`OpenAI servisi prompt güncellemesi yapılamadı: ${e}`);
      }

      // Başarılı yanıt döndür
      res.json({
        success: true,
        message: `Prompt "${key}" başarıyla güncellendi`
      });
    } catch (error) {
      console.error("Prompt güncellenirken hata:", error);
      res.status(500).json({
        error: `Prompt güncellenemedi: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  });

  // AI Model endpoints
  app.get("/api/ai-models", protectRoute, (req: Request, res: Response) => {
    try {
      const modelInfo = {
        models: AI_MODELS,
        current: getCurrentModel()
      };
      res.json(modelInfo);
    } catch (error) {
      res.status(500).json({ error: "AI modelleri getirilirken bir hata oluştu" });
    }
  });

  app.post("/api/ai-models/set", protectRoute, (req: Request, res: Response) => {
    try {
      const { modelType } = req.body;

      if (!modelType || !AI_MODELS[modelType as keyof typeof AI_MODELS]) {
        return res.status(400).json({
          success: false,
          message: "Geçersiz model tipi. Kullanılabilir modeller: " + Object.keys(AI_MODELS).join(", ")
        });
      }

      // API anahtarları kontrol et - o4 modeli için ayrı OpenAI API anahtarı gerekiyor
      let warningMessage = null;

      if (modelType === "o4" && !process.env.OPENAI_API_KEY) {
        warningMessage = "OpenAI API anahtarı eksik! GPT-4o modeli doğru çalışmayabilir. Hata durumunda o1 modeline otomatik geçiş yapılacak.";
        console.warn(warningMessage);
      } else if ((modelType === "o1" || modelType === "o3") && !process.env.AZURE_OPENAI_API_KEY) {
        warningMessage = "Azure OpenAI API anahtarı eksik! o1/o3 modelleri doğru çalışmayabilir.";
        console.warn(warningMessage);
      }

      const result = setAIModel(modelType as keyof typeof AI_MODELS);
      if (result) {
        const currentModel = getCurrentModel();
        const modelInfo = AI_MODELS[currentModel];

        res.json({
          success: true,
          message: `AI modeli başarıyla ${modelInfo.displayName} olarak değiştirildi`,
          warningMessage: warningMessage,
          model: {
            modelType: currentModel,
            deploymentName: modelInfo.deploymentName,
            maxTokens: modelInfo.maxTokens,
            displayName: modelInfo.displayName
          }
        });
      } else {
        res.status(500).json({
          success: false,
          message: "Model değiştirilemedi"
        });
      }
    } catch (error) {
      console.error("Model değiştirme hatası:", error);
      res.status(500).json({
        success: false,
        message: "Model değiştirme işlemi sırasında bir hata oluştu: " + (error instanceof Error ? error.message : String(error))
      });
    }
  });

  // Reanalyze document endpoint
  // AI Assistant endpoint
  app.post("/api/ai-assistant", protectRoute, async (req: Request, res: Response) => {
    try {
      const { message, context, format } = req.body;

      if (!message) {
        return res.status(400).json({ error: "Mesaj alanı gereklidir" });
      }

      console.log("AI Assistant isteği:", {
        message: message.substring(0, 100) + "...",
        format: format || 'none'
      });

      // Doğrudan kullanıcı isteğini OpenAI'a gönder
      const response = await openaiService.getAIAssistantResponse(message, context);

      // Yanıt metni çok uzun olabilir, bu yüzden büyük yanıtları düzgün döndürdüğümüzden emin olalım
      // Başarılı yanıtı döndür
      res.json({
        answer: response,
        success: true,
        format: format || 'unspecified'
      });
    } catch (error) {
      console.error("AI Assistant hatası:", error);
      res.status(500).json({
        error: `AI yanıtı alınamadı: ${error instanceof Error ? error.message : String(error)}`,
        errorDetails: error instanceof Error ? error.stack : null
      });
    }
  });

  app.post("/api/documents/:id/reanalyze", protectRoute, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ error: "Geçersiz doküman ID'si" });
      }

      const document = await storage.getDocument(id);
      if (!document) {
        return res.status(404).json({ error: "Doküman bulunamadı" });
      }

      // Doküman içeriğini kullanarak yeniden analiz yap
      const analysisResult = await openaiService.analyzeDocument(document.content);

      // Eski analiz sonuçlarını temizle (opsiyonel, veritabanı yapısına bağlı)
      // Mevcut yapımızda sadece yeni kayıtlar oluşturacağız

      // Store components
      for (const component of analysisResult.components) {
        await storage.createComponent({
          documentId: document.id,
          name: component.name,
          description: component.description || null,
          type: component.type || null,
          isNew: component.isNew === true ? 1 : 0,
        });
      }

      // Store requirements
      for (const requirement of analysisResult.requirements) {
        await storage.createRequirement({
          documentId: document.id,
          code: requirement.code,
          description: requirement.description,
          category: requirement.category || null,
        });
      }

      // Store API endpoints
      for (const endpoint of analysisResult.apiEndpoints) {
        await storage.createApiEndpoint({
          documentId: document.id,
          url: endpoint.url,
          method: endpoint.method,
          parameters: endpoint.parameters || {},
          requirementCode: endpoint.requirementCode || null,
        });
      }

      // Store AI analysis
      await storage.createAiAnalysis({
        documentId: document.id,
        observations: analysisResult.observations || [],
      });

      res.json({
        success: true,
        message: "Doküman başarıyla yeniden analiz edildi",
        analysis: analysisResult
      });
    } catch (error) {
      console.error("Doküman yeniden analiz hatası:", error);
      res.status(500).json({
        error: `Doküman yeniden analiz edilemedi: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  });

  // API Bağlantıları ile ilgili endpoints
  // Bir projenin tüm API bağlantılarını getir
  app.get("/api/projects/:projectId/connections", protectRoute, async (req: Request, res: Response) => {
    try {
      const projectId = parseInt(req.params.projectId);

      // Projeyi kontrol et
      const project = await storage.getProject(projectId);
      if (!project) {
        return res.status(404).json({ error: "Proje bulunamadı" });
      }

      // Kullanıcının projeye erişim yetkisi var mı kontrol et
      const hasAccess = await storage.hasProjectAccess(req.user!.id, projectId);
      if (!hasAccess && req.user?.role !== 'admin') {
        return res.status(403).json({ error: "Bu projeye erişim yetkiniz yok" });
      }

      // API bağlantılarını getir
      try {
        // @ts-ignore
        const connections = await storage.getApiConnectionsByProjectId(projectId);
        res.json(connections);
      } catch (error) {
        console.error("API bağlantılarını getirme hatası:", error);
        res.status(500).json({ error: "API bağlantıları getirilemedi" });
      }
    } catch (error) {
      console.error("API bağlantılarını getirme hatası:", error);
      res.status(500).json({ error: "API bağlantıları getirilirken bir hata oluştu" });
    }
  });

  // Bir bağlantının detaylarını getir
  app.get("/api/projects/:projectId/connections/:connectionId", protectRoute, async (req: Request, res: Response) => {
    try {
      const projectId = parseInt(req.params.projectId);
      const connectionId = parseInt(req.params.connectionId);

      // Kullanıcının projeye erişim yetkisi var mı kontrol et
      const hasAccess = await storage.hasProjectAccess(req.user!.id, projectId);
      if (!hasAccess && req.user?.role !== 'admin') {
        return res.status(403).json({ error: "Bu projeye erişim yetkiniz yok" });
      }

      // Bağlantıyı getir
      try {
        // @ts-ignore
        const connection = await storage.getApiConnection(connectionId);

        if (!connection || connection.projectId !== projectId) {
          return res.status(404).json({ error: "Bağlantı bulunamadı" });
        }

        res.json(connection);
      } catch (error) {
        console.error("API bağlantısını getirme hatası:", error);
        res.status(500).json({ error: "API bağlantısı getirilemedi" });
      }
    } catch (error) {
      console.error("API bağlantısını getirme hatası:", error);
      res.status(500).json({ error: "API bağlantısı getirilirken bir hata oluştu" });
    }
  });

  // Yeni bir API bağlantısı ekle
  app.post("/api/projects/:projectId/connections", protectRoute, async (req: Request, res: Response) => {
    try {
      const projectId = parseInt(req.params.projectId);

      // Projeyi kontrol et
      const project = await storage.getProject(projectId);
      if (!project) {
        return res.status(404).json({ error: "Proje bulunamadı" });
      }

      // Kullanıcının projeyi düzenleme yetkisi var mı kontrol et
      const hasAccess = await storage.hasProjectAccess(req.user!.id, projectId, "EDITOR");
      if (!hasAccess && req.user?.role !== 'admin') {
        return res.status(403).json({ error: "Bu projede düzenleme yetkiniz yok" });
      }

      // Request body'yi doğrula
      const validatedData = insertApiConnectionSchema.parse({
        ...req.body,
        projectId,
        createdBy: req.user!.id
      });

      // Aynı baseUrl ile bağlantı var mı kontrol et
      try {
        // @ts-ignore
        const existingConnections = await storage.getApiConnectionsByProjectId(projectId);
        const duplicateConnection = existingConnections.find((conn: any) =>
          conn.baseUrl === validatedData.baseUrl && conn.type === validatedData.type
        );

        if (duplicateConnection) {
          return res.status(409).json({
            error: "Bu URL ve tip ile zaten bir bağlantı mevcut",
            existingConnection: duplicateConnection
          });
        }
      } catch (error) {
        console.error("Mevcut bağlantı kontrolü hatası:", error);
      }

      // Settings JSON ise string'e çevir
      if (validatedData.settings && typeof validatedData.settings === 'object') {
        validatedData.settings = JSON.stringify(validatedData.settings);
      }

      // API bağlantısını oluştur
      try {
        // @ts-ignore
        const newConnection = await storage.createApiConnection(validatedData);
        res.status(201).json(newConnection);
      } catch (error) {
        console.error("API bağlantısı oluşturma hatası:", error);
        res.status(500).json({ error: "API bağlantısı oluşturulamadı" });
      }
    } catch (error) {
      console.error("API bağlantısı oluşturma hatası:", error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: "Geçersiz veri", details: error.errors });
      } else {
        res.status(500).json({ error: "API bağlantısı oluşturulurken bir hata oluştu" });
      }
    }
  });

  // Bir API bağlantısını güncelle
  app.put("/api/projects/:projectId/connections/:connectionId", protectRoute, async (req: Request, res: Response) => {
    try {
      const projectId = parseInt(req.params.projectId);
      const connectionId = parseInt(req.params.connectionId);

      // Kullanıcının projeyi düzenleme yetkisi var mı kontrol et
      const hasAccess = await storage.hasProjectAccess(req.user!.id, projectId, "EDITOR");
      if (!hasAccess && req.user?.role !== 'admin') {
        return res.status(403).json({ error: "Bu projede düzenleme yetkiniz yok" });
      }

      // Bağlantıyı kontrol et
      try {
        // @ts-ignore
        const connection = await storage.getApiConnection(connectionId);
        if (!connection || connection.projectId !== projectId) {
          return res.status(404).json({ error: "Bağlantı bulunamadı" });
        }

        // Sadece izin verilen alanları güncelle
        const allowedFields = ['name', 'type', 'baseUrl', 'apiKey', 'username', 'token', 'settings', 'status'];
        const updateData: Record<string, any> = {};

        for (const field of allowedFields) {
          if (req.body[field] !== undefined) {
            updateData[field] = req.body[field];
          }
        }

        // Settings JSON ise string'e çevir
        if (updateData.settings && typeof updateData.settings === 'object') {
          updateData.settings = JSON.stringify(updateData.settings);
        }

        // API bağlantısını güncelle
        // @ts-ignore
        const updatedConnection = await storage.updateApiConnection(connectionId, updateData);
        if (!updatedConnection) {
          return res.status(404).json({ error: "Bağlantı bulunamadı" });
        }

        res.json(updatedConnection);
      } catch (error) {
        console.error("API bağlantısını güncelleme hatası:", error);
        res.status(500).json({ error: "API bağlantısı güncellenemedi" });
      }
    } catch (error) {
      console.error("API bağlantısını güncelleme hatası:", error);
      res.status(500).json({ error: "API bağlantısı güncellenirken bir hata oluştu" });
    }
  });

  // Bir API bağlantısını sil
  app.delete("/api/projects/:projectId/connections/:connectionId", protectRoute, async (req: Request, res: Response) => {
    try {
      const projectId = parseInt(req.params.projectId);
      const connectionId = parseInt(req.params.connectionId);

      // Kullanıcının projeyi düzenleme yetkisi var mı kontrol et
      const hasAccess = await storage.hasProjectAccess(req.user!.id, projectId, "EDITOR");
      if (!hasAccess && req.user?.role !== 'admin') {
        return res.status(403).json({ error: "Bu projede düzenleme yetkiniz yok" });
      }

      // Bağlantıyı kontrol et
      try {
        // @ts-ignore
        const connection = await storage.getApiConnection(connectionId);
        if (!connection || connection.projectId !== projectId) {
          return res.status(404).json({ error: "Bağlantı bulunamadı" });
        }

        // API bağlantısını sil
        // @ts-ignore
        const result = await storage.deleteApiConnection(connectionId);
        if (!result) {
          return res.status(404).json({ error: "Bağlantı bulunamadı" });
        }

        res.json({ success: true, message: "API bağlantısı başarıyla silindi" });
      } catch (error) {
        console.error("API bağlantısını silme hatası:", error);
        res.status(500).json({ error: "API bağlantısı silinemedi" });
      }
    } catch (error) {
      console.error("API bağlantısını silme hatası:", error);
      res.status(500).json({ error: "API bağlantısı silinirken bir hata oluştu" });
    }
  });

  // AI Öğrenme Belleği API Endpointleri
  app.post("/api/coverage/teach", protectRoute, async (req: Request, res: Response) => {
    try {
      const {
        documentId,
        requirementCode,
        requirementDescription,
        missingInformation,
        solution
      } = req.body;

      if (!requirementCode || !missingInformation || !solution) {
        return res.status(400).json({ error: "Eksik bilgiler - gereksinim kodu, eksik bilgi ve çözüm gerekli" });
      }

      // Öğrenme belleğine ekle
      const memory = await storage.createAiLearningMemory({
        queryPattern: `${requirementCode} ${requirementDescription}`.substring(0, 250), // Sorgu deseni olarak gereksinim bilgileri
        knowledgeStore: {
          requirementCode,
          requirementDescription,
          missingInformation,
          solution,
          documentId,
          addedBy: req.user?.id,
          addedByName: req.user?.name || req.user?.username
        },
        successMetric: 0.95, // Başlangıç başarı metriği (yüksek)
        frequencyUsed: 1 // İlk kullanım
      });

      return res.status(201).json({
        message: "Öğrenme belleğine başarıyla eklendi",
        memory
      });
    } catch (error) {
      console.error("Öğrenme belleği ekleme hatası:", error);
      return res.status(500).json({ error: "Öğrenme belleğine eklerken bir hata oluştu" });
    }
  });

  app.get("/api/coverage/learnings", protectRoute, async (req: Request, res: Response) => {
    try {
      const { query } = req.query;
      let memories;

      if (query && typeof query === 'string') {
        memories = await storage.searchAiLearningMemory(query);
      } else {
        // Tüm öğrenme verilerini getir (en son 20 kayıt)
        memories = await storage.searchAiLearningMemory("");
      }

      return res.json(memories.slice(0, 20)); // En fazla 20 sonuç döndür
    } catch (error) {
      console.error("Öğrenme belleği getirme hatası:", error);
      return res.status(500).json({ error: "Öğrenme belleği sorgulanırken bir hata oluştu" });
    }
  });

  // Jira Entegrasyon API'leri
  app.post("/api/integrations/jira/test", protectRoute, async (req: Request, res: Response) => {
    try {
      const { url, username, apiToken } = req.body;

      if (!url || !username || !apiToken) {
        return res.status(400).json({
          success: false,
          message: "URL, kullanıcı adı ve API token gereklidir"
        });
      }

      // Jira API'ye test bağlantısı
      const testUrl = `${url.replace(/\/$/, '')}/rest/api/2/myself`;
      const auth = Buffer.from(`${username}:${apiToken}`).toString('base64');

      const response = await fetch(testUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Basic ${auth}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const userData = await response.json();
        res.json({
          success: true,
          message: "Bağlantı başarılı",
          user: {
            displayName: userData.displayName,
            emailAddress: userData.emailAddress
          }
        });
      } else {
        res.json({
          success: false,
          message: `Bağlantı başarısız: ${response.status} ${response.statusText}`
        });
      }
    } catch (error) {
      console.error("Jira bağlantı testi hatası:", error);
      res.json({
        success: false,
        message: `Bağlantı hatası: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`
      });
    }
  });

  app.post("/api/integrations/jira/projects", protectRoute, async (req: Request, res: Response) => {
    try {
      const { url, username, apiToken } = req.body;

      if (!url || !username || !apiToken) {
        return res.status(400).json({
          success: false,
          message: "Jira bağlantı bilgileri gerekli"
        });
      }

      const projectsUrl = `${url.replace(/\/$/, '')}/rest/api/2/project`;
      const auth = Buffer.from(`${username}:${apiToken}`).toString('base64');

      console.log("Jira projects request:", projectsUrl);

      const response = await fetch(projectsUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Basic ${auth}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      console.log("Jira projects response status:", response.status);

      if (response.ok) {
        const responseText = await response.text();
        console.log("Jira projects response text (first 200 chars):", responseText.substring(0, 200));

        let projects;
        try {
          projects = JSON.parse(responseText);
        } catch (parseError) {
          console.error("JSON parse error for projects:", parseError);
          console.error("Response text:", responseText);
          return res.status(500).json({
            success: false,
            message: "Jira'dan gelen yanıt geçerli JSON formatında değil",
            details: responseText.substring(0, 500)
          });
        }

        res.json({
          success: true,
          projects: projects.map((project: any) => ({
            id: project.id,
            key: project.key,
            name: project.name,
            description: project.description || '',
            lead: project.lead?.displayName || ''
          }))
        });
      } else {
        const errorText = await response.text();
        console.error("Jira projects error:", response.status, errorText);
        res.status(response.status).json({
          success: false,
          message: `Projeler alınamadı: ${response.statusText}`,
          details: errorText
        });
      }
    } catch (error) {
      console.error("Jira projeleri getirme hatası:", error);
      res.status(500).json({
        success: false,
        message: `Hata: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`
      });
    }
  });

  app.post("/api/integrations/jira/issues", protectRoute, async (req: Request, res: Response) => {
    try {
      const { url, username, apiToken, projectKey } = req.body;

      if (!url || !username || !apiToken || !projectKey) {
        return res.status(400).json({
          success: false,
          message: "Jira bağlantı bilgileri ve proje anahtarı gerekli"
        });
      }

      const issuesUrl = `${url.replace(/\/$/, '')}/rest/api/2/search?jql=project=${projectKey}&fields=key,summary,description,issuetype,status,priority,comment,attachment&maxResults=50`;
      const auth = Buffer.from(`${username}:${apiToken}`).toString('base64');

      console.log("Jira issues request:", issuesUrl);

      const response = await fetch(issuesUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Basic ${auth}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      console.log("Jira issues response status:", response.status);

      if (response.ok) {
        const responseText = await response.text();
        console.log("Jira issues response text (first 200 chars):", responseText.substring(0, 200));

        let data;
        try {
          data = JSON.parse(responseText);
        } catch (parseError) {
          console.error("JSON parse error for issues:", parseError);
          console.error("Response text:", responseText);
          return res.status(500).json({
            success: false,
            message: "Jira'dan gelen yanıt geçerli JSON formatında değil",
            details: responseText.substring(0, 500)
          });
        }

        const issues = data.issues.map((issue: any) => ({
          id: issue.id,
          key: issue.key,
          summary: issue.fields.summary,
          description: issue.fields.description || '',
          issueType: issue.fields.issuetype.name,
          status: issue.fields.status.name,
          priority: issue.fields.priority?.name || 'Unknown',
          assignee: issue.fields.assignee?.displayName || '',
          reporter: issue.fields.reporter?.displayName || '',
          created: issue.fields.created,
          updated: issue.fields.updated,
          comments: issue.fields.comment?.comments?.map((comment: any) => ({
            id: comment.id,
            author: comment.author.displayName,
            body: comment.body,
            created: comment.created
          })) || [],
          attachments: issue.fields.attachment?.map((attachment: any) => ({
            id: attachment.id,
            filename: attachment.filename,
            size: attachment.size,
            mimeType: attachment.mimeType,
            author: attachment.author.displayName,
            created: attachment.created,
            content: attachment.content
          })) || []
        }));

        res.json({
          success: true,
          issues
        });
      } else {
        const errorText = await response.text();
        console.error("Jira issues error:", response.status, errorText);
        res.status(response.status).json({
          success: false,
          message: `Issue'lar alınamadı: ${response.statusText}`,
          details: errorText
        });
      }
    } catch (error) {
      console.error("Jira issue'ları getirme hatası:", error);
      res.status(500).json({
        success: false,
        message: `Hata: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`
      });
    }
  });

  // Jira Issue Güncelleme
  app.put("/api/integrations/jira/issues/:issueKey", protectRoute, async (req: Request, res: Response) => {
    try {
      const { issueKey } = req.params;
      const { url, username, apiToken, updates } = req.body;

      if (!url || !username || !apiToken || !updates) {
        return res.status(400).json({
          success: false,
          message: "Gerekli parametreler eksik"
        });
      }

      const updateUrl = `${url.replace(/\/$/, '')}/rest/api/2/issue/${issueKey}`;
      const auth = Buffer.from(`${username}:${apiToken}`).toString('base64');

      // Jira API format'ına uygun update objesi oluştur
      const updatePayload = {
        fields: {}
      };

      if (updates.summary) {
        updatePayload.fields.summary = updates.summary;
      }
      if (updates.description !== undefined) {
        updatePayload.fields.description = updates.description;
      }
      if (updates.assignee) {
        updatePayload.fields.assignee = { name: updates.assignee };
      }

      // Status ve priority için transition gerekebilir, basit update yapalım
      const response = await fetch(updateUrl, {
        method: 'PUT',
        headers: {
          'Authorization': `Basic ${auth}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updatePayload)
      });

      if (response.ok || response.status === 204) {
        res.json({
          success: true,
          message: "Issue başarıyla güncellendi"
        });
      } else {
        const errorData = await response.text();
        res.status(response.status).json({
          success: false,
          message: `Issue güncellenemedi: ${response.statusText}`,
          details: errorData
        });
      }
    } catch (error) {
      console.error("Jira issue güncelleme hatası:", error);
      res.status(500).json({
        success: false,
        message: `Hata: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`
      });
    }
  });

  // Jira Issue Silme
  app.delete("/api/integrations/jira/issues/:issueKey", protectRoute, async (req: Request, res: Response) => {
    try {
      const { issueKey } = req.params;
      const { url, username, apiToken } = req.body;

      if (!url || !username || !apiToken) {
        return res.status(400).json({
          success: false,
          message: "Jira bağlantı bilgileri gerekli"
        });
      }

      const deleteUrl = `${url.replace(/\/$/, '')}/rest/api/2/issue/${issueKey}`;
      const auth = Buffer.from(`${username}:${apiToken}`).toString('base64');

      const response = await fetch(deleteUrl, {
        method: 'DELETE',
        headers: {
          'Authorization': `Basic ${auth}`,
          'Accept': 'application/json'
        }
      });

      if (response.ok || response.status === 204) {
        res.json({
          success: true,
          message: "Issue başarıyla silindi"
        });
      } else {
        res.status(response.status).json({
          success: false,
          message: `Issue silinemedi: ${response.statusText}`
        });
      }
    } catch (error) {
      console.error("Jira issue silme hatası:", error);
      res.status(500).json({
        success: false,
        message: `Hata: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`
      });
    }
  });

  // Jira Issue'ya Yorum Ekleme
  app.post("/api/integrations/jira/issues/:issueKey/comments", protectRoute, async (req: Request, res: Response) => {
    try {
      const { issueKey } = req.params;
      const { url, username, apiToken, comment } = req.body;

      if (!url || !username || !apiToken || !comment) {
        return res.status(400).json({
          success: false,
          message: "Gerekli parametreler eksik"
        });
      }

      const commentUrl = `${url.replace(/\/$/, '')}/rest/api/2/issue/${issueKey}/comment`;
      const auth = Buffer.from(`${username}:${apiToken}`).toString('base64');

      const response = await fetch(commentUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${auth}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          body: comment
        })
      });

      if (response.ok) {
        const commentData = await response.json();
        res.json({
          success: true,
          message: "Yorum başarıyla eklendi",
          comment: commentData
        });
      } else {
        res.status(response.status).json({
          success: false,
          message: `Yorum eklenemedi: ${response.statusText}`
        });
      }
    } catch (error) {
      console.error("Jira yorum ekleme hatası:", error);
      res.status(500).json({
        success: false,
        message: `Hata: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`
      });
    }
  });

  // Jira AI Bot Analizi
  app.post("/api/integrations/jira/bot-analysis", protectRoute, async (req: Request, res: Response) => {
    try {
      const { url, username, apiToken, issueKey, command, issueData } = req.body;

      if (!url || !username || !apiToken || !issueKey || !command || !issueData) {
        return res.status(400).json({
          success: false,
          message: "Gerekli parametreler eksik"
        });
      }

      // Komut tipini belirle
      let analysisType = 'general';
      if (command.includes('scenario')) analysisType = 'scenario';
      else if (command.includes('coverage')) analysisType = 'coverage';
      else if (command.includes('resource')) analysisType = 'resource';
      else if (command.includes('api')) analysisType = 'api';
      else if (command.includes('security')) analysisType = 'security';
      else if (command.includes('performance')) analysisType = 'performance';
      else if (command.includes('architecture')) analysisType = 'architecture';
      else if (command.includes('general')) analysisType = 'general';

      // AI analizi yap
      let analysis = '';
      let metadata = {};

      switch (analysisType) {
        case 'scenario':
          analysis = await generateTestScenariosForIssue(issueData);
          metadata = { type: 'test_scenarios', issueType: issueData.type };
          break;

        case 'coverage':
          analysis = await generateCoverageAnalysisForIssue(issueData);
          metadata = { type: 'coverage_analysis', priority: issueData.priority };
          break;

        case 'resource':
          analysis = await generateResourceAnalysisForIssue(issueData);
          metadata = { type: 'resource_analysis', components: [] };
          break;

        case 'api':
          analysis = await generateAPIAnalysisForIssue(issueData);
          metadata = { type: 'api_analysis', endpoints: [] };
          break;

        case 'security':
          analysis = await generateSecurityAnalysisForIssue(issueData);
          metadata = { type: 'security_analysis', riskLevel: 'medium' };
          break;

        case 'performance':
          analysis = await generatePerformanceAnalysisForIssue(issueData);
          metadata = { type: 'performance_analysis', metrics: [] };
          break;

        case 'architecture':
          analysis = await generateArchitectureAnalysisForIssue(issueData);
          metadata = { type: 'architecture_analysis', patterns: [] };
          break;

        default:
          analysis = await generateGeneralAnalysisForIssue(issueData);
          metadata = { type: 'general_analysis' };
      }

      // Jira'ya yorum olarak ekle
      const commentUrl = `${url.replace(/\/$/, '')}/rest/api/2/issue/${issueKey}/comment`;
      const auth = Buffer.from(`${username}:${apiToken}`).toString('base64');

      const botComment = `🤖 **AI Bot Analizi - ${analysisType.toUpperCase()}**\n\n${analysis}\n\n---\n*Bu analiz AI tarafından otomatik olarak oluşturulmuştur.*`;

      const commentResponse = await fetch(commentUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${auth}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          body: botComment
        })
      });

      if (commentResponse.ok) {
        res.json({
          success: true,
          message: "Bot analizi tamamlandı ve Jira'ya eklendi",
          analysisType,
          analysis,
          metadata,
          commentAdded: true
        });
      } else {
        // Analiz başarılı ama yorum eklenemedi
        res.json({
          success: true,
          message: "Bot analizi tamamlandı ancak Jira'ya yorum eklenemedi",
          analysisType,
          analysis,
          metadata,
          commentAdded: false,
          commentError: `${commentResponse.status}: ${commentResponse.statusText}`
        });
      }

    } catch (error) {
      console.error("Jira bot analizi hatası:", error);
      res.status(500).json({
        success: false,
        message: `Hata: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`
      });
    }
  });

  // OpenAI service'den bot analiz fonksiyonunu kullan
  async function callJiraBotAnalysis(prompt: string): Promise<string> {
    return await openaiService.generateJiraBotAnalysis(prompt, 3);
  }

  // Bot analiz fonksiyonları - Optimize edilmiş
  async function generateTestScenariosForIssue(issueData: any): Promise<string> {
    // Kısa ve odaklı prompt
    const prompt = `Test uzmanı olarak "${issueData.summary}" için test senaryoları oluştur.

ISSUE: ${issueData.summary}
TIP: ${issueData.type}
AÇIKLAMA: ${issueData.description || 'Açıklama yok'}

ÇIKTI FORMATI:

TEST SENARYOLARI

POZITIF TESTLER:
1. [Senaryo]: [Adımlar] → [Sonuç]
2. [Senaryo]: [Adımlar] → [Sonuç]

NEGATIF TESTLER:
1. [Hata Senaryosu]: [Adımlar] → [Hata]
2. [Sınır Testi]: [Değerler] → [Sonuç]

Kısa ve net yaz, maksimum 200 kelime.`;

    try {
      return await callJiraBotAnalysis(prompt);
    } catch (error) {
      return `❌ TEST SENARYOLARI OLUŞTURULAMADI

Hata: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}

ÖNERILEN MANUEL TEST SENARYOLARI:

1. Temel Fonksiyonalite Testi
   • ${issueData.summary} özelliğinin temel çalışmasını test et

2. Hata Durumu Testi
   • Geçersiz girişlerle sistem davranışını kontrol et

3. Entegrasyon Testi
   • Diğer sistemlerle entegrasyonu doğrula`;
    }
  }

  async function generateCoverageAnalysisForIssue(issueData: any): Promise<string> {
    const prompt = `Sen bir test analisti ve kalite uzmanısın. Aşağıdaki Jira issue için kapsamlı test kapsam analizi yap:

Issue: ${issueData.summary}
Açıklama: ${issueData.description}
Tip: ${issueData.type}
Öncelik: ${issueData.priority}

Lütfen Jira comment formatında (basit metin) kapsam analizi oluştur:

TEST KAPSAM ANALIZI

FONKSIYONEL KAPSAM:

Etkilenen Alanlar:
• Ana Fonksiyon: [Ana fonksiyonalite]
• Yan Fonksiyonlar: [İlgili fonksiyonlar]
• Entegrasyon Noktaları: [Diğer sistemlerle entegrasyonlar]

SISTEM BILEŞENLERI:

Etkilenen Katmanlar:
• Frontend: [UI bileşenleri]
• Backend: [API/Servis katmanları]
• Veritabanı: [Tablo/Model değişiklikleri]
• Harici Sistemler: [3. parti entegrasyonlar]

KULLANICI SENARYOLARI:

Hedef Kullanıcılar:
• Birincil: [Ana kullanıcı grubu]
• İkincil: [Diğer kullanıcı grupları]
• Yönetici: [Admin kullanıcıları]

RISK ANALIZI:

Risk Seviyesi: [YÜKSEK/ORTA/DÜŞÜK]
Risk Faktörleri:
• Teknik Risk: [Teknik zorluklar]
• İş Riski: [İş süreçlerine etkisi]
• Güvenlik Riski: [Güvenlik etkileri]

TEST KAPSAMI ÖNERILERI:

Zorunlu Testler:
1. [Test türü 1]
2. [Test türü 2]
3. [Test türü 3]

Opsiyonel Testler:
1. [Ek test türü 1]
2. [Ek test türü 2]

EKSIK TEST ALANLARI:

Dikkat Edilmesi Gerekenler:
• [Eksik alan 1]
• [Eksik alan 2]
• [Eksik alan 3]

KAPSAM METRIKLERI:

Tahmini Test Sayısı: [Sayı]
Tahmini Süre: [Gün/Saat]
Kaynak İhtiyacı: [Kişi sayısı]

Sadece kapsam analizini döndür, markdown kullanma, basit metin formatında yaz.`;

    try {
      const analysisResult = await openaiService.analyzeDocument(prompt);

      if (typeof analysisResult === 'object' && analysisResult !== null) {
        const content = (analysisResult as any).analysis || (analysisResult as any).content || (analysisResult as any).summary || JSON.stringify(analysisResult, null, 2);
        return typeof content === 'string' ? content : JSON.stringify(content, null, 2);
      }

      return typeof analysisResult === 'string' ? analysisResult : JSON.stringify(analysisResult, null, 2);
    } catch (error) {
      return `❌ KAPSAM ANALIZI OLUŞTURULAMADI

Hata: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}

TEMEL KAPSAM ÖNERILERI:

• Fonksiyonel Test: ${issueData.summary} özelliğinin tüm fonksiyonlarını test et
• Entegrasyon Testi: Diğer sistemlerle entegrasyonu doğrula
• Regresyon Testi: Mevcut özelliklerin etkilenmediğini kontrol et
• Performans Testi: Sistem performansını ölç`;
    }
  }

  async function generateResourceAnalysisForIssue(issueData: any): Promise<string> {
    const prompt = `Sen bir proje yöneticisi ve kaynak planlama uzmanısın. Aşağıdaki Jira issue için detaylı kaynak analizi yap:

Issue: ${issueData.summary}
Açıklama: ${issueData.description}
Tip: ${issueData.type}
Öncelik: ${issueData.priority}

Lütfen Jira comment formatında (basit metin) kaynak analizi oluştur:

KAYNAK ANALIZI

TEKNIK KAYNAKLAR:

Sistem Bileşenleri:
• Frontend: [Gerekli UI bileşenleri]
• Backend: [API/Servis katmanları]
• Veritabanı: [Tablo/Model değişiklikleri]
• Altyapı: [Sunucu/Cloud kaynakları]

ENTEGRASYON GEREKSINIMLERI:

Harici Sistemler:
• API'ler: [3. parti API'ler]
• Servisler: [Mikroservisler]
• Veritabanları: [Harici DB'ler]
• Araçlar: [Geliştirme araçları]

INSAN KAYNAKLARI:

Gerekli Roller:
• Frontend Developer: [Süre] gün
• Backend Developer: [Süre] gün
• DevOps Engineer: [Süre] gün
• QA Engineer: [Süre] gün
• UI/UX Designer: [Süre] gün

GELIŞTIRME ARAÇLARI:

Gerekli Araçlar:
• IDE/Editor: [Geliştirme ortamı]
• Framework: [Kullanılacak framework'ler]
• Kütüphaneler: [Gerekli kütüphaneler]
• Test Araçları: [Test framework'leri]

ZAMAN TAHMINI:

Geliştirme Aşamaları:
• Analiz & Tasarım: [X] gün
• Geliştirme: [X] gün
• Test: [X] gün
• Deployment: [X] gün
• Toplam: [X] gün

MALIYET TAHMINI:

Kaynak Maliyetleri:
• İnsan Kaynağı: [Maliyet]
• Altyapı: [Maliyet]
• Araçlar/Lisanslar: [Maliyet]
• Toplam: [Maliyet]

RISK FAKTÖRLERI:

Potansiyel Riskler:
• Teknik Risk: [Risk açıklaması]
• Kaynak Risk: [Kaynak eksikliği riski]
• Zaman Risk: [Gecikme riski]

ÖNKOŞULLAR:

Başlamadan Önce Gerekli:
• [Önkoşul 1]
• [Önkoşul 2]
• [Önkoşul 3]

Sadece kaynak analizini döndür, markdown kullanma, basit metin formatında yaz.`;

    try {
      const analysisResult = await openaiService.analyzeDocument(prompt);

      if (typeof analysisResult === 'object' && analysisResult !== null) {
        const content = (analysisResult as any).analysis || (analysisResult as any).content || (analysisResult as any).summary || JSON.stringify(analysisResult, null, 2);
        return typeof content === 'string' ? content : JSON.stringify(content, null, 2);
      }

      return typeof analysisResult === 'string' ? analysisResult : JSON.stringify(analysisResult, null, 2);
    } catch (error) {
      return `❌ KAYNAK ANALIZI OLUŞTURULAMADI

Hata: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}

TEMEL KAYNAK ÖNERILERI:

• Geliştirici: 1 Full-stack developer, 3-5 gün
• Test: 1 QA engineer, 2-3 gün
• Altyapı: Mevcut sistemler kullanılabilir
• Toplam Süre: 5-8 gün tahmini`;
    }
  }

  async function generateAPIAnalysisForIssue(issueData: any): Promise<string> {
    const prompt = `Sen bir API mimarı ve backend uzmanısın. Aşağıdaki Jira issue için detaylı API analizi yap:

**Issue:** ${issueData.summary}
**Açıklama:** ${issueData.description}
**Tip:** ${issueData.type}
**Öncelik:** ${issueData.priority}

Lütfen aşağıdaki formatta API analizi oluştur:

## 🔌 API Analizi

### 📡 Endpoint Gereksinimleri
**Gerekli API Endpoint'leri:**

#### 1. [Endpoint Adı]
- **URL:** \`/api/[endpoint-path]\`
- **Method:** [GET/POST/PUT/DELETE]
- **Açıklama:** [Endpoint açıklaması]

#### 2. [Endpoint Adı]
- **URL:** \`/api/[endpoint-path]\`
- **Method:** [GET/POST/PUT/DELETE]
- **Açıklama:** [Endpoint açıklaması]

### 📝 Request/Response Formatları

#### Request Format:
\`\`\`json
{
  "field1": "string",
  "field2": "number",
  "field3": "boolean"
}
\`\`\`

#### Response Format:
\`\`\`json
{
  "success": true,
  "data": {
    "id": "number",
    "name": "string"
  },
  "message": "string"
}
\`\`\`

### 🔒 Güvenlik Gereksinimleri
**Authentication:**
- **Tip:** [Bearer Token/API Key/OAuth]
- **Scope:** [Gerekli yetkiler]

**Authorization:**
- **Roller:** [Gerekli roller]
- **İzinler:** [Gerekli izinler]

### ⚠️ Hata Kodları
**HTTP Status Kodları:**
- **200:** Başarılı
- **400:** Geçersiz istek
- **401:** Yetkisiz erişim
- **403:** Yasak
- **404:** Bulunamadı
- **500:** Sunucu hatası

### 📊 Performans Gereksinimleri
**Rate Limiting:**
- **Limit:** [İstek/dakika]
- **Burst:** [Maksimum burst]

**Response Time:**
- **Hedef:** [ms]
- **Maksimum:** [ms]

### 🔗 Entegrasyon Noktaları
**Harici API'ler:**
- [API adı]: [Kullanım amacı]
- [API adı]: [Kullanım amacı]

### 📚 Dokümantasyon Önerileri
**Gerekli Dokümantasyon:**
- API Reference
- Code Examples
- Error Handling Guide
- Authentication Guide

Sadece API analizini döndür, başka açıklama ekleme.`;

    try {
      return await callJiraBotAnalysis(prompt);
    } catch (error) {
      return `## ❌ API Analizi Oluşturulamadı

**Hata:** ${error instanceof Error ? error.message : 'Bilinmeyen hata'}

**Temel API Önerileri:**
- **CRUD Endpoint'leri:** GET, POST, PUT, DELETE
- **Authentication:** Bearer Token kullan
- **Response Format:** JSON standardı
- **Error Handling:** HTTP status kodları ile`;
    }
  }

  async function generateGeneralAnalysisForIssue(issueData: any): Promise<string> {
    const prompt = `Sen bir iş analisti ve proje uzmanısın. Aşağıdaki Jira issue için kapsamlı genel analiz yap:

**Issue:** ${issueData.summary}
**Açıklama:** ${issueData.description}
**Tip:** ${issueData.type}
**Öncelik:** ${issueData.priority}

Lütfen aşağıdaki formatta genel analiz oluştur:

## 📋 Genel Analiz

### 🎯 Issue Amacı ve Kapsamı
**Ana Amaç:** [Issue'nun temel amacı]
**Kapsam:** [Neyi içeriyor, neyi içermiyor]
**Hedef Kullanıcılar:** [Kim için geliştirilecek]

### 💼 İş Gereksinimleri
**İş Değeri:** [Bu issue'nun iş değeri]
**Başarı Kriterleri:** [Nasıl ölçülecek]
**İş Süreçlerine Etkisi:** [Mevcut süreçlere etkisi]

### 🔧 Teknik Gereksinimler
**Fonksiyonel Gereksinimler:**
- [Gereksinim 1]
- [Gereksinim 2]
- [Gereksinim 3]

**Non-Fonksiyonel Gereksinimler:**
- **Performans:** [Performans kriterleri]
- **Güvenlik:** [Güvenlik gereksinimleri]
- **Kullanılabilirlik:** [UX gereksinimleri]

### ⚠️ Risk Analizi
**Yüksek Riskler:**
- [Risk 1]: [Etki ve olasılık]
- [Risk 2]: [Etki ve olasılık]

**Orta Riskler:**
- [Risk 1]: [Etki ve olasılık]

**Risk Azaltma Stratejileri:**
- [Strateji 1]
- [Strateji 2]

### 🔗 Bağımlılıklar
**Teknik Bağımlılıklar:**
- [Bağımlılık 1]
- [Bağımlılık 2]

**İş Bağımlılıkları:**
- [Bağımlılık 1]
- [Bağımlılık 2]

### ✅ Kabul Kriterleri Önerileri
**Scenario 1:** [Given-When-Then formatında]
- **Given:** [Ön koşul]
- **When:** [Aksiyon]
- **Then:** [Beklenen sonuç]

**Scenario 2:** [Given-When-Then formatında]
- **Given:** [Ön koşul]
- **When:** [Aksiyon]
- **Then:** [Beklenen sonuç]

### 🛠️ Geliştirme Yaklaşımı
**Önerilen Yaklaşım:** [Agile/Waterfall/Hybrid]
**Sprint Planı:** [Sprint'lere bölüm]
**Milestone'lar:** [Önemli kilometre taşları]

### 🧪 Test Stratejisi
**Test Seviyeleri:**
- **Unit Test:** [Birim testleri]
- **Integration Test:** [Entegrasyon testleri]
- **System Test:** [Sistem testleri]
- **User Acceptance Test:** [Kullanıcı kabul testleri]

### 📊 Başarı Metrikleri
**KPI'lar:**
- [Metrik 1]: [Hedef değer]
- [Metrik 2]: [Hedef değer]
- [Metrik 3]: [Hedef değer]

Sadece genel analizi döndür, başka açıklama ekleme.`;

    try {
      return await callJiraBotAnalysis(prompt);
    } catch (error) {
      return `## ❌ Genel Analiz Oluşturulamadı

**Hata:** ${error instanceof Error ? error.message : 'Bilinmeyen hata'}

**Temel Analiz Önerileri:**
- **Amaç:** ${issueData.summary} özelliğini geliştirmek
- **Kapsam:** Temel fonksiyonalite ve test
- **Risk:** Orta seviye teknik risk
- **Süre:** 5-10 gün tahmini`;
    }
  }

  async function generateSecurityAnalysisForIssue(issueData: any): Promise<string> {
    const prompt = `Sen bir siber güvenlik uzmanısın. Aşağıdaki Jira issue için detaylı güvenlik analizi yap:

**Issue:** ${issueData.summary}
**Açıklama:** ${issueData.description}
**Tip:** ${issueData.type}
**Öncelik:** ${issueData.priority}

Lütfen aşağıdaki formatta güvenlik analizi oluştur:

## 🔒 Güvenlik Analizi

### ⚠️ Güvenlik Riskleri
**Yüksek Risk:**
- [Risk 1]: [Açıklama ve etki]
- [Risk 2]: [Açıklama ve etki]

**Orta Risk:**
- [Risk 1]: [Açıklama ve etki]

**Düşük Risk:**
- [Risk 1]: [Açıklama ve etki]

### 🛡️ Güvenlik Kontrolleri
**Authentication:**
- [Kontrol 1]
- [Kontrol 2]

**Authorization:**
- [Kontrol 1]
- [Kontrol 2]

**Data Protection:**
- [Kontrol 1]
- [Kontrol 2]

### 🔍 Güvenlik Testleri
**Penetration Testing:**
- [Test 1]
- [Test 2]

**Vulnerability Assessment:**
- [Test 1]
- [Test 2]

### 📋 Güvenlik Gereksinimleri
**Compliance:**
- [Gereksinim 1]
- [Gereksinim 2]

**Standards:**
- [Standard 1]
- [Standard 2]

Sadece güvenlik analizini döndür, başka açıklama ekleme.`;

    try {
      return await callJiraBotAnalysis(prompt);
    } catch (error) {
      return `## ❌ Güvenlik Analizi Oluşturulamadı

**Hata:** ${error instanceof Error ? error.message : 'Bilinmeyen hata'}

**Temel Güvenlik Önerileri:**
- **Authentication:** Güçlü kimlik doğrulama kullan
- **Authorization:** Rol bazlı erişim kontrolü
- **Data Encryption:** Hassas verileri şifrele
- **Input Validation:** Girdi doğrulaması yap`;
    }
  }

  async function generatePerformanceAnalysisForIssue(issueData: any): Promise<string> {
    const prompt = `Sen bir performans uzmanısın. Aşağıdaki Jira issue için detaylı performans analizi yap:

**Issue:** ${issueData.summary}
**Açıklama:** ${issueData.description}
**Tip:** ${issueData.type}
**Öncelik:** ${issueData.priority}

Lütfen aşağıdaki formatta performans analizi oluştur:

## ⚡ Performans Analizi

### 📊 Performans Metrikleri
**Response Time:**
- **Hedef:** [ms]
- **Maksimum:** [ms]
- **Ortalama:** [ms]

**Throughput:**
- **Hedef:** [req/sec]
- **Peak:** [req/sec]

**Resource Usage:**
- **CPU:** [%]
- **Memory:** [MB]
- **Disk I/O:** [MB/s]

### 🔍 Performans Testleri
**Load Testing:**
- [Test senaryosu 1]
- [Test senaryosu 2]

**Stress Testing:**
- [Test senaryosu 1]
- [Test senaryosu 2]

**Volume Testing:**
- [Test senaryosu 1]

### 🚀 Optimizasyon Önerileri
**Database:**
- [Öneri 1]
- [Öneri 2]

**Application:**
- [Öneri 1]
- [Öneri 2]

**Infrastructure:**
- [Öneri 1]
- [Öneri 2]

### 📈 Monitoring
**Key Metrics:**
- [Metrik 1]
- [Metrik 2]

**Alerting:**
- [Alert 1]
- [Alert 2]

Sadece performans analizini döndür, başka açıklama ekleme.`;

    try {
      return await callJiraBotAnalysis(prompt);
    } catch (error) {
      return `## ❌ Performans Analizi Oluşturulamadı

**Hata:** ${error instanceof Error ? error.message : 'Bilinmeyen hata'}

**Temel Performans Önerileri:**
- **Response Time:** < 200ms hedefle
- **Caching:** Uygun cache stratejisi kullan
- **Database:** Index optimizasyonu yap
- **Monitoring:** Sürekli izleme kur`;
    }
  }

  async function generateArchitectureAnalysisForIssue(issueData: any): Promise<string> {
    const prompt = `Sen bir yazılım mimarısın. Aşağıdaki Jira issue için detaylı mimari analiz yap:

**Issue:** ${issueData.summary}
**Açıklama:** ${issueData.description}
**Tip:** ${issueData.type}
**Öncelik:** ${issueData.priority}

Lütfen aşağıdaki formatta mimari analiz oluştur:

## 🏗️ Mimari Analiz

### 🎯 Mimari Yaklaşım
**Pattern:** [Microservices/Monolith/Serverless]
**Style:** [RESTful/GraphQL/Event-driven]
**Paradigm:** [DDD/Clean Architecture/Hexagonal]

### 🧩 Sistem Bileşenleri
**Frontend:**
- [Component 1]
- [Component 2]

**Backend:**
- [Service 1]
- [Service 2]

**Data Layer:**
- [Database 1]
- [Cache 1]

### 🔗 Entegrasyon Mimarisi
**Internal APIs:**
- [API 1]: [Açıklama]
- [API 2]: [Açıklama]

**External APIs:**
- [API 1]: [Açıklama]
- [API 2]: [Açıklama]

### 📦 Deployment Mimarisi
**Environment:**
- **Development:** [Yapı]
- **Staging:** [Yapı]
- **Production:** [Yapı]

**Infrastructure:**
- **Containers:** [Docker/Kubernetes]
- **Cloud:** [AWS/Azure/GCP]
- **CI/CD:** [Pipeline yapısı]

### 🔄 Data Flow
**Request Flow:**
1. [Adım 1]
2. [Adım 2]
3. [Adım 3]

**Data Flow:**
1. [Adım 1]
2. [Adım 2]
3. [Adım 3]

### 🛡️ Non-Functional Requirements
**Scalability:** [Ölçeklenebilirlik stratejisi]
**Reliability:** [Güvenilirlik yaklaşımı]
**Maintainability:** [Sürdürülebilirlik]

Sadece mimari analizi döndür, başka açıklama ekleme.`;

    try {
      return await callJiraBotAnalysis(prompt);
    } catch (error) {
      return `## ❌ Mimari Analiz Oluşturulamadı

**Hata:** ${error instanceof Error ? error.message : 'Bilinmeyen hata'}

**Temel Mimari Önerileri:**
- **Pattern:** Microservices yaklaşımı değerlendir
- **API:** RESTful API tasarımı kullan
- **Database:** Uygun veritabanı seç
- **Scalability:** Horizontal scaling planla`;
    }
  }

  // Jira Attachment İşleme Fonksiyonu
  async function processJiraAttachment(attachment: any, auth: string, issueKey: string, userId: number) {
    try {
      console.log(`📎 Processing attachment: ${attachment.filename} (${attachment.mimeType})`);

      // Desteklenen dosya türlerini kontrol et
      const supportedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
        'text/csv',
        'application/json',
        'application/xml',
        'text/xml'
      ];

      if (!supportedTypes.includes(attachment.mimeType)) {
        console.log(`⚠️ Desteklenmeyen dosya türü: ${attachment.mimeType} - ${attachment.filename}`);
        return;
      }

      // Dosya boyutu kontrolü (50MB limit)
      const maxSize = 50 * 1024 * 1024; // 50MB
      if (attachment.size > maxSize) {
        console.log(`⚠️ Dosya çok büyük: ${attachment.filename} (${attachment.size} bytes)`);
        return;
      }

      // Attachment'ı indir
      const downloadResponse = await fetch(attachment.content, {
        method: 'GET',
        headers: {
          'Authorization': `Basic ${auth}`,
          'Accept': '*/*'
        }
      });

      if (!downloadResponse.ok) {
        throw new Error(`Attachment indirilemedi: ${downloadResponse.statusText}`);
      }

      const fileBuffer = Buffer.from(await downloadResponse.arrayBuffer());
      console.log(`✅ Attachment indirildi: ${attachment.filename} (${fileBuffer.length} bytes)`);

      // Dosya uzantısını belirle
      const fileExtension = attachment.filename.split('.').pop()?.toLowerCase() || 'unknown';

      // Doküman olarak kaydet
      const document = await storage.createDocument({
        projectId: 1, // Default proje
        name: `${issueKey} - ${attachment.filename}`,
        content: `Jira Issue ${issueKey} eki: ${attachment.filename}\n\nDosya Türü: ${attachment.mimeType}\nBoyut: ${attachment.size} bytes\nYükleyen: ${attachment.author.displayName}\nTarih: ${attachment.created}`,
        type: 'jira-attachment',
        createdBy: userId
      });

      console.log(`📄 Doküman oluşturuldu: ${document.name} (ID: ${document.id})`);

      // Dosyayı işle ve analiz et
      try {
        const processedContent = await documentProcessor.processDocument(fileBuffer, fileExtension, false);
        console.log(`🔍 Attachment işlendi: ${attachment.filename}`);

        // AI analizi yap
        const { analyzeDocument } = await import('./services/openai');
        const analysisResult = await analyzeDocument(processedContent);

        if (analysisResult) {
          console.log(`🤖 Attachment AI analizi tamamlandı: ${attachment.filename}`);

          // Analiz sonuçlarını kaydet
          await storage.saveAIAnalysis(document.id, analysisResult);

          // Bileşenleri kaydet
          if (analysisResult.components?.length > 0) {
            for (const comp of analysisResult.components) {
              await storage.createComponent({
                documentId: document.id,
                name: comp.name || 'Bilinmeyen Bileşen',
                description: comp.description || null,
                type: comp.type || null
              });
            }
          }

          // Gereksinimleri kaydet
          if (analysisResult.requirements?.length > 0) {
            for (const req of analysisResult.requirements) {
              await storage.createRequirement({
                documentId: document.id,
                code: req.code || `REQ-${Date.now()}`,
                description: req.description || 'Gereksinim açıklaması',
                category: req.category || null
              });
            }
          }

          // API endpoint'leri kaydet
          if (analysisResult.apiEndpoints?.length > 0) {
            for (const endpoint of analysisResult.apiEndpoints) {
              await storage.createApiEndpoint({
                documentId: document.id,
                url: endpoint.url || '/api/unknown',
                method: endpoint.method || 'GET',
                parameters: endpoint.parameters || {},
                requirementCode: endpoint.requirementCode || null
              });
            }
          }
        }
      } catch (processingError) {
        console.error(`❌ Attachment işleme hatası: ${attachment.filename}`, processingError);
      }

    } catch (error) {
      console.error(`❌ Jira attachment işleme hatası:`, error);
      throw error;
    }
  }

  // Rate limiting için memory store
  const botRequestTracker = new Map<string, { count: number; lastRequest: number; processing: Set<string> }>();

  // İşlenmiş comment'leri takip et (duplicate comment processing'i engellemek için)
  const processedComments = new Set<string>();

  // Issue bazlı komut geçmişi (her issue için hangi komutların çalıştırıldığını takip et)
  const issueCommandHistory = new Map<string, Set<string>>();

  // Rate limiting temizleme (her 5 dakikada bir)
  setInterval(() => {
    const now = Date.now();
    const fiveMinutesAgo = now - (5 * 60 * 1000);

    // Rate limit tracker temizleme
    botRequestTracker.forEach((data, key) => {
      if (data.lastRequest < fiveMinutesAgo) {
        botRequestTracker.delete(key);
      }
    });

    // Processed comments temizleme (1 saat sonra)
    if (processedComments.size > 1000) { // Çok büyürse temizle
      processedComments.clear();
      console.log('🧹 Processed comments cache cleared');
    }

    // Issue command history temizleme (24 saat sonra)
    if (issueCommandHistory.size > 500) { // Çok büyürse temizle
      issueCommandHistory.clear();
      console.log('🧹 Issue command history cache cleared');
    }
  }, 5 * 60 * 1000);

  // Jira Webhook - Comment oluşturulduğunda tetiklenir
  app.post("/api/integrations/jira/webhook", async (req: Request, res: Response) => {
    let rateLimitKey: string | undefined;
    let processingKey: string | undefined;

    try {
      const webhookData = req.body;

      console.log("Jira webhook received - Event:", webhookData.webhookEvent, "Issue:", webhookData.issue?.key);

      // Webhook event tipini kontrol et
      if (webhookData.webhookEvent !== 'comment_created') {
        return res.status(200).json({ message: 'Event ignored - not a comment creation' });
      }

      const comment = webhookData.comment;
      const issue = webhookData.issue;

      if (!comment || !issue) {
        return res.status(400).json({ error: 'Missing comment or issue data' });
      }

      // Aynı comment'in birden fazla işlenmesini engelle
      const commentId = comment.id;
      if (processedComments.has(commentId)) {
        console.log(`🔄 Comment ${commentId} already processed, ignoring`);
        return res.status(200).json({ message: 'Comment already processed' });
      }

      // Comment'i işlenmiş olarak işaretle
      processedComments.add(commentId);

      // Bot'un kendi comment'lerini filtrele
      const commentBody = comment.body || '';
      const commentAuthor = comment.author?.displayName || comment.author?.emailAddress || '';

      // Bot'un kendi yanıtlarını tespit et (içerik bazlı)
      if (commentBody.includes('🤖 AI BOT ANALIZI') ||
        commentBody.includes('Bu analiz AI tarafından otomatik olarak oluşturulmuştur') ||
        commentBody.includes('AI Bot Analizi') ||
        commentBody.includes('Komut:') ||
        commentBody.includes('Zaman:')) {
        console.log(`🤖 Ignoring bot's own comment in issue ${issue.key} (content-based)`);
        return res.status(200).json({ message: 'Ignoring bot\'s own comment' });
      }

      // Bot hesabından gelen comment'leri filtrele (eğer bot özel hesap kullanıyorsa)
      if (commentAuthor.toLowerCase().includes('bot') ||
        commentAuthor.toLowerCase().includes('automation') ||
        commentAuthor.toLowerCase().includes('ai')) {
        console.log(`🤖 Ignoring potential bot comment from ${commentAuthor} in issue ${issue.key}`);
        return res.status(200).json({ message: 'Ignoring potential bot comment' });
      }

      // Comment içinde bot komutunu ara - Sıkı format kontrolü
      const validCommands = ['scenario', 'coverage', 'resource', 'api', 'security', 'performance', 'architecture', 'general'];

      // Süslü parantez ve normal format desteği
      const botCommandRegex = /(?:\{\{)?@ai-bot\s+(scenario|coverage|resource|api|security|performance|architecture|general)(?:\}\})?(?:\s|$)/i;
      const match = commentBody.match(botCommandRegex);

      if (!match) {
        // Eğer @ai-bot var ama geçersiz komut varsa uyarı ver
        if (commentBody.includes('@ai-bot')) {
          console.log(`⚠️ Invalid bot command in issue ${issue.key}: "${commentBody.trim()}"`);
          return res.status(200).json({
            message: 'Invalid bot command format',
            validCommands: validCommands.map(cmd => `@ai-bot ${cmd} veya {{@ai-bot ${cmd}}}`)
          });
        }
        return res.status(200).json({ message: 'No bot command found in comment' });
      }

      const analysisType = match[1].toLowerCase();
      const command = `@ai-bot ${analysisType}`;

      // Komutun geçerli olduğunu doğrula
      if (!validCommands.includes(analysisType)) {
        console.log(`⚠️ Invalid analysis type: ${analysisType}`);
        return res.status(400).json({
          error: 'Invalid analysis type',
          validCommands: validCommands.map(cmd => `@ai-bot ${cmd}`)
        });
      }

      console.log(`Bot command detected: ${command} in issue ${issue.key}`);

      // Issue bazlı komut geçmişi kontrolü (aynı issue'da aynı komut 1 kez çalışabilir)
      const issueKey = issue.key;
      const issueCommands = issueCommandHistory.get(issueKey) || new Set<string>();

      if (issueCommands.has(analysisType)) {
        console.log(`⚠️ Command ${analysisType} already executed for issue ${issueKey}`);
        return res.status(409).json({
          error: 'Command already executed',
          message: `${analysisType} analizi bu issue için zaten çalıştırılmış. Her komut issue başına sadece 1 kez çalışabilir.`
        });
      }

      // Rate limiting kontrolü
      rateLimitKey = `${issue.key}-${comment.author.accountId}`;
      processingKey = `${issue.key}-${analysisType}`;
      const now = Date.now();

      // Kullanıcı bazlı rate limiting (5 dakikada maksimum 3 komut)
      const userTracker = botRequestTracker.get(rateLimitKey) || { count: 0, lastRequest: 0, processing: new Set() };

      // 5 dakika içindeki istekleri say
      if (now - userTracker.lastRequest < 5 * 60 * 1000) {
        if (userTracker.count >= 3) {
          console.log(`⚠️ Rate limit exceeded for user ${comment.author.displayName} in issue ${issue.key}`);
          return res.status(429).json({
            error: 'Rate limit exceeded',
            message: 'Maksimum 5 dakikada 3 bot komutu kullanabilirsiniz. Lütfen bekleyin.'
          });
        }
        userTracker.count++;
      } else {
        // 5 dakika geçmiş, sayacı sıfırla
        userTracker.count = 1;
        userTracker.processing.clear();
      }

      userTracker.lastRequest = now;

      // Aynı issue ve analiz türü için duplicate request kontrolü
      if (userTracker.processing.has(processingKey)) {
        console.log(`⚠️ Duplicate request detected for ${processingKey}`);
        return res.status(409).json({
          error: 'Request already processing',
          message: `${analysisType} analizi bu issue için zaten işleniyor. Lütfen bekleyin.`
        });
      }

      // Processing set'e ekle
      userTracker.processing.add(processingKey);
      botRequestTracker.set(rateLimitKey, userTracker);

      console.log(`✅ Rate limit check passed for ${rateLimitKey} (${userTracker.count}/3)`);

      // Jira connection bilgilerini bul (webhook'tan gelen issue'ya göre)
      const jiraBaseUrl = webhookData.issue.self.split('/rest/api')[0];

      console.log(`Looking for Jira connection with URL: ${jiraBaseUrl}`);

      // Veritabanından bu Jira instance için connection bilgilerini bul
      // Tüm projelerdeki bağlantıları kontrol et (basit yaklaşım)
      let jiraConnection = null;

      // Projeler üzerinde döngü yaparak Jira bağlantısını bul
      const projects = await storage.getProjects();
      for (const project of projects) {
        const connections = await storage.getApiConnectionsByProjectId(project.id);

        const foundConnection = connections.find((conn: any) => {
          // URL'leri normalize et (trailing slash'leri kaldır)
          const connUrl = conn.baseUrl?.replace(/\/$/, '');
          const webhookUrl = jiraBaseUrl.replace(/\/$/, '');

          return conn.type === 'jira' && connUrl === webhookUrl;
        });

        if (foundConnection) {
          jiraConnection = foundConnection;
          console.log(`✅ Found Jira connection for ${jiraBaseUrl}`);
          break;
        }
      }

      if (!jiraConnection) {
        console.error(`No Jira connection found for URL: ${jiraBaseUrl}`);
        return res.status(404).json({ error: 'Jira connection not found' });
      }

      // Issue data'sını hazırla
      const issueData = {
        summary: issue.fields.summary,
        description: issue.fields.description || '',
        type: issue.fields.issuetype.name,
        priority: issue.fields.priority?.name || 'Medium'
      };

      // Bot analizini çalıştır
      let analysis = '';
      let metadata = {};

      switch (analysisType) {
        case 'scenario':
          analysis = await generateTestScenariosForIssue(issueData);
          metadata = { type: 'test_scenarios', issueType: issueData.type };
          break;

        case 'coverage':
          analysis = await generateCoverageAnalysisForIssue(issueData);
          metadata = { type: 'coverage_analysis', priority: issueData.priority };
          break;

        case 'resource':
          analysis = await generateResourceAnalysisForIssue(issueData);
          metadata = { type: 'resource_analysis', components: [] };
          break;

        case 'api':
          analysis = await generateAPIAnalysisForIssue(issueData);
          metadata = { type: 'api_analysis', endpoints: [] };
          break;

        case 'security':
          analysis = await generateSecurityAnalysisForIssue(issueData);
          metadata = { type: 'security_analysis', riskLevel: 'medium' };
          break;

        case 'performance':
          analysis = await generatePerformanceAnalysisForIssue(issueData);
          metadata = { type: 'performance_analysis', metrics: [] };
          break;

        case 'architecture':
          analysis = await generateArchitectureAnalysisForIssue(issueData);
          metadata = { type: 'architecture_analysis', patterns: [] };
          break;

        default:
          analysis = await generateGeneralAnalysisForIssue(issueData);
          metadata = { type: 'general_analysis' };
      }

      // Jira'ya bot yanıtını comment olarak ekle
      const commentUrl = `${jiraConnection.baseUrl}/rest/api/2/issue/${issue.key}/comment`;
      const auth = Buffer.from(`${jiraConnection.username}:${jiraConnection.token}`).toString('base64');

      const botComment = `🤖 AI BOT ANALIZI - ${analysisType.toUpperCase()}

${analysis}

---
Bu analiz AI tarafından otomatik olarak oluşturulmuştur.
Komut: ${command}
Zaman: ${new Date().toLocaleString('tr-TR')}`;

      const commentResponse = await fetch(commentUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${auth}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          body: botComment
        })
      });

      if (commentResponse.ok) {
        // Başarılı analiz sonrası komut geçmişine ekle
        issueCommands.add(analysisType);
        issueCommandHistory.set(issueKey, issueCommands);

        console.log(`🤖 Bot response added to issue ${issue.key} - Analysis: ${analysisType}`);
        res.json({
          success: true,
          message: `Bot analizi tamamlandı ve ${issue.key} issue'sına eklendi`,
          analysisType,
          issueKey: issue.key
        });
      } else {
        const errorText = await commentResponse.text();
        console.error(`❌ Failed to add comment to ${issue.key}:`, errorText);
        res.status(500).json({
          success: false,
          message: `Bot analizi tamamlandı ancak Jira'ya yorum eklenemedi: ${commentResponse.statusText}`
        });
      }

      // Processing'den kaldır (başarılı veya başarısız olsun)
      const finalUserTracker = botRequestTracker.get(rateLimitKey);
      if (finalUserTracker) {
        finalUserTracker.processing.delete(processingKey);
        botRequestTracker.set(rateLimitKey, finalUserTracker);
      }

    } catch (error) {
      console.error("Jira webhook processing error:", error);

      // Hata durumunda da processing'den kaldır
      if (typeof rateLimitKey !== 'undefined' && typeof processingKey !== 'undefined') {
        const errorUserTracker = botRequestTracker.get(rateLimitKey);
        if (errorUserTracker) {
          errorUserTracker.processing.delete(processingKey);
          botRequestTracker.set(rateLimitKey, errorUserTracker);
        }
      }

      res.status(500).json({
        success: false,
        message: `Webhook işleme hatası: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`
      });
    }
  });

  // Webhook test endpoint
  app.get("/api/integrations/jira/webhook/test", async (req: Request, res: Response) => {
    res.json({
      success: true,
      message: "Jira AI Bot webhook endpoint is working!",
      timestamp: new Date().toISOString(),
      supportedCommands: [
        '@ai-bot scenario (veya {{@ai-bot scenario}})',
        '@ai-bot coverage (veya {{@ai-bot coverage}})',
        '@ai-bot resource (veya {{@ai-bot resource}})',
        '@ai-bot api (veya {{@ai-bot api}})',
        '@ai-bot security (veya {{@ai-bot security}})',
        '@ai-bot performance (veya {{@ai-bot performance}})',
        '@ai-bot architecture (veya {{@ai-bot architecture}})',
        '@ai-bot general (veya {{@ai-bot general}})'
      ]
    });
  });

  app.post("/api/integrations/jira/analyze", protectRoute, async (req: Request, res: Response) => {
    try {
      const { url, username, apiToken, projectKey, issueKeys } = req.body;

      if (!issueKeys || !Array.isArray(issueKeys) || issueKeys.length === 0) {
        return res.status(400).json({
          success: false,
          message: "En az bir issue seçilmelidir"
        });
      }

      const auth = Buffer.from(`${username}:${apiToken}`).toString('base64');
      const analyzedDocuments = [];

      for (const issueKey of issueKeys) {
        try {
          // Issue detaylarını al (attachment'ları da dahil et)
          const issueUrl = `${url.replace(/\/$/, '')}/rest/api/2/issue/${issueKey}?expand=comments,attachment`;
          const issueResponse = await fetch(issueUrl, {
            method: 'GET',
            headers: {
              'Authorization': `Basic ${auth}`,
              'Accept': 'application/json'
            }
          });

          if (!issueResponse.ok) {
            console.error(`Issue ${issueKey} alınamadı:`, issueResponse.statusText);
            continue;
          }

          const issue = await issueResponse.json();

          // Issue içeriğini birleştir
          let content = `Issue: ${issue.key}\n\n`;
          content += `Başlık: ${issue.fields.summary}\n\n`;

          if (issue.fields.description) {
            content += `Açıklama:\n${issue.fields.description}\n\n`;
          }

          content += `Tip: ${issue.fields.issuetype.name}\n`;
          content += `Durum: ${issue.fields.status.name}\n`;
          content += `Öncelik: ${issue.fields.priority?.name || 'Belirtilmemiş'}\n\n`;

          // Yorumları ekle
          if (issue.fields.comment?.comments?.length > 0) {
            content += "Yorumlar:\n";
            issue.fields.comment.comments.forEach((comment: any) => {
              content += `- ${comment.author.displayName} (${comment.created}):\n`;
              content += `  ${comment.body}\n\n`;
            });
          }

          // Attachment'ları listele
          if (issue.fields.attachment?.length > 0) {
            content += "Ekler:\n";
            issue.fields.attachment.forEach((attachment: any) => {
              content += `- ${attachment.filename} (${attachment.size} bytes) - ${attachment.mimeType}\n`;
              content += `  Yükleyen: ${attachment.author.displayName} (${attachment.created})\n`;
              content += `  URL: ${attachment.content}\n\n`;
            });
          }

          // Doküman olarak kaydet
          const document = await storage.createDocument({
            projectId: 1, // Default proje (admin kullanıcının projesi)
            name: `Jira Issue: ${issue.key} - ${issue.fields.summary}`,
            content,
            type: 'jira',
            createdBy: req.user!.id
          });

          analyzedDocuments.push({
            issueKey: issue.key,
            documentId: document.id,
            title: document.name
          });

          // Attachment'ları işle ve ayrı dokümanlar olarak kaydet
          if (issue.fields.attachment?.length > 0) {
            console.log(`Issue ${issueKey} için ${issue.fields.attachment.length} attachment bulundu`);

            for (const attachment of issue.fields.attachment) {
              try {
                await processJiraAttachment(attachment, auth, issue.key, req.user!.id);
              } catch (attachmentError) {
                console.error(`Attachment ${attachment.filename} işleme hatası:`, attachmentError);
              }
            }
          }

          // Dokümanı analiz et
          try {
            // Jira issue içeriğini Buffer olarak işle
            const contentBuffer = Buffer.from(content, 'utf-8');
            const analyzedContent = await documentProcessor.processDocument(contentBuffer, 'jira', false);

            // AI analizi için OpenAI servisini kullan
            const { analyzeDocument } = await import('./services/openai');
            const analysisResult = await analyzeDocument(analyzedContent);

            // Analiz sonuçlarını kaydet
            if (analysisResult) {
              console.log(`Jira issue ${issueKey} başarıyla AI ile analiz edildi`);

              // AI analiz sonuçlarını veritabanına kaydet
              await storage.saveAIAnalysis(document.id, analysisResult);

              // Bileşenleri kaydet
              if (analysisResult.components && analysisResult.components.length > 0) {
                for (const comp of analysisResult.components) {
                  await storage.createComponent({
                    documentId: document.id,
                    name: comp.name || 'Bilinmeyen Bileşen',
                    description: comp.description || null,
                    type: comp.type || null
                  });
                }
              }

              // Gereksinimleri kaydet
              if (analysisResult.requirements && analysisResult.requirements.length > 0) {
                for (const req of analysisResult.requirements) {
                  await storage.createRequirement({
                    documentId: document.id,
                    code: req.code || `REQ-${Date.now()}`,
                    description: req.description || req.title || 'Gereksinim açıklaması',
                    category: req.category || null
                  });
                }
              }

              // API endpoint'leri kaydet
              if (analysisResult.apiEndpoints && analysisResult.apiEndpoints.length > 0) {
                for (const endpoint of analysisResult.apiEndpoints) {
                  await storage.createApiEndpoint({
                    documentId: document.id,
                    url: endpoint.url || endpoint.path || '/api/unknown',
                    method: endpoint.method || 'GET',
                    parameters: endpoint.parameters || {},
                    requirementCode: endpoint.requirementCode || null
                  });
                }
              }
            }
          } catch (analysisError) {
            console.error(`Issue ${issueKey} analiz hatası:`, analysisError);
          }

        } catch (issueError) {
          console.error(`Issue ${issueKey} işleme hatası:`, issueError);
        }
      }

      if (analyzedDocuments.length === 0) {
        return res.status(400).json({
          success: false,
          message: "Hiçbir issue analiz edilemedi"
        });
      }

      res.json({
        success: true,
        message: `${analyzedDocuments.length} issue başarıyla analiz edildi`,
        documents: analyzedDocuments
      });

    } catch (error) {
      console.error("Jira issue analizi hatası:", error);
      res.status(500).json({
        success: false,
        message: `Analiz hatası: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`
      });
    }
  });

  // Sistem Ayarları API Endpoints
  app.get('/api/system-settings', protectRoute, async (req: Request, res: Response) => {
    try {
      const { category } = req.query;

      let settings;
      if (category && typeof category === 'string') {
        settings = await storage.getSystemSettingsByCategory(category);
      } else {
        settings = await storage.getSystemSettings();
      }

      res.json(settings);
    } catch (error) {
      console.error('Sistem ayarları getirme hatası:', error);
      res.status(500).json({ error: 'Sistem ayarları getirilemedi' });
    }
  });

  app.get('/api/system-settings/:category/:key', protectRoute, async (req: Request, res: Response) => {
    try {
      const { category, key } = req.params;
      const setting = await storage.getSystemSetting(category, key);

      if (!setting) {
        return res.status(404).json({ error: 'Ayar bulunamadı' });
      }

      res.json(setting);
    } catch (error) {
      console.error('Sistem ayarı getirme hatası:', error);
      res.status(500).json({ error: 'Sistem ayarı getirilemedi' });
    }
  });

  app.post('/api/system-settings', protectRoute, async (req: Request, res: Response) => {
    try {
      // Sadece admin veya api_settings izni olan kullanıcılar ayar oluşturabilir
      const user = req.user as any;
      const hasPermission = user.role === 'admin' || await storage.hasPermission(user.id, 'api_settings');

      if (!hasPermission) {
        return res.status(403).json({ error: 'Bu işlem için yetkiniz yok' });
      }

      const settingData = {
        ...req.body,
        createdBy: user.id
      };

      const setting = await storage.createSystemSetting(settingData);
      res.status(201).json(setting);
    } catch (error) {
      console.error('Sistem ayarı oluşturma hatası:', error);
      res.status(500).json({ error: 'Sistem ayarı oluşturulamadı' });
    }
  });

  app.put('/api/system-settings/:id', protectRoute, async (req: Request, res: Response) => {
    try {
      // Sadece admin veya api_settings izni olan kullanıcılar ayar güncelleyebilir
      const user = req.user as any;
      const hasPermission = user.role === 'admin' || await storage.hasPermission(user.id, 'api_settings');

      if (!hasPermission) {
        return res.status(403).json({ error: 'Bu işlem için yetkiniz yok' });
      }

      const { id } = req.params;
      const setting = await storage.updateSystemSetting(parseInt(id), req.body);

      if (!setting) {
        return res.status(404).json({ error: 'Ayar bulunamadı' });
      }

      res.json(setting);
    } catch (error) {
      console.error('Sistem ayarı güncelleme hatası:', error);
      res.status(500).json({ error: 'Sistem ayarı güncellenemedi' });
    }
  });

  app.delete('/api/system-settings/:id', protectRoute, async (req: Request, res: Response) => {
    try {
      // Sadece admin veya api_settings izni olan kullanıcılar ayar silebilir
      const user = req.user as any;
      const hasPermission = user.role === 'admin' || await storage.hasPermission(user.id, 'api_settings');

      if (!hasPermission) {
        return res.status(403).json({ error: 'Bu işlem için yetkiniz yok' });
      }

      const { id } = req.params;
      const success = await storage.deleteSystemSetting(parseInt(id));

      if (!success) {
        return res.status(404).json({ error: 'Ayar bulunamadı' });
      }

      res.json({ success: true, message: 'Sistem ayarı silindi' });
    } catch (error) {
      console.error('Sistem ayarı silme hatası:', error);
      res.status(500).json({ error: 'Sistem ayarı silinemedi' });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
