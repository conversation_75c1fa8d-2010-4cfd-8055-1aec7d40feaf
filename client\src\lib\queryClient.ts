import { QueryClient, QueryFunction } from "@tanstack/react-query";

// Enhanced error handling for API responses
async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    try {
      // Try to parse as JSON error first
      const contentType = res.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const errorData = await res.json();
        // Format a better error message from JSON response
        const errorMessage = errorData.message || errorData.error || JSON.stringify(errorData);
        throw new Error(`${res.status}: ${errorMessage}`);
      } else {
        // Fall back to text if not JSON
        const text = await res.text() || res.statusText;
        throw new Error(`${res.status}: ${text}`);
      }
    } catch (error) {
      // If we can't parse the error, just use the status text
      if (error instanceof Error && !error.message.includes(`${res.status}`)) {
        throw new Error(`${res.status}: ${res.statusText}`);
      }
      throw error;
    }
  }
}

// Maximum number of retries for transient errors
const MAX_RETRIES = 3;
// Base delay between retries in ms (will be multiplied by retry count)
const RETRY_DELAY = 500;

// Improved API request function with retry logic for network errors
export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
  retryCount = 0,
): Promise<Response> {
  try {
    const res = await fetch(url, {
      method,
      headers: data ? { "Content-Type": "application/json" } : {},
      body: data ? JSON.stringify(data) : undefined,
      credentials: "include",
      // Uzun sürebilen AI işlemleri için daha uzun timeout
      signal: AbortSignal.timeout(900000), // 15 dakika (900 saniye) timeout
    });

    // Only retry on specific status codes indicating transient errors
    if ([408, 429, 500, 502, 503, 504].includes(res.status) && retryCount < MAX_RETRIES) {
      // Exponential backoff with jitter
      const delay = RETRY_DELAY * Math.pow(2, retryCount) * (0.5 + Math.random() * 0.5);
      console.warn(`API request to ${url} failed with status ${res.status}, retrying in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
      return apiRequest(method, url, data, retryCount + 1);
    }

    await throwIfResNotOk(res);
    return res;
  } catch (error) {
    // Retry on network errors (like connection refused)
    if (error instanceof TypeError && retryCount < MAX_RETRIES) {
      const delay = RETRY_DELAY * Math.pow(2, retryCount) * (0.5 + Math.random() * 0.5);
      console.warn(`API request to ${url} failed with network error, retrying in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
      return apiRequest(method, url, data, retryCount + 1);
    }
    throw error;
  }
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    try {
      const res = await fetch(queryKey[0] as string, {
        credentials: "include",
        // Uzun sürebilen AI işlemleri için daha uzun timeout
        signal: AbortSignal.timeout(900000), // 15 dakika (900 saniye) timeout
      });

      if (unauthorizedBehavior === "returnNull" && res.status === 401) {
        return null;
      }

      await throwIfResNotOk(res);
      return await res.json();
    } catch (error) {
      console.error(`Query error for ${queryKey[0]}:`, error);
      throw error;
    }
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: 60000, // 1 minute (less aggressive caching)
      retry: 3, // Retry failed queries a few times
      retryDelay: attempt => Math.min(1000 * 2 ** attempt, 30000), // Exponential backoff
    },
    mutations: {
      retry: 2, // Also retry mutations
      retryDelay: attempt => Math.min(1000 * 2 ** attempt, 10000),
    },
  },
});
