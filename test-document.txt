## Doküman Analiz ve Test Senaryosu Çıkarma Ajanı (Genişletilmiş Tasarım)

### Temel Bileşenler (Güncel)

1. **Doküman İşleme Modülü**
   - PDF ve Word dosyalarını okuyabilme
   - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> metin, tab<PERSON><PERSON>, diyagramları tanıma
   - Doküman yapı<PERSON>ını (başlıklar, bölümler) anlama

2. **Bilgi Çıkarım Motoru**
   - <PERSON><PERSON><PERSON><PERSON><PERSON>, işlevsel özellikler ve kısıtlamaları tespit etme
   - Beklenen davranışları ve çıktıları anlama
   - Kritik iş akışlarını belirleme

3. **Hafıza Sistemi**
   - Önceki çıkarımları hatırlama ve ilişkilendirme
   - Doküman içindeki çapraz referansları takip etme
   - Bilgi haritası oluşturma ve güncelleme

4. **Senaryo Oluşturma Modülü**
   - Gereksinimleri test senaryolarına dönüştürme
   - Her senaryonun ön koşul, adım ve beklenen sonuçlarını oluşturma
   - Kapsamlı test senaryoları kütüphanesi oluşturma

5. **Öz Değerlendirme Sistemi**
   - Senaryoların gereksinimleri kapsama oranını hesaplama
   - Eksik veya belirsiz alanları tespit etme
   - İyileştirme önerileri sunma

6. **Gelişmiş AI Modül Entegrasyonu (YENİ)**
   - GPT-4o API entegrasyonu ile karmaşık bağlamları anlama ve işleme
   - Belirsiz gereksinimleri netleştirme ve yorumlama
   - Domaine özgü bilgileri ve jargonu anlama yeteneği

### API Entegrasyonu

Sistemin AI yeteneklerini geliştirmek için Azure OpenAI GPT-4o entegrasyonu:

```javascript
import OpenAI from "openai";

// Azure OpenAI yapılandırması
const openai = new OpenAI({
  apiKey: process.env.AZURE_OPENAI_API_KEY,
  baseURL: "https://netas-ai.openai.azure.com/openai/deployments/gpt-4o",
  defaultHeaders: {
    "api-key": process.env.AZURE_OPENAI_API_KEY,
  },
  defaultQuery: {
    "api-version": "2024-08-01-preview",
  },
});

// Doküman analizi fonksiyonu
async function analyzeDocument(content) {
  const response = await openai.chat.completions.create({
    model: "gpt-4o",
    messages: [
      { 
        role: "user", 
        content: `Lütfen aşağıdaki dokümanı analiz et ve şu bilgileri çıkar:
        1. Bileşenler ve modüller
        2. Gereksinimler
        3. API endpoint'leri
        4. Test senaryoları
        
        Doküman:
        ${content}`
      }
    ],
    response_format: { type: "json_object" },
    max_tokens: 1000
  });
  
  return response.choices[0].message.content;
}
```

### Gerçek Zamanlı İşleme Akışı Örneği

1. Test mühendisi bir API dokümantasyonu yükler
2. Sistem dokümanı işler ve temel bileşenleri çıkarır
3. GPT-4o entegrasyonu belirsiz noktaları tespit eder ve netleştirir
4. Sistem API endpointlerini, parametreleri ve yanıt yapılarını haritalar
5. AI, olası test senaryolarını oluşturur (mutlu yol, hata durumları, sınır koşulları)
6. Hafıza sistemi API entegrasyonları arasındaki bağımlılıkları izler
7. Öz değerlendirme sistemi kapsama analizini yapar
8. Test mühendisi senaryoları inceler ve geribildirim sağlar
9. AI geribildirimleri işleyerek hafıza sistemini ve test yaklaşımını geliştirir