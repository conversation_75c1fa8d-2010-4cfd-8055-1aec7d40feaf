
> rest-express@1.0.0 dev
10:53:59 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 6 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
10:54:02 PM [express] GET /api/ai-models 304 in 2ms :: {"models":{"o1":{"deploymentName":"netas-ai-o1…
10:54:02 PM [express] GET /api/documents 304 in 1ms :: [{"id":22,"name":"demo.docx","type":"docx","co…
10:54:07 PM [express] GET /api/ai-models 304 in 1ms :: {"models":{"o1":{"deploymentName":"netas-ai-o1…
10:54:07 PM [express] GET /api/documents 200 in 1ms :: [{"id":22,"name":"demo.docx","type":"docx","co…
AI Model changed to: o1 (GPT-o1)
10:55:17 PM [express] POST /api/ai-models/set 200 in 2ms :: {"success":true,"message":"AI modeli 'o1'…
OpenAI ile doküman analizi hatası: BadRequestError: 400 Unsupported parameter: 'max_tokens' is not supported with this model. Use 'max_completion_tokens' instead.
    at Function.generate (/home/<USER>/workspace/node_modules/openai/src/error.ts:72:14)
    at OpenAI.makeStatusError (/home/<USER>/workspace/node_modules/openai/src/core.ts:462:21)
    at OpenAI.makeRequest (/home/<USER>/workspace/node_modules/openai/src/core.ts:526:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Object.analyzeDocument (/home/<USER>/workspace/server/services/openai.ts:170:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:87:34) {
  status: 400,
  headers: {
    'apim-request-id': '4ee22883-5bbe-4206-af95-01f3dbbcc806',
    'azureml-model-session': 'v20250319-1-164616836',
    'content-length': '245',
    'content-type': 'application/json',
    date: 'Mon, 07 Apr 2025 22:55:26 GMT',
    'ms-azureml-model-error-reason': 'model_error',
    'ms-azureml-model-error-statuscode': '400',
    'ms-azureml-model-time': '15',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    'x-aml-cluster': 'hyena-northcentralus-01',
    'x-content-type-options': 'nosniff',
    'x-envoy-upstream-service-time': '17',
    'x-ms-client-request-id': '4ee22883-5bbe-4206-af95-01f3dbbcc806',
    'x-ms-rai-invoked': 'true',
    'x-ms-region': 'East US',
    'x-ratelimit-limit-requests': '100',
    'x-ratelimit-limit-tokens': '600000',
    'x-ratelimit-remaining-requests': '99',
    'x-ratelimit-remaining-tokens': '595904',
    'x-request-id': 'f3d77d9f-dbee-42aa-a1f3-e583d2283341'
  },
  request_id: 'f3d77d9f-dbee-42aa-a1f3-e583d2283341',
  error: {
    message: "Unsupported parameter: 'max_tokens' is not supported with this model. Use 'max_completion_tokens' instead.",
    type: 'invalid_request_error',
    param: 'max_tokens',
    code: 'unsupported_parameter'
  },
  code: 'unsupported_parameter',
  param: 'max_tokens',
  type: 'invalid_request_error'
}
Doküman analiz hatası: Error: Doküman analizi başarısız oldu: 400 Unsupported parameter: 'max_tokens' is not supported with this model. Use 'max_completion_tokens' instead.
    at Object.analyzeDocument (/home/<USER>/workspace/server/services/openai.ts:186:11)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:87:34)
10:55:26 PM [express] POST /api/documents/upload 201 in 3156ms :: {"document":{"id":23,"name":"demo.d…
10:55:26 PM [express] GET /api/documents 200 in 1ms :: [{"id":23,"name":"demo.docx","type":"docx","co…