1:05:49 PM [express] GET /api/ai-models 304 in 23ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
1:05:49 PM [express] GET /api/documents 304 in 190ms :: [{"id":21,"name":"<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>.…
1:06:11 PM [express] GET /api/ai-models 304 in 24ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
Kullanıcı projeleri getirme hatası: TypeError: storage.getUserProjects is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:494:42)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
İzinleri getirme hatası: TypeError: storage.getUserPermissions is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:120:45)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1:06:11 PM [express] GET /api/user-projects 500 in 25ms :: {"error":"Kullanıcı projeleri getirilirke…
1:06:11 PM [express] GET /api/permissions 500 in 24ms :: {"error":"İzinler getirilirken bir hata olu…
1:06:11 PM [express] GET /api/documents 304 in 189ms :: [{"id":21,"name":"TrendKart_Analiz_Dokumani.…
Kullanıcı projeleri getirme hatası: TypeError: storage.getUserProjects is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:494:42)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1:06:13 PM [express] GET /api/user-projects 500 in 25ms :: {"error":"Kullanıcı projeleri getirilirke…
İzinleri getirme hatası: TypeError: storage.getUserPermissions is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:120:45)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1:06:13 PM [express] GET /api/permissions 500 in 26ms :: {"error":"İzinler getirilirken bir hata olu…
Kullanıcı projeleri getirme hatası: TypeError: storage.getUserProjects is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:494:42)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1:06:15 PM [express] GET /api/user-projects 500 in 22ms :: {"error":"Kullanıcı projeleri getirilirke…
İzinleri getirme hatası: TypeError: storage.getUserPermissions is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:120:45)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1:06:15 PM [express] GET /api/permissions 500 in 22ms :: {"error":"İzinler getirilirken bir hata olu…
İzinleri getirme hatası: TypeError: storage.getUserPermissions is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:120:45)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1:06:19 PM [express] GET /api/permissions 500 in 22ms :: {"error":"İzinler getirilirken bir hata olu…
Kullanıcı projeleri getirme hatası: TypeError: storage.getUserProjects is not a function
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:494:42)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at protectRoute (/home/<USER>/workspace/server/routes.ts:50:14)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at strategy.pass (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:355:9)
    at /home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:120:12
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:352:31)
    at deserialized (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:364:7)
    at <anonymous> (/home/<USER>/workspace/server/auth.ts:74:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1:06:19 PM [express] GET /api/user-projects 500 in 25ms :: {"error":"Kullanıcı projeleri getirilirke…
