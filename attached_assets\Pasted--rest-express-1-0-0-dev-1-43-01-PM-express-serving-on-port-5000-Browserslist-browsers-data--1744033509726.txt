
> rest-express@1.0.0 dev
1:43:01 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 6 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
1:43:04 PM [express] GET /api/documents 304 in 3ms :: [{"id":3,"name":"demo.docx","type":"docx","con…
1:43:08 PM [express] GET /api/documents 200 in 2ms :: [{"id":3,"name":"demo.docx","type":"docx","con…
1:43:54 PM [express] GET /api/documents/3/ai-analysis 304 in 1ms :: {"id":2,"document_id":3,"observa…
1:43:54 PM [express] GET /api/documents/3/requirements 304 in 1ms :: [{"id":10,"document_id":3,"code…
1:43:54 PM [express] GET /api/documents/3/api-endpoints 304 in 1ms :: [{"id":2,"document_id":3,"url"…
1:43:54 PM [express] GET /api/documents/3 304 in 1ms :: {"id":3,"name":"demo.docx","type":"docx","co…
1:43:54 PM [express] GET /api/documents/3/components 304 in 1ms :: [{"id":4,"document_id":3,"name":"…
1:43:55 PM [express] GET /api/documents/3/test-scenarios 304 in 1ms :: []
Test senaryosu ekleniyor (düzeltilmiş): {
  documentId: 3,
  title: 'REQ-001 Ses Dosyasının Görüntülenmesi ve Dinletilmesi',
  preconditions: 'Kaptan bir rota için bir ses dosyası yüklemiş olmalı. Kullanıcı uygulamaya giriş yapmış olmalı.',
  steps: '["1. Uygulamada sesli içerik sekmesine git.","2. İlgili rotayı seç.","3. Görüntülenen ses dosyasına tıkla.","4. Play butonuna bas."]',
  expectedResults: 'Uygulama ses dosyasını oynatmalı ve kullanıcı dosyayı dinlemeli.',
  requirementCode: 'REQ-001'
}
Test senaryosu ekleniyor (düzeltilmiş): {
  documentId: 3,
  title: 'REQ-002 Voice Readout Menüsünde Uçuş Bilgilerinin Gösterilmesi',
  preconditions: 'Mobil cihaz ve internet bağlantısı aktif olmalı. Uygulama açılmış olmalı.',
  steps: '["1. Uygulamada Voice Readout menüsüne git.","2. Gösterilen uçuş bilgilerini kontrol et."]',
  expectedResults: 'Voice Readout menüsünde doğru uçuş bilgileri görüntülenir.',
  requirementCode: 'REQ-002'
}
Test senaryosu ekleniyor (düzeltilmiş): {
  documentId: 3,
  title: 'REQ-003 Offline Modda İndirilen Verileri Görüntüleme',
  preconditions: 'Kullanıcı önceden veri indirmiş olmalı. Cihaz internet bağlantısı kapalı.',
  steps: '["1. Uygulamayı offline moda geç.","2. Önceden indirilen verilerin görüntülendiğini kontrol et."]',
  expectedResults: 'Offline modda sadece indirilen veriler görüntülenir.',
  requirementCode: 'REQ-003'
}
Test senaryosu ekleniyor (düzeltilmiş): {
  documentId: 3,
  title: 'REQ-004 Servis Performansı Testi',
  preconditions: 'Uygulama internet bağlantısı olan bir cihazda çalıştırılıyor.',
  steps: '["1. Servis isteği gönder.","2. Servis cevabını ve datanın ekrana yazılma hızını ölç.","3. Hızın 1 ila 10 saniye arasında olup olmadığını kontrol et."]',
  expectedResults: 'Ekrana yazılma süresi 1-10 saniye arasında olmalı.',
  requirementCode: 'REQ-004'
}
Test senaryosu ekleniyor (düzeltilmiş): {
  documentId: 3,
  title: 'REQ-005 Güvenlik Taramalarının Kontrol Edilmesi',
  preconditions: 'Güvenlik tarama raporu mevcut olmalı.',
  steps: '["1. Güvenlik tarama raporunu aç.","2. Raporu incele ve major bulgu olup olmadığını kontrol et.","3. Major bulgu varsa çözülme durumunu takip et."]',
  expectedResults: 'Major bulgu varsa çözülmeden canlı geçiş olmamalıdır.',
  requirementCode: 'REQ-005'
}
1:44:23 PM [express] POST /api/documents/3/generate-test-scenarios 200 in 26537ms :: {"scenarios":[{…
1:44:24 PM [express] GET /api/documents/3/test-scenarios 200 in 1ms :: [{"id":1,"document_id":3,"tit…