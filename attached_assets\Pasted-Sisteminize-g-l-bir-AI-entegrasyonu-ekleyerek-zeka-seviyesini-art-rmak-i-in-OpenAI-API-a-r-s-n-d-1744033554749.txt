Sisteminize güçlü bir AI entegrasyonu ekleyerek zeka seviyesini artırmak için OpenAI API çağrısını dahil ediyorum. Bu, doküman analiz ajanınızın yeteneklerini önemli ölçüde geliştirecektir.

## Doküman Analiz ve Test Senaryosu Çıkarma Ajanı (Genişletilmiş Tasarım)

### Temel Bileşenler (Güncel)

1. **Doküman İşleme Modülü**
   - PDF ve Word dosyalarını okuyabilme
   - İçerikteki metin, tablolar, diyagramları tanıma
   - Doküman yapı<PERSON>ını (başlıklar, bölümler) anlama

2. **Bilgi Çıkarım Motoru**
   - Gereksinimler, işlevsel özellikler ve kısıtlamaları tespit etme
   - Beklenen davranışları ve çıktıları anlama
   - Kritik iş akışlarını belirleme

3. **Ha<PERSON><PERSON>za Sistemi**
   - Önceki çıkarımları hatırlama ve ilişkilendirme
   - Doküman içindeki çapraz referansları takip etme
   - Bilgi haritası oluşturma ve güncelleme

4. **Senaryo Oluşturma Modülü**
   - Gereksinimleri test senaryolarına dönüştürme
   - Her senaryonun ön koşul, adım ve beklenen sonuçlarını oluşturma
   - Kapsamlı test senaryoları kütüphanesi oluşturma

5. **Öz Değerlendirme Sistemi**
   - Senaryoların gereksinimleri kapsama oranını hesaplama
   - Eksik veya belirsiz alanları tespit etme
   - İyileştirme önerileri sunma

6. **Gelişmiş AI Modül Entegrasyonu (YENİ)**
   - GPT-4o API entegrasyonu ile karmaşık bağlamları anlama ve işleme
   - Belirsiz gereksinimleri netleştirme ve yorumlama
   - Domaine özgü bilgileri ve jargonu anlama yeteneği

### API Entegrasyonu

Sistemin AI yeteneklerini geliştirmek için Azure OpenAI GPT-4o entegrasyonu:

```python
import requests
import json

def query_advanced_ai(content, api_key):
    url = "https://netas-ai.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview"
    headers = {
        "Content-Type": "application/json",
        "api-key": api_key
    }
    
    data = {
        "messages": [{"role": "user", "content": content}],
        "max_tokens": 1000  # Daha uzun yanıtlar için artırıldı
    }
    
    response = requests.post(url, headers=headers, data=json.dumps(data))
    return response.json()
```

### Güncellenmiş Sistem Mimarisi

Özür dilerim, diagram oluşturma aracında bir sorun oluştu. Bunun yerine, mimarinin metin tanımını vereyim:

### Güncellenmiş Sistem Mimarisi (Metin Açıklaması)

- **Doküman Giriş** → **Doküman İşleme Modülü** → **Yapısal Analiz** ve **İçerik Çıkarımı**
- **Yapısal Analiz** ve **İçerik Çıkarımı** → **Bilgi Çıkarım Motoru**
- **Bilgi Çıkarım Motoru** ↔ **Gelişmiş AI Modülü (GPT-4o)** (çift yönlü etkileşim)
- **Bilgi Çıkarım Motoru** → **Gereksinim Tespiti**, **İlişkilerin Belirlenmesi**, **Kritik Noktaların Tespiti**
- Tüm çıkarımlar → **Hafıza Sistemi** ↔ **Gelişmiş AI Modülü** (çift yönlü etkileşim)
- **Hafıza Sistemi** → **Bilgi Haritası** → **Senaryo Oluşturma Modülü** ↔ **Gelişmiş AI Modülü**
- **Senaryo Oluşturma Modülü** → **Test Senaryoları** → **Öz Değerlendirme Sistemi** ↔ **Gelişmiş AI Modülü**
- **Öz Değerlendirme Sistemi** → **Kapsam Analizi** ve **Tutarlılık Kontrolü** → **Nihai Test Senaryoları**
- **Nihai Test Senaryoları** → **Test Mühendisi İncelemesi** → **Geribildirim**
- **Geribildirim** → **Hafıza Sistemi**, **Senaryo Oluşturma Modülü** ve **Gelişmiş AI Modülü**

### AI Entegrasyonunun Avantajları

1. **Gelişmiş Doğal Dil Anlama**
   - Karmaşık ve teknik dokümanları daha iyi anlama
   - Örtük gereksinimleri ve bağlamsal ilişkileri tespit etme

2. **Belirsizliklerin Giderilmesi**
   - Muğlak ifadeleri tanıma ve netleştirme önerileri sunma
   - Eksik bilgileri fark edip tamamlama

3. **Çok Yönlü Test Yaklaşımı**
   - Farklı test stratejileri önerme (sınır değer analizi, durum tabanlı, vb.)
   - Edge case'leri ve özel durumları tespit etme

4. **Sürekli Kendini Geliştirme**
   - Geribildirimlerden öğrenerek senaryo kalitesini sürekli artırma
   - Sektöre ve uygulamaya özel bilgi birikimi oluşturma

### Gerçek Zamanlı İşleme Akışı Örneği

1. Test mühendisi bir API dokümantasyonu yükler
2. Sistem dokümanı işler ve temel bileşenleri çıkarır
3. GPT-4o entegrasyonu belirsiz noktaları tespit eder ve netleştirir
4. Sistem API endpointlerini, parametreleri ve yanıt yapılarını haritalar
5. AI, olası test senaryolarını oluşturur (mutlu yol, hata durumları, sınır koşulları)
6. Hafıza sistemi API entegrasyonları arasındaki bağımlılıkları izler
7. Öz değerlendirme sistemi kapsama analizini yapar
8. Test mühendisi senaryoları inceler ve geribildirim sağlar
9. AI geribildirimleri işleyerek hafıza sistemini ve test yaklaşımını geliştirir

Bu yapı sayesinde, sistem giderek daha akıllı hale gelecek ve tekrarlayan dokümanlarda bile yeni ve önemli test noktalarını tespit edebilecektir. API anahtarı güvenliği için, anahtarı sistem içinde güvenli bir şekilde saklamak ve çevresel değişkenler veya güvenli bir depo kullanmak önerilir.