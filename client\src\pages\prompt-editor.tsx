import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { fetchPrompts, updatePrompt } from "@/lib/api";
import { Spinner } from "@/components/ui/spinner";

type Prompt = {
  key: string;
  value: string;
  title?: string;
  description?: string;
};

// Prompt başlıkları ve açıklamaları
const promptMetadata: Record<string, { title: string; description: string }> = {
  DOCUMENT_ANALYSIS_PROMPT: {
    title: "Doküman Analizi",
    description: "Dokümanları analiz ederken kullanılan prompt. Gereksinimler, API uç noktaları ve bileşenleri çıkarmak için kullanılır."
  },
  TEST_SCENARIOS_GENERATION_PROMPT: {
    title: "Test Senaryosu Oluşturma",
    description: "Gereksinimlerden test senaryoları oluşturmak için kullanılan prompt."
  },
  TEST_SCENARIO_VALIDATION_PROMPT: {
    title: "Test Senaryosu Doğrulama",
    description: "Test senaryolarının doğruluğunu ve kalitesini değerlendirmek için kullanılan prompt."
  },
  DOCUMENT_COVERAGE_VALIDATION_PROMPT: {
    title: "Doküman Kapsama Değerlendirmesi",
    description: "Test senaryolarının gereksinimleri ne kadar iyi kapsadığını değerlendirmek için kullanılan prompt."
  },
  AI_ASSISTANT_PROMPT: {
    title: "AI Asistanı",
    description: "Kullanıcı sorularını yanıtlamak için kullanılan prompt."
  }
};

export default function PromptEditorPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [selectedPromptKey, setSelectedPromptKey] = useState<string | null>(null);
  const [editedValue, setEditedValue] = useState<string>("");
  const [isEdited, setIsEdited] = useState(false);
  const [previewMode, setPreviewMode] = useState<"raw" | "formatted">("formatted");

  // Promptları getir
  const { data: prompts, isLoading, error } = useQuery({
    queryKey: ["/api/prompts"],
    select: (data: any): Prompt[] => {
      return data.map((prompt: Prompt) => ({
        ...prompt,
        title: promptMetadata[prompt.key]?.title || prompt.key,
        description: promptMetadata[prompt.key]?.description || "Açıklama yok"
      }));
    }
  });

  // Promptu güncelle
  const updatePromptMutation = useMutation({
    mutationFn: (promptData: { key: string; value: string }) => {
      return updatePrompt(promptData.key, promptData.value);
    },
    onSuccess: () => {
      toast({
        title: "Prompt güncellendi",
        description: "Prompt başarıyla güncellendi ve sisteme kaydedildi.",
      });
      
      // Promptlar cache'ini yenile
      queryClient.invalidateQueries({ queryKey: ["/api/prompts"] });
      setIsEdited(false);
    },
    onError: (error) => {
      toast({
        title: "Hata",
        description: `Prompt güncellenemedi: ${error instanceof Error ? error.message : "Bilinmeyen hata"}`,
        variant: "destructive"
      });
    }
  });

  // Seçili prompt değiştiğinde değerini düzenleme kutucuğuna yükle
  useEffect(() => {
    if (selectedPromptKey && prompts) {
      const selectedPrompt = prompts.find(p => p.key === selectedPromptKey);
      if (selectedPrompt) {
        setEditedValue(selectedPrompt.value);
        setIsEdited(false);
      }
    }
  }, [selectedPromptKey, prompts]);

  // Sayfa yüklendiğinde ilk promptu seç
  useEffect(() => {
    if (prompts && prompts.length > 0 && !selectedPromptKey) {
      setSelectedPromptKey(prompts[0].key);
    }
  }, [prompts, selectedPromptKey]);

  // Prompt değerini güncelle
  const handleSave = () => {
    if (selectedPromptKey) {
      updatePromptMutation.mutate({
        key: selectedPromptKey,
        value: editedValue
      });
    }
  };

  // Prompt değişikliklerinden vazgeç
  const handleCancel = () => {
    if (selectedPromptKey && prompts) {
      const selectedPrompt = prompts.find(p => p.key === selectedPromptKey);
      if (selectedPrompt) {
        setEditedValue(selectedPrompt.value);
        setIsEdited(false);
      }
    }
  };

  // Değişken yer tutucuları için renklendirme
  const formatPromptText = (text: string) => {
    if (!text) return "";
    
    // {{değişken}} formatındaki metinleri renklendir
    return text.replace(/\{\{([^}]+)\}\}/g, '<span style="color: #3182ce; font-weight: bold;">{{$1}}</span>');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Spinner size="lg" />
        <span className="ml-2">Prompt'lar yükleniyor...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className="max-w-2xl mx-auto mt-8">
        <AlertDescription>
          Prompt'lar yüklenirken bir hata oluştu: {error instanceof Error ? error.message : "Bilinmeyen hata"}
        </AlertDescription>
      </Alert>
    );
  }

  const selectedPrompt = selectedPromptKey ? prompts?.find(p => p.key === selectedPromptKey) : null;

  return (
    <div className="container max-w-7xl py-8">
      <div className="flex flex-col mb-10">
        <h1 className="text-4xl font-bold text-center bg-gradient-to-r from-indigo-600 via-purple-600 to-violet-600 text-transparent bg-clip-text">
          AI Prompt Yöneticisi
        </h1>
        <p className="text-center text-gray-500 mt-3 max-w-2xl mx-auto">
          Sistem performansını ve çıktı kalitesini artırmak için AI modelinin davranışını belirleyen promptları özelleştirin
        </p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
        <div className="lg:col-span-3">
          <div className="sticky top-5">
            <Card className="shadow-md border-gray-200/60 overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-slate-50 to-gray-50 border-b pb-4">
                <CardTitle className="text-lg flex items-center">
                  <i className="fas fa-book-open mr-2 text-indigo-500"></i>
                  <span>Prompt Kütüphanesi</span>
                </CardTitle>
                <CardDescription>Özelleştirmek istediğiniz AI promptunu seçin</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="">
                  {prompts?.map((prompt) => (
                    <Button
                      key={prompt.key}
                      variant="ghost"
                      className={`w-full rounded-none justify-start text-left h-auto py-3 px-4 border-l-2 ${
                        selectedPromptKey === prompt.key 
                          ? 'bg-indigo-50/80 border-l-indigo-600 text-indigo-700 font-medium' 
                          : 'border-l-transparent hover:bg-slate-50 hover:border-l-slate-300'
                      }`}
                      onClick={() => setSelectedPromptKey(prompt.key)}
                    >
                      <div className="flex flex-col items-start">
                        <span className="text-sm font-medium">{prompt.title || prompt.key}</span>
                        <span className="text-xs text-slate-500 mt-1 line-clamp-2">{prompt.description}</span>
                      </div>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        
        <div className="lg:col-span-9">
          {selectedPrompt && (
            <Card className="shadow-md border-gray-200/60">
              <CardHeader className="bg-gradient-to-r from-slate-50 to-gray-50 border-b">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-xl text-indigo-800 flex items-center">
                      {selectedPrompt.key === 'DOCUMENT_ANALYSIS_PROMPT' && <i className="fas fa-file-alt mr-2 text-indigo-500"></i>}
                      {selectedPrompt.key === 'TEST_SCENARIOS_GENERATION_PROMPT' && <i className="fas fa-tasks mr-2 text-green-500"></i>}
                      {selectedPrompt.key === 'TEST_SCENARIO_VALIDATION_PROMPT' && <i className="fas fa-check-square mr-2 text-amber-500"></i>}
                      {selectedPrompt.key === 'DOCUMENT_COVERAGE_VALIDATION_PROMPT' && <i className="fas fa-chart-pie mr-2 text-red-500"></i>}
                      {selectedPrompt.key === 'AI_ASSISTANT_PROMPT' && <i className="fas fa-robot mr-2 text-purple-500"></i>}
                      {selectedPrompt.title}
                    </CardTitle>
                    <CardDescription className="mt-1">{selectedPrompt.description}</CardDescription>
                  </div>
                  <Tabs value={previewMode} onValueChange={(v) => setPreviewMode(v as any)}>
                    <TabsList className="bg-white">
                      <TabsTrigger value="formatted" className="data-[state=active]:bg-indigo-50 data-[state=active]:text-indigo-700">
                        <i className="fas fa-eye mr-1.5"></i>Ön İzleme
                      </TabsTrigger>
                      <TabsTrigger value="raw" className="data-[state=active]:bg-indigo-50 data-[state=active]:text-indigo-700">
                        <i className="fas fa-code mr-1.5"></i>Düzenleyici
                      </TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="space-y-0">
                  {previewMode === "raw" ? (
                    <div className="relative">
                      <div className="absolute top-3 right-3 z-10 bg-white/90 rounded px-2 py-1 text-xs font-medium">
                        <i className="fas fa-lightbulb text-amber-500 mr-1"></i>
                        <span>İpucu: <span className="text-indigo-600">{"{{değişkenAdı}}"}</span> formatında template değişkenleri kullanabilirsiniz</span>
                      </div>
                      <Textarea
                        value={editedValue}
                        onChange={(e) => {
                          setEditedValue(e.target.value);
                          setIsEdited(true);
                        }}
                        className="font-mono text-sm p-5 min-h-[65vh] resize-none border-0 focus-visible:ring-0 focus-visible:ring-offset-0 rounded-none"
                      />
                    </div>
                  ) : (
                    <div className="bg-white border-0 p-6 min-h-[65vh]">
                      <div className="bg-slate-50 rounded-lg border border-slate-200 p-5 prose prose-indigo max-w-none">
                        <div 
                          className="whitespace-pre-wrap" 
                          dangerouslySetInnerHTML={{ __html: formatPromptText(editedValue) }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
              <div className="border-t p-4 bg-gray-50 flex items-center justify-between">
                <div className="flex items-center">
                  <i className="fas fa-info-circle text-indigo-500 mr-2"></i>
                  <span className="text-sm text-slate-600">Değişiklikler uygulandığında sistem davranışı hemen güncellenir</span>
                </div>
                <div className="flex gap-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCancel}
                    disabled={!isEdited || updatePromptMutation.isPending}
                    className="min-w-[100px]"
                  >
                    <i className="fas fa-undo mr-1.5"></i>
                    Vazgeç
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSave}
                    disabled={!isEdited || updatePromptMutation.isPending}
                    className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white min-w-[100px]"
                  >
                    {updatePromptMutation.isPending ? (
                      <>
                        <Spinner className="mr-2" size="sm" />
                        Kaydediliyor...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-check mr-1.5"></i>
                        Kaydet
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}