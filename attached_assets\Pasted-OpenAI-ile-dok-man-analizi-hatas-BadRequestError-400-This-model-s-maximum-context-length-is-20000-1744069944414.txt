OpenAI ile doküman analizi hatası: BadRequestError: 400 This model's maximum context length is 200000 tokens. However, you requested 204903 tokens (4903 in the messages, 200000 in the completion). Please reduce the length of the messages or completion.
    at Function.generate (/home/<USER>/workspace/node_modules/openai/src/error.ts:72:14)
    at OpenAI.makeStatusError (/home/<USER>/workspace/node_modules/openai/src/core.ts:462:21)
    at OpenAI.makeRequest (/home/<USER>/workspace/node_modules/openai/src/core.ts:526:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Object.analyzeDocument (/home/<USER>/workspace/server/services/openai.ts:235:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:87:34) {
  status: 400,
  headers: {
    'apim-request-id': '14e403e4-c032-4f55-a4d7-8f594f9efc4d',
    'azureml-model-session': 'v20250319-1-164616836',
    'content-length': '335',
    'content-type': 'application/json',
    date: 'Mon, 07 Apr 2025 23:51:45 GMT',
    'ms-azureml-model-error-reason': 'model_error',
    'ms-azureml-model-error-statuscode': '400',
    'ms-azureml-model-time': '37',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    'x-aml-cluster': 'hyena-northcentralus-02',
    'x-content-type-options': 'nosniff',
    'x-envoy-upstream-service-time': '40',
    'x-ms-client-request-id': '14e403e4-c032-4f55-a4d7-8f594f9efc4d',
    'x-ms-rai-invoked': 'true',
    'x-ms-region': 'East US',
    'x-ratelimit-limit-requests': '100',
    'x-ratelimit-limit-tokens': '600000',
    'x-ratelimit-remaining-requests': '99',
    'x-ratelimit-remaining-tokens': '595904',
    'x-request-id': '0535e0b9-2893-4f50-8841-196da6a91c31'
  },
  request_id: '0535e0b9-2893-4f50-8841-196da6a91c31',
  error: {
    message: "This model's maximum context length is 200000 tokens. However, you requested 204903 tokens (4903 in the messages, 200000 in the completion). Please reduce the length of the messages or completion.",
    type: 'invalid_request_error',
    param: 'messages',
    code: 'context_length_exceeded'
  },
  code: 'context_length_exceeded',
  param: 'messages',
  type: 'invalid_request_error'
}
Doküman analiz hatası: Error: Doküman analizi başarısız oldu: 400 This model's maximum context length is 200000 tokens. However, you requested 204903 tokens (4903 in the messages, 200000 in the completion). Please reduce the length of the messages or completion.
    at Object.analyzeDocument (/home/<USER>/workspace/server/services/openai.ts:245:11)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:87:34)