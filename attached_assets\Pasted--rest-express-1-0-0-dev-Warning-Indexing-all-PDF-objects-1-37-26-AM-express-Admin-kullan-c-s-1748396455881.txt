
> rest-express@1.0.0 dev
Warning: Indexing all PDF objects
1:37:26 AM [express] <PERSON><PERSON> kull<PERSON>ıcı<PERSON>ı zaten mevcut: netas
1:37:26 AM [express] serving on port 5000
1:37:49 AM [express] GET /api/permissions 304 in 53ms :: ["user_management","project_management","do…
1:37:50 AM [express] GET /api/user-projects 304 in 50ms :: [{"userId":1,"projectId":1,"role":"owner"…
1:37:50 AM [express] GET /api/ai-models 304 in 26ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
1:37:51 AM [express] GET /api/documents 304 in 1714ms :: [{"id":80,"name":"ABC Bank KampanyalÄ± Bire…
1:39:06 AM [express] GET /api/documents 304 in 445ms :: [{"id":80,"name":"ABC Bank KampanyalÄ± Birey…
1:39:49 AM [express] POST /api/integrations/jira/test 200 in 359ms :: {"success":true,"message":"Bağ…
1:39:49 AM [express] POST /api/integrations/jira/projects 200 in 316ms :: {"success":true,"projects"…
1:39:56 AM [express] POST /api/integrations/jira/issues 200 in 559ms :: {"success":true,"issues":[{"…
1:40:11 AM [express] POST /api/integrations/jira/issues 200 in 461ms :: {"success":true,"issues":[{"…
createDocument işlemi sırasında hata: error: null value in column "name" of relation "documents" violates not-null constraint
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:196:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:2057:28) {
  length: 324,
  severity: 'ERROR',
  code: '23502',
  detail: 'Failing row contains (102, null, null, Issue: SCRUM-5\n' +
    '\n' +
    'Başlık: vector db\n' +
    '\n' +
    'Tip: Task\n' +
    'Durum: To Do\n' +
    'Önc..., null, null, 2025-05-28 01:40:16.503, 1, null).',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'documents',
  column: 'name',
  dataType: undefined,
  constraint: undefined,
  file: 'execMain.c',
  line: '2006',
  routine: 'ExecConstraints'
}
Issue SCRUM-5 işleme hatası: error: null value in column "name" of relation "documents" violates not-null constraint
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:196:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:2057:28) {
  length: 324,
  severity: 'ERROR',
  code: '23502',
  detail: 'Failing row contains (102, null, null, Issue: SCRUM-5\n' +
    '\n' +
    'Başlık: vector db\n' +
    '\n' +
    'Tip: Task\n' +
    'Durum: To Do\n' +
    'Önc..., null, null, 2025-05-28 01:40:16.503, 1, null).',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'documents',
  column: 'name',
  dataType: undefined,
  constraint: undefined,
  file: 'execMain.c',
  line: '2006',
  routine: 'ExecConstraints'
}
createDocument işlemi sırasında hata: error: null value in column "name" of relation "documents" violates not-null constraint
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:196:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:2057:28) {
  length: 324,
  severity: 'ERROR',
  code: '23502',
  detail: 'Failing row contains (103, null, null, Issue: SCRUM-4\n' +
    '\n' +
    'Başlık: agentic memory\n' +
    '\n' +
    'Tip: Task\n' +
    'Durum: To Do..., null, null, 2025-05-28 01:40:16.889, 1, null).',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'documents',
  column: 'name',
  dataType: undefined,
  constraint: undefined,
  file: 'execMain.c',
  line: '2006',
  routine: 'ExecConstraints'
}
Issue SCRUM-4 işleme hatası: error: null value in column "name" of relation "documents" violates not-null constraint
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:196:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:2057:28) {
  length: 324,
  severity: 'ERROR',
  code: '23502',
  detail: 'Failing row contains (103, null, null, Issue: SCRUM-4\n' +
    '\n' +
    'Başlık: agentic memory\n' +
    '\n' +
    'Tip: Task\n' +
    'Durum: To Do..., null, null, 2025-05-28 01:40:16.889, 1, null).',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'documents',
  column: 'name',
  dataType: undefined,
  constraint: undefined,
  file: 'execMain.c',
  line: '2006',
  routine: 'ExecConstraints'
}
createDocument işlemi sırasında hata: error: null value in column "name" of relation "documents" violates not-null constraint
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:196:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:2057:28) {
  length: 323,
  severity: 'ERROR',
  code: '23502',
  detail: 'Failing row contains (104, null, null, Issue: SCRUM-3\n' +
    '\n' +
    'Başlık: agentic ui\n' +
    '\n' +
    'Tip: Task\n' +
    'Durum: To Do\n' +
    'Ön..., null, null, 2025-05-28 01:40:19.29, 1, null).',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'documents',
  column: 'name',
  dataType: undefined,
  constraint: undefined,
  file: 'execMain.c',
  line: '2006',
  routine: 'ExecConstraints'
}
Issue SCRUM-3 işleme hatası: error: null value in column "name" of relation "documents" violates not-null constraint
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:196:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:2057:28) {
  length: 323,
  severity: 'ERROR',
  code: '23502',
  detail: 'Failing row contains (104, null, null, Issue: SCRUM-3\n' +
    '\n' +
    'Başlık: agentic ui\n' +
    '\n' +
    'Tip: Task\n' +
    'Durum: To Do\n' +
    'Ön..., null, null, 2025-05-28 01:40:19.29, 1, null).',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'documents',
  column: 'name',
  dataType: undefined,
  constraint: undefined,
  file: 'execMain.c',
  line: '2006',
  routine: 'ExecConstraints'
}
1:40:19 AM [express] POST /api/integrations/jira/analyze 400 in 3458ms :: {"success":false,"message"…
createDocument işlemi sırasında hata: error: null value in column "name" of relation "documents" violates not-null constraint
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:196:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:2057:28) {
  length: 324,
  severity: 'ERROR',
  code: '23502',
  detail: 'Failing row contains (105, null, null, Issue: SCRUM-5\n' +
    '\n' +
    'Başlık: vector db\n' +
    '\n' +
    'Tip: Task\n' +
    'Durum: To Do\n' +
    'Önc..., null, null, 2025-05-28 01:40:21.169, 1, null).',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'documents',
  column: 'name',
  dataType: undefined,
  constraint: undefined,
  file: 'execMain.c',
  line: '2006',
  routine: 'ExecConstraints'
}
Issue SCRUM-5 işleme hatası: error: null value in column "name" of relation "documents" violates not-null constraint
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:196:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:2057:28) {
  length: 324,
  severity: 'ERROR',
  code: '23502',
  detail: 'Failing row contains (105, null, null, Issue: SCRUM-5\n' +
    '\n' +
    'Başlık: vector db\n' +
    '\n' +
    'Tip: Task\n' +
    'Durum: To Do\n' +
    'Önc..., null, null, 2025-05-28 01:40:21.169, 1, null).',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'documents',
  column: 'name',
  dataType: undefined,
  constraint: undefined,
  file: 'execMain.c',
  line: '2006',
  routine: 'ExecConstraints'
}
createDocument işlemi sırasında hata: error: null value in column "name" of relation "documents" violates not-null constraint
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:196:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:2057:28) {
  length: 324,
  severity: 'ERROR',
  code: '23502',
  detail: 'Failing row contains (106, null, null, Issue: SCRUM-4\n' +
    '\n' +
    'Başlık: agentic memory\n' +
    '\n' +
    'Tip: Task\n' +
    'Durum: To Do..., null, null, 2025-05-28 01:40:21.461, 1, null).',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'documents',
  column: 'name',
  dataType: undefined,
  constraint: undefined,
  file: 'execMain.c',
  line: '2006',
  routine: 'ExecConstraints'
}
Issue SCRUM-4 işleme hatası: error: null value in column "name" of relation "documents" violates not-null constraint
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:196:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:2057:28) {
  length: 324,
  severity: 'ERROR',
  code: '23502',
  detail: 'Failing row contains (106, null, null, Issue: SCRUM-4\n' +
    '\n' +
    'Başlık: agentic memory\n' +
    '\n' +
    'Tip: Task\n' +
    'Durum: To Do..., null, null, 2025-05-28 01:40:21.461, 1, null).',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'documents',
  column: 'name',
  dataType: undefined,
  constraint: undefined,
  file: 'execMain.c',
  line: '2006',
  routine: 'ExecConstraints'
}
createDocument işlemi sırasında hata: error: null value in column "name" of relation "documents" violates not-null constraint
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:196:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:2057:28) {
  length: 324,
  severity: 'ERROR',
  code: '23502',
  detail: 'Failing row contains (107, null, null, Issue: SCRUM-3\n' +
    '\n' +
    'Başlık: agentic ui\n' +
    '\n' +
    'Tip: Task\n' +
    'Durum: To Do\n' +
    'Ön..., null, null, 2025-05-28 01:40:21.993, 1, null).',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'documents',
  column: 'name',
  dataType: undefined,
  constraint: undefined,
  file: 'execMain.c',
  line: '2006',
  routine: 'ExecConstraints'
}
Issue SCRUM-3 işleme hatası: error: null value in column "name" of relation "documents" violates not-null constraint
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:196:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:2057:28) {
  length: 324,
  severity: 'ERROR',
  code: '23502',
  detail: 'Failing row contains (107, null, null, Issue: SCRUM-3\n' +
    '\n' +
    'Başlık: agentic ui\n' +
    '\n' +
    'Tip: Task\n' +
    'Durum: To Do\n' +
    'Ön..., null, null, 2025-05-28 01:40:21.993, 1, null).',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'documents',
  column: 'name',
  dataType: undefined,
  constraint: undefined,
  file: 'execMain.c',
  line: '2006',
  routine: 'ExecConstraints'
}
1:40:22 AM [express] POST /api/integrations/jira/analyze 400 in 1440ms :: {"success":false,"message"…
createDocument işlemi sırasında hata: error: null value in column "name" of relation "documents" violates not-null constraint
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:196:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:2057:28) {
  length: 324,
  severity: 'ERROR',
  code: '23502',
  detail: 'Failing row contains (108, null, null, Issue: SCRUM-5\n' +
    '\n' +
    'Başlık: vector db\n' +
    '\n' +
    'Tip: Task\n' +
    'Durum: To Do\n' +
    'Önc..., null, null, 2025-05-28 01:40:24.849, 1, null).',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'documents',
  column: 'name',
  dataType: undefined,
  constraint: undefined,
  file: 'execMain.c',
  line: '2006',
  routine: 'ExecConstraints'
}
Issue SCRUM-5 işleme hatası: error: null value in column "name" of relation "documents" violates not-null constraint
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:196:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:2057:28) {
  length: 324,
  severity: 'ERROR',
  code: '23502',
  detail: 'Failing row contains (108, null, null, Issue: SCRUM-5\n' +
    '\n' +
    'Başlık: vector db\n' +
    '\n' +
    'Tip: Task\n' +
    'Durum: To Do\n' +
    'Önc..., null, null, 2025-05-28 01:40:24.849, 1, null).',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'documents',
  column: 'name',
  dataType: undefined,
  constraint: undefined,
  file: 'execMain.c',
  line: '2006',
  routine: 'ExecConstraints'
}
createDocument işlemi sırasında hata: error: null value in column "name" of relation "documents" violates not-null constraint
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:196:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:2057:28) {
  length: 324,
  severity: 'ERROR',
  code: '23502',
  detail: 'Failing row contains (109, null, null, Issue: SCRUM-4\n' +
    '\n' +
    'Başlık: agentic memory\n' +
    '\n' +
    'Tip: Task\n' +
    'Durum: To Do..., null, null, 2025-05-28 01:40:25.158, 1, null).',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'documents',
  column: 'name',
  dataType: undefined,
  constraint: undefined,
  file: 'execMain.c',
  line: '2006',
  routine: 'ExecConstraints'
}
Issue SCRUM-4 işleme hatası: error: null value in column "name" of relation "documents" violates not-null constraint
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:196:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:2057:28) {
  length: 324,
  severity: 'ERROR',
  code: '23502',
  detail: 'Failing row contains (109, null, null, Issue: SCRUM-4\n' +
    '\n' +
    'Başlık: agentic memory\n' +
    '\n' +
    'Tip: Task\n' +
    'Durum: To Do..., null, null, 2025-05-28 01:40:25.158, 1, null).',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'documents',
  column: 'name',
  dataType: undefined,
  constraint: undefined,
  file: 'execMain.c',
  line: '2006',
  routine: 'ExecConstraints'
}
createDocument işlemi sırasında hata: error: null value in column "name" of relation "documents" violates not-null constraint
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:196:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:2057:28) {
  length: 324,
  severity: 'ERROR',
  code: '23502',
  detail: 'Failing row contains (110, null, null, Issue: SCRUM-3\n' +
    '\n' +
    'Başlık: agentic ui\n' +
    '\n' +
    'Tip: Task\n' +
    'Durum: To Do\n' +
    'Ön..., null, null, 2025-05-28 01:40:25.601, 1, null).',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'documents',
  column: 'name',
  dataType: undefined,
  constraint: undefined,
  file: 'execMain.c',
  line: '2006',
  routine: 'ExecConstraints'
}
Issue SCRUM-3 işleme hatası: error: null value in column "name" of relation "documents" violates not-null constraint
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.createDocument (/home/<USER>/workspace/server/storage-postgres.ts:196:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:2057:28) {
  length: 324,
  severity: 'ERROR',
  code: '23502',
  detail: 'Failing row contains (110, null, null, Issue: SCRUM-3\n' +
    '\n' +
    'Başlık: agentic ui\n' +
    '\n' +
    'Tip: Task\n' +
    'Durum: To Do\n' +
    'Ön..., null, null, 2025-05-28 01:40:25.601, 1, null).',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'documents',
  column: 'name',
  dataType: undefined,
  constraint: undefined,
  file: 'execMain.c',
  line: '2006',
  routine: 'ExecConstraints'
}
1:40:25 AM [express] POST /api/integrations/jira/analyze 400 in 1251ms :: {"success":false,"message"