
> rest-express@1.0.0 dev
3:08:26 PM [express] <PERSON><PERSON> zaten mevcut: netas
3:08:26 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 6 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
3:09:40 PM [express] GET /api/permissions 304 in 90ms :: ["document_management","test_management"]
3:09:40 PM [express] GET /api/user-projects 304 in 51ms :: []
3:09:40 PM [express] GET /api/ai-models 304 in 24ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
3:09:40 PM [express] GET /api/documents 304 in 46ms :: []
Unexpected database pool error: error: terminating connection due to administrator command
    at cn.parseErrorMessage (file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1182:6)
    at cn.handlePacket (file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1143:13)
    at cn.parse (file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1127:36)
    at v.<anonymous> (file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1190:16)
    at v.emit (file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:397:12)
    at WebSocket.<anonymous> (file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:987:80)
    at callListener (/home/<USER>/workspace/node_modules/ws/lib/event-target.js:290:14)
    at WebSocket.onMessage (/home/<USER>/workspace/node_modules/ws/lib/event-target.js:209:9)
    at WebSocket.emit (node:events:518:28)
    at Receiver.receiverOnMessage (/home/<USER>/workspace/node_modules/ws/lib/websocket.js:1220:20)
    at Receiver.emit (node:events:518:28)
    at Receiver.dataMessage (/home/<USER>/workspace/node_modules/ws/lib/receiver.js:569:14)
    at Receiver.getData (/home/<USER>/workspace/node_modules/ws/lib/receiver.js:496:10)
    at Receiver.startLoop (/home/<USER>/workspace/node_modules/ws/lib/receiver.js:167:16)
    at Receiver._write (/home/<USER>/workspace/node_modules/ws/lib/receiver.js:94:10)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at TLSSocket.socketOnData (/home/<USER>/workspace/node_modules/ws/lib/websocket.js:1355:35)
    at TLSSocket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Readable.push (node:internal/streams/readable:392:5)
    at TLSWrap.onStreamRead (node:internal/stream_base_commons:191:23) {
  length: 116,
  severity: 'FATAL',
  code: '57P01',
  detail: undefined,
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'postgres.c',
  line: '3289',
  routine: 'ProcessInterrupts',
  client: NeonClient {
    _events: [Object: null prototype] { error: [Function (anonymous)] },
    _eventsCount: 1,
    _maxListeners: undefined,
    connectionParameters: ConnectionParameters {
      user: 'neondb_owner',
      database: 'neondb',
      port: 5432,
      host: 'ep-crimson-boat-a5c0746z.us-east-2.aws.neon.tech',
      binary: false,
      options: undefined,
      ssl: {},
      client_encoding: '',
      replication: undefined,
      isDomainSocket: false,
      application_name: undefined,
      fallback_application_name: undefined,
      statement_timeout: false,
      lock_timeout: false,
      idle_in_transaction_session_timeout: false,
      query_timeout: false,
      connect_timeout: 10
    },
    user: 'neondb_owner',
    database: 'neondb',
    port: 5432,
    host: 'ep-crimson-boat-a5c0746z.us-east-2.aws.neon.tech',
    replication: undefined,
    _Promise: [Function: Promise],
    _types: TypeOverrides { _types: [Object], text: {}, binary: {} },
    _ending: true,
    _connecting: false,
    _connected: true,
    _connectionError: false,
    _queryable: false,
    connection: Connection {
      _events: [Object: null prototype],
      _eventsCount: 22,
      _maxListeners: undefined,
      stream: [Socket],
      _keepAlive: false,
      _keepAliveInitialDelayMillis: 0,
      lastBuffer: false,
      parsedStatements: {},
      ssl: false,
      _ending: true,
      _emitMessage: false,
      _connecting: true
    },
    queryQueue: [],
    binary: false,
    processID: 697718590,
    secretKey: -1792818021,
    ssl: false,
    _connectionTimeoutMillis: 10000,
    config: {
      connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
      connectionTimeoutMillis: 10000,
      idleTimeoutMillis: 60000,
      max: 10,
      maxUses: 500,
      allowExitOnIdle: true,
      maxLifetimeSeconds: 0
    },
    _connectionCallback: null,
    connectionTimeoutHandle: Timeout {
      _idleTimeout: -1,
      _idlePrev: null,
      _idleNext: null,
      _idleStart: 75109,
      _onTimeout: null,
      _timerArgs: undefined,
      _repeat: null,
      _destroyed: true,
      [Symbol(refed)]: true,
      [Symbol(kHasPrimitive)]: false,
      [Symbol(asyncId)]: 10166,
      [Symbol(triggerId)]: 0
    },
    release: [Function (anonymous)],
    activeQuery: null,
    readyForQuery: true,
    hasExecuted: true,
    _poolUseCount: 8
  }
}
Unexpected database pool error: error: terminating connection due to administrator command
    at cn.parseErrorMessage (file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1182:6)
    at cn.handlePacket (file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1143:13)
    at cn.parse (file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1127:36)
    at v.<anonymous> (file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1190:16)
    at v.emit (file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:397:12)
    at WebSocket.<anonymous> (file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:987:80)
    at callListener (/home/<USER>/workspace/node_modules/ws/lib/event-target.js:290:14)
    at WebSocket.onMessage (/home/<USER>/workspace/node_modules/ws/lib/event-target.js:209:9)
    at WebSocket.emit (node:events:518:28)
    at Receiver.receiverOnMessage (/home/<USER>/workspace/node_modules/ws/lib/websocket.js:1220:20)
    at Receiver.emit (node:events:518:28)
    at Receiver.dataMessage (/home/<USER>/workspace/node_modules/ws/lib/receiver.js:569:14)
    at Receiver.getData (/home/<USER>/workspace/node_modules/ws/lib/receiver.js:496:10)
    at Receiver.startLoop (/home/<USER>/workspace/node_modules/ws/lib/receiver.js:167:16)
    at Receiver._write (/home/<USER>/workspace/node_modules/ws/lib/receiver.js:94:10)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at TLSSocket.socketOnData (/home/<USER>/workspace/node_modules/ws/lib/websocket.js:1355:35)
    at TLSSocket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Readable.push (node:internal/streams/readable:392:5)
    at TLSWrap.onStreamRead (node:internal/stream_base_commons:191:23) {
  length: 116,
  severity: 'FATAL',
  code: '57P01',
  detail: undefined,
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'postgres.c',
  line: '3289',
  routine: 'ProcessInterrupts',
  client: NeonClient {
    _events: [Object: null prototype] { error: [Function (anonymous)] },
    _eventsCount: 1,
    _maxListeners: undefined,
    connectionParameters: ConnectionParameters {
      user: 'neondb_owner',
      database: 'neondb',
      port: 5432,
      host: 'ep-crimson-boat-a5c0746z.us-east-2.aws.neon.tech',
      binary: false,
      options: undefined,
      ssl: {},
      client_encoding: '',
      replication: undefined,
      isDomainSocket: false,
      application_name: undefined,
      fallback_application_name: undefined,
      statement_timeout: false,
      lock_timeout: false,
      idle_in_transaction_session_timeout: false,
      query_timeout: false,
      connect_timeout: 10
    },
    user: 'neondb_owner',
    database: 'neondb',
    port: 5432,
    host: 'ep-crimson-boat-a5c0746z.us-east-2.aws.neon.tech',
    replication: undefined,
    _Promise: [Function: Promise],
    _types: TypeOverrides { _types: [Object], text: {}, binary: {} },
    _ending: true,
    _connecting: false,
    _connected: true,
    _connectionError: false,
    _queryable: false,
    connection: Connection {
      _events: [Object: null prototype],
      _eventsCount: 22,
      _maxListeners: undefined,
      stream: [Socket],
      _keepAlive: false,
      _keepAliveInitialDelayMillis: 0,
      lastBuffer: false,
      parsedStatements: {},
      ssl: false,
      _ending: true,
      _emitMessage: false,
      _connecting: true
    },
    queryQueue: [],
    binary: false,
    processID: 1917843429,
    secretKey: -952384842,
    ssl: false,
    _connectionTimeoutMillis: 10000,
    config: {
      connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
      connectionTimeoutMillis: 10000,
      idleTimeoutMillis: 60000,
      max: 10,
      maxUses: 500,
      allowExitOnIdle: true,
      maxLifetimeSeconds: 0
    },
    _connectionCallback: null,
    connectionTimeoutHandle: Timeout {
      _idleTimeout: -1,
      _idlePrev: null,
      _idleNext: null,
      _idleStart: 75451,
      _onTimeout: null,
      _timerArgs: undefined,
      _repeat: null,
      _destroyed: true,
      [Symbol(refed)]: true,
      [Symbol(kHasPrimitive)]: false,
      [Symbol(asyncId)]: 10215,
      [Symbol(triggerId)]: 0
    },
    release: [Function (anonymous)],
    activeQuery: null,
    readyForQuery: true,
    hasExecuted: true,
    _poolUseCount: 5
  }
}
o1/o3 modelleri için Azure OpenAI API anahtarı: ******GrPml
3:10:32 PM [express] POST /api/documents/upload 201 in 37273ms :: {"document":{"id":27,"name":"AD_TL…
3:10:34 PM [express] GET /api/documents 200 in 129ms :: [{"id":27,"name":"AD_TLP-2023-000342_Vergi_O…
3:10:41 PM [express] GET /api/ai-models 304 in 23ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
3:10:49 PM [express] GET /api/user-projects 304 in 51ms :: []
3:10:49 PM [express] GET /api/permissions 200 in 58ms :: ["user_management","project_management","do…
3:10:51 PM [express] GET /api/ai-models 304 in 25ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
3:11:01 PM [express] GET /api/permissions 304 in 44ms :: ["user_management","project_management","do…
3:11:01 PM [express] GET /api/user-projects 304 in 44ms :: []
3:11:01 PM [express] GET /api/ai-models 304 in 22ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
3:11:01 PM [express] GET /api/documents 200 in 245ms :: [{"id":21,"name":"TrendKart_Analiz_Dokumani.…
3:11:12 PM [express] GET /api/documents/27/requirements 200 in 49ms :: [{"id":288,"documentId":27,"c…
3:11:12 PM [express] GET /api/documents/27/api-endpoints 200 in 55ms :: [{"id":101,"documentId":27,"…
3:11:12 PM [express] GET /api/documents/27/ai-analysis 200 in 59ms :: {"id":26,"documentId":27,"obse…
3:11:12 PM [express] GET /api/documents/27/components 200 in 56ms :: [{"id":139,"documentId":27,"nam…
3:11:12 PM [express] GET /api/documents/27 200 in 148ms :: {"id":27,"name":"AD_TLP-2023-000342_Vergi…
3:11:15 PM [express] GET /api/ai-models 304 in 24ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
3:11:23 PM [express] GET /api/ai-models 304 in 24ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
3:11:29 PM [express] GET /api/user-projects 304 in 44ms :: []
3:11:29 PM [express] GET /api/permissions 200 in 71ms :: ["document_management","test_management"]
3:11:29 PM [express] GET /api/ai-models 304 in 22ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
3:11:29 PM [express] GET /api/documents 200 in 127ms :: [{"id":27,"name":"AD_TLP-2023-000342_Vergi_O…
3:11:55 PM [express] GET /api/user-projects 200 in 44ms :: []
3:11:55 PM [express] GET /api/permissions 200 in 79ms :: ["document_management","test_management"]
3:11:55 PM [express] GET /api/ai-models 200 in 23ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
3:11:55 PM [express] GET /api/documents 200 in 128ms :: [{"id":27,"name":"AD_TLP-2023-000342_Vergi_O…
3:11:57 PM [express] GET /api/documents/27/components 200 in 43ms :: [{"id":139,"documentId":27,"nam…
3:11:57 PM [express] GET /api/documents/27/api-endpoints 200 in 44ms :: [{"id":101,"documentId":27,"…
3:11:57 PM [express] GET /api/documents/27/requirements 200 in 45ms :: [{"id":288,"documentId":27,"c…
3:11:57 PM [express] GET /api/documents/27/ai-analysis 200 in 45ms :: {"id":26,"documentId":27,"obse…
3:11:58 PM [express] GET /api/documents/27 200 in 122ms :: {"id":27,"name":"AD_TLP-2023-000342_Vergi…
3:11:59 PM [express] GET /api/documents/27/test-scenarios 200 in 62ms :: []
o1/o3 modelleri için Azure OpenAI API anahtarı: ******GrPml