1:31:08 PM [express] GET /api/permissions 304 in 52ms :: []
1:31:08 PM [express] GET /api/ai-models 304 in 22ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
1:31:08 PM [express] GET /api/user-projects 304 in 55ms :: []
1:31:08 PM [express] GET /api/documents 304 in 208ms :: [{"id":21,"name":"Trend<PERSON><PERSON>_Analiz_<PERSON>mani.…
1:31:11 PM [express] GET /api/permissions/all 200 in 24ms :: [{"id":1,"name":"user_management","desc…
1:31:11 PM [express] GET /api/users 200 in 47ms :: [{"id":1,"username":"netas","role":"admin","name"…
1:31:21 PM [express] GET /api/users/1/permissions 200 in 46ms :: []
<PERSON>zin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:31:22 PM [express] POST /api/users/1/permissions 500 in 90ms :: {"error":"İzin değiştirilemedi: in…
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:31:23 PM [express] POST /api/users/1/permissions 500 in 70ms :: {"error":"İzin değiştirilemedi: in…
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:31:24 PM [express] POST /api/users/1/permissions 500 in 72ms :: {"error":"İzin değiştirilemedi: in…
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:31:26 PM [express] POST /api/users/1/permissions 500 in 74ms :: {"error":"İzin değiştirilemedi: in…
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:31:27 PM [express] POST /api/users/1/permissions 500 in 83ms :: {"error":"İzin değiştirilemedi: in…
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:31:28 PM [express] POST /api/users/1/permissions 500 in 87ms :: {"error":"İzin değiştirilemedi: in…
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:31:29 PM [express] POST /api/users/1/permissions 500 in 86ms :: {"error":"İzin değiştirilemedi: in…
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:31:30 PM [express] POST /api/users/1/permissions 500 in 84ms :: {"error":"İzin değiştirilemedi: in…
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:31:33 PM [express] POST /api/users/1/permissions 500 in 86ms :: {"error":"İzin değiştirilemedi: in…
1:31:38 PM [express] GET /api/permissions/all 304 in 21ms :: [{"id":1,"name":"user_management","desc…
1:31:38 PM [express] GET /api/user-projects 304 in 47ms :: []
1:31:38 PM [express] GET /api/permissions 304 in 47ms :: []
1:31:38 PM [express] GET /api/users 304 in 51ms :: [{"id":1,"username":"netas","role":"admin","name"…
1:31:43 PM [express] GET /api/user-projects 304 in 49ms :: []
1:31:43 PM [express] GET /api/permissions 304 in 49ms :: []
1:31:45 PM [express] GET /api/projects 200 in 56ms :: []
1:31:51 PM [express] GET /api/ai-models 304 in 24ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
1:31:51 PM [express] GET /api/documents 304 in 206ms :: [{"id":21,"name":"TrendKart_Analiz_Dokumani.…
1:31:55 PM [express] GET /api/permissions/all 304 in 25ms :: [{"id":1,"name":"user_management","desc…
1:31:55 PM [express] GET /api/users 304 in 51ms :: [{"id":1,"username":"netas","role":"admin","name"…
1:32:06 PM [express] POST /api/users 201 in 136ms :: {"id":2,"username":"test","role":"user","name":…
1:32:06 PM [express] GET /api/users 200 in 46ms :: [{"id":1,"username":"netas","role":"admin","name"…
1:32:09 PM [express] GET /api/users/2/permissions 200 in 46ms :: []
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:32:10 PM [express] POST /api/users/2/permissions 500 in 80ms :: {"error":"İzin değiştirilemedi: in…
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:32:10 PM [express] POST /api/users/2/permissions 500 in 83ms :: {"error":"İzin değiştirilemedi: in…
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:32:11 PM [express] POST /api/users/2/permissions 500 in 88ms :: {"error":"İzin değiştirilemedi: in…
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:32:13 PM [express] POST /api/users/2/permissions 500 in 74ms :: {"error":"İzin değiştirilemedi: in…
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:32:14 PM [express] POST /api/users/2/permissions 500 in 75ms :: {"error":"İzin değiştirilemedi: in…
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:32:15 PM [express] POST /api/users/2/permissions 500 in 75ms :: {"error":"İzin değiştirilemedi: in…
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:32:16 PM [express] POST /api/users/2/permissions 500 in 77ms :: {"error":"İzin değiştirilemedi: in…
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:32:18 PM [express] POST /api/users/2/permissions 500 in 90ms :: {"error":"İzin değiştirilemedi: in…
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:32:20 PM [express] POST /api/users/2/permissions 500 in 77ms :: {"error":"İzin değiştirilemedi: in…
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:32:21 PM [express] POST /api/users/2/permissions 500 in 77ms :: {"error":"İzin değiştirilemedi: in…
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:32:22 PM [express] POST /api/users/2/permissions 500 in 77ms :: {"error":"İzin değiştirilemedi: in…
İzin değiştirme hatası: error: insert or update on table "user_permissions" violates foreign key constraint "user_permissions_permission_id_permissions_id_fk"
    at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PostgresStorage.grantPermission (/home/<USER>/workspace/server/storage-postgres.ts:514:7)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:215:9) {
  length: 336,
  severity: 'ERROR',
  code: '23503',
  detail: 'Key (permission_id)=(1) is not present in table "permissions".',
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: 'public',
  table: 'user_permissions',
  column: undefined,
  dataType: undefined,
  constraint: 'user_permissions_permission_id_permissions_id_fk',
  file: 'ri_triggers.c',
  line: '2608',
  routine: 'ri_ReportViolation'
}
1:32:24 PM [express] POST /api/users/2/permissions 500 in 79ms :: {"error":"İzin değiştirilemedi: in…
