2025-04-17 12:44:10.26
ccd6d094
User
o1/o3 modelleri için Azure OpenAI API anahtarı: ******GrPml
2025-04-17 12:44:41.61
ccd6d094
User
JSON ayrışt<PERSON>rma hatası: SyntaxError: Unterminated string in JSON at position 543
2025-04-17 12:44:41.61
ccd6d094
User
at JSON.parse ()
2025-04-17 12:44:41.61
ccd6d094
User
at Object.validateDocumentCoverage (file:///home/<USER>/workspace/dist/index.js:1852:19)
2025-04-17 12:44:41.61
ccd6d094
User
at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-04-17 12:44:41.61
ccd6d094
User
at async file:///home/<USER>/workspace/dist/index.js:2475:30
2025-04-17 12:44:41.61
ccd6d094
User
OpenAI'dan al<PERSON>nan ya<PERSON> (ilk 500 karakter): {
2025-04-17 12:44:41.61
ccd6d094
User
"coverageRate": 0.83,
2025-04-17 12:44:41.61
ccd6d094
User
"missingRequirements": ["REQ-016", "REQ-017", "REQ-018"],
2025-04-17 12:44:41.61
ccd6d094
User
"analysisDetails": {
2025-04-17 12:44:41.61
ccd6d094
User
"completeness": 0.85,
2025-04-17 12:44:41.61
ccd6d094
User
"accuracy": 0.9,
2025-04-17 12:44:41.61
ccd6d094
User
"clarity": 0.9,
2025-04-17 12:44:41.61
ccd6d094
User
"maintainability": 0.85,
2025-04-17 12:44:41.61
ccd6d094
User
"traceability": 0.9
2025-04-17 12:44:41.61
ccd6d094
User
},
2025-04-17 12:44:41.61
ccd6d094
User
"recommendations": [
2025-04-17 12:44:41.61
ccd6d094
User
"Performans (REQ-016) için ortalama ve maksimum servis cevap sürelerini ölçen ek test senaryoları oluşturulmalı.",
2025-04-17 12:44:41.61
ccd6d094
User
"Güvenlik (REQ-017) hususunda yetkisiz kullanıcıların admin ekranlarına erişim denemelerini, rol bazlı yetki kontrolleri...
2025-04-17 12:44:41.65
ccd6d094
User
9:44:41 AM [express] POST /api/documents/20/validate-coverage 200 in 31501ms :: {"id":11,"documentId…
2025-04-17 12:44:49.22
ccd6d094
User
9:44:49 AM [express] DELETE /api/documents/20/coverage-validation 200 in 47ms :: {"success":true,"me…
2025-04-17 12:44:49.49
ccd6d094
User
9:44:49 AM [express] GET /api/documents/20/coverage-validation 200 in 47ms