import React, { useState, useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Textarea } from '@/components/ui/textarea';
import {
  Bot,
  Search,
  Play,
  Settings,
  Zap,
  FileText,
  Target,
  BarChart3,
  MessageSquare,
  CheckCircle,
  Clock,
  AlertCircle,
  RefreshCw,
  Download,
  Upload,
  Code,
  Database,
  TestTube
} from 'lucide-react';

interface JiraConnection {
  id: number;
  name: string;
  baseUrl: string;
  username: string;
  token: string;
  status: string;
}

interface JiraProject {
  id: string;
  key: string;
  name: string;
  description?: string;
  lead?: string;
}

interface JiraIssue {
  id: string;
  key: string;
  summary: string;
  description: string;
  issueType: string;
  status: string;
  priority: string;
  assignee?: string;
  reporter?: string;
  created: string;
  updated: string;
  comments: Array<{
    id: string;
    author: string;
    body: string;
    created: string;
  }>;
}

interface BotAnalysisResult {
  type: 'scenario' | 'coverage' | 'resource' | 'api';
  title: string;
  content: string;
  metadata?: any;
  timestamp: string;
}

interface JiraAIBotProps {
  projectId: number;
}

const BOT_COMMANDS = [
  {
    command: '@ai-bot scenario',
    description: 'Test senaryoları oluştur',
    icon: <TestTube size={16} className="text-green-600" />,
    color: 'bg-green-100 text-green-800'
  },
  {
    command: '@ai-bot coverage',
    description: 'Kapsam analizi yap',
    icon: <BarChart3 size={16} className="text-blue-600" />,
    color: 'bg-blue-100 text-blue-800'
  },
  {
    command: '@ai-bot resource',
    description: 'Kaynak analizi yap',
    icon: <Database size={16} className="text-purple-600" />,
    color: 'bg-purple-100 text-purple-800'
  },
  {
    command: '@ai-bot api',
    description: 'API endpoint analizi',
    icon: <Code size={16} className="text-orange-600" />,
    color: 'bg-orange-100 text-orange-800'
  },
  {
    command: '@ai-bot general',
    description: 'Genel analiz yap',
    icon: <FileText size={16} className="text-gray-600" />,
    color: 'bg-gray-100 text-gray-800'
  },
  {
    command: '@ai-bot security',
    description: 'Güvenlik analizi',
    icon: <AlertCircle size={16} className="text-red-600" />,
    color: 'bg-red-100 text-red-800'
  },
  {
    command: '@ai-bot performance',
    description: 'Performans analizi',
    icon: <Zap size={16} className="text-yellow-600" />,
    color: 'bg-yellow-100 text-yellow-800'
  },
  {
    command: '@ai-bot architecture',
    description: 'Mimari analiz',
    icon: <Settings size={16} className="text-indigo-600" />,
    color: 'bg-indigo-100 text-indigo-800'
  }
];

export default function JiraAIBot({ projectId }: JiraAIBotProps) {
  const [selectedConnection, setSelectedConnection] = useState<JiraConnection | null>(null);
  const [selectedProject, setSelectedProject] = useState<JiraProject | null>(null);
  const [selectedIssue, setSelectedIssue] = useState<JiraIssue | null>(null);
  const [botCommand, setBotCommand] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<BotAnalysisResult[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [autoMode, setAutoMode] = useState(false);
  const [lastAnalysisTime, setLastAnalysisTime] = useState<number>(0);
  const [analysisCount, setAnalysisCount] = useState<number>(0);
  const [rateLimitMessage, setRateLimitMessage] = useState<string>('');

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Jira bağlantılarını getir
  const { data: connections = [] } = useQuery<JiraConnection[]>({
    queryKey: [`/api/projects/${projectId}/connections`],
    queryFn: async () => {
      const response = await apiRequest('GET', `/api/projects/${projectId}/connections?type=jira`);
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 dakika boyunca fresh kabul et
    refetchOnWindowFocus: false, // Pencere focus'unda yeniden fetch etme
    refetchOnMount: false, // Mount'ta yeniden fetch etme (sadece ilk kez)
    refetchInterval: false // Otomatik interval refresh'i kapat
  });

  // Jira projelerini getir
  const { data: jiraProjects = [] } = useQuery<JiraProject[]>({
    queryKey: ['jira-projects', selectedConnection?.id],
    queryFn: async () => {
      if (!selectedConnection) return [];

      try {
        const response = await apiRequest('POST', '/api/integrations/jira/projects', {
          url: selectedConnection.baseUrl,
          username: selectedConnection.username,
          apiToken: selectedConnection.token
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Jira projects error:', response.status, errorText);
          throw new Error(`Projeler alınamadı: ${response.status} ${response.statusText}`);
        }

        const responseText = await response.text();
        console.log('Jira projects response:', responseText.substring(0, 200));

        let data;
        try {
          data = JSON.parse(responseText);
        } catch (parseError) {
          console.error('JSON parse error:', parseError);
          throw new Error('Jira\'dan gelen yanıt geçerli JSON formatında değil');
        }

        if (!data.success) {
          throw new Error(data.message || 'Projeler alınamadı');
        }

        return data.projects || [];
      } catch (error) {
        console.error('Jira projects fetch error:', error);
        toast({
          title: 'Jira Projeler Hatası',
          description: error instanceof Error ? error.message : 'Bilinmeyen hata',
          variant: 'destructive',
        });
        return [];
      }
    },
    enabled: !!selectedConnection,
    staleTime: 10 * 60 * 1000, // 10 dakika boyunca fresh kabul et
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchInterval: false
  });

  // Jira issue'larını getir
  const { data: jiraIssues = [], isLoading: issuesLoading } = useQuery<JiraIssue[]>({
    queryKey: ['jira-issues', selectedConnection?.id, selectedProject?.key],
    queryFn: async () => {
      if (!selectedConnection || !selectedProject) return [];

      try {
        const response = await apiRequest('POST', '/api/integrations/jira/issues', {
          url: selectedConnection.baseUrl,
          username: selectedConnection.username,
          apiToken: selectedConnection.token,
          projectKey: selectedProject.key
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Jira issues error:', response.status, errorText);
          throw new Error(`Issue'lar alınamadı: ${response.status} ${response.statusText}`);
        }

        const responseText = await response.text();
        console.log('Jira issues response:', responseText.substring(0, 200));

        let data;
        try {
          data = JSON.parse(responseText);
        } catch (parseError) {
          console.error('JSON parse error:', parseError);
          throw new Error('Jira\'dan gelen yanıt geçerli JSON formatında değil');
        }

        if (!data.success) {
          throw new Error(data.message || 'Issue\'lar alınamadı');
        }

        return data.issues || [];
      } catch (error) {
        console.error('Jira issues fetch error:', error);
        toast({
          title: 'Jira Issue\'lar Hatası',
          description: error instanceof Error ? error.message : 'Bilinmeyen hata',
          variant: 'destructive',
        });
        return [];
      }
    },
    enabled: !!selectedConnection && !!selectedProject,
    staleTime: 2 * 60 * 1000, // 2 dakika boyunca fresh kabul et (issue'lar daha sık değişebilir)
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchInterval: false
  });

  // Filtrelenmiş issue'lar
  const filteredIssues = useMemo(() => {
    return jiraIssues.filter(issue => {
      const matchesSearch = !searchTerm ||
        issue.summary.toLowerCase().includes(searchTerm.toLowerCase()) ||
        issue.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
        issue.description.toLowerCase().includes(searchTerm.toLowerCase());

      return matchesSearch;
    });
  }, [jiraIssues, searchTerm]);

  // Bot analizi çalıştırma
  const runBotAnalysisMutation = useMutation({
    mutationFn: async ({ issue, command }: { issue: JiraIssue; command: string }) => {
      if (!selectedConnection) throw new Error('Bağlantı seçilmedi');

      const response = await apiRequest('POST', '/api/integrations/jira/bot-analysis', {
        url: selectedConnection.baseUrl,
        username: selectedConnection.username,
        apiToken: selectedConnection.token,
        issueKey: issue.key,
        command: command,
        issueData: {
          summary: issue.summary,
          description: issue.description,
          type: issue.issueType,
          priority: issue.priority
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Bot analysis error:', response.status, errorText);
        throw new Error(`Bot analizi başarısız: ${response.status} ${response.statusText}`);
      }

      const responseText = await response.text();
      console.log('Bot analysis response:', responseText.substring(0, 200));

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        throw new Error('Bot\'dan gelen yanıt geçerli JSON formatında değil');
      }

      if (!data.success) {
        throw new Error(data.message || 'Bot analizi başarısız');
      }

      return data;
    },
    onSuccess: (data, variables) => {
      const result: BotAnalysisResult = {
        type: data.analysisType,
        title: `${variables.issue.key} - ${data.analysisType.toUpperCase()} Analizi`,
        content: data.analysis,
        metadata: data.metadata,
        timestamp: new Date().toISOString()
      };

      setAnalysisResults(prev => [result, ...prev]);

      toast({
        title: 'Bot analizi tamamlandı',
        description: `${variables.issue.key} için ${data.analysisType} analizi başarıyla tamamlandı.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Bot analizi hatası',
        description: error.message || 'Analiz sırasında bir hata oluştu.',
        variant: 'destructive',
      });
    }
  });

  // Rate limiting kontrolü
  const checkRateLimit = useCallback(() => {
    const now = Date.now();
    const fiveMinutesAgo = now - (5 * 60 * 1000);

    // 5 dakika geçmişse sayacı sıfırla
    if (lastAnalysisTime < fiveMinutesAgo) {
      setAnalysisCount(0);
      setRateLimitMessage('');
      return true;
    }

    // 5 dakika içinde 3'ten fazla analiz yapılmışsa engelle
    if (analysisCount >= 3) {
      const remainingTime = Math.ceil((lastAnalysisTime + (5 * 60 * 1000) - now) / 1000);
      const minutes = Math.floor(remainingTime / 60);
      const seconds = remainingTime % 60;

      setRateLimitMessage(`Rate limit aşıldı. ${minutes}:${seconds.toString().padStart(2, '0')} sonra tekrar deneyin.`);
      return false;
    }

    return true;
  }, [lastAnalysisTime, analysisCount]);

  // Bot komutunu çalıştır
  const executeBotCommand = useCallback(async (issue: JiraIssue, command: string) => {
    // Rate limiting kontrolü
    if (!checkRateLimit()) {
      toast({
        title: 'Rate Limit',
        description: rateLimitMessage,
        variant: 'destructive',
      });
      return;
    }

    setIsProcessing(true);
    setRateLimitMessage('');

    try {
      await runBotAnalysisMutation.mutateAsync({ issue, command });

      // Başarılı analiz sonrası sayacı artır
      setAnalysisCount(prev => prev + 1);
      setLastAnalysisTime(Date.now());

    } finally {
      setIsProcessing(false);
    }
  }, [runBotAnalysisMutation, checkRateLimit, rateLimitMessage, toast]);

  // Otomatik mod toggle
  const toggleAutoMode = useCallback(() => {
    setAutoMode(!autoMode);
    if (!autoMode) {
      toast({
        title: 'Otomatik mod aktif',
        description: 'Bot artık yeni issue\'ları otomatik olarak analiz edecek.',
      });
    } else {
      toast({
        title: 'Otomatik mod deaktif',
        description: 'Bot artık manuel komutları bekleyecek.',
      });
    }
  }, [autoMode, toast]);

  // Analiz sonucunu formatla
  const formatAnalysisContent = (content: string) => {
    // Markdown-like formatting
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/```(.*?)```/gs, '<pre class="bg-gray-100 p-2 rounded text-sm"><code>$1</code></pre>')
      .replace(/`(.*?)`/g, '<code class="bg-gray-100 px-1 rounded text-sm">$1</code>')
      .replace(/\n/g, '<br>');
  };

  // Analiz tipine göre icon
  const getAnalysisIcon = (type: string) => {
    switch (type) {
      case 'scenario':
        return <TestTube size={16} className="text-green-600" />;
      case 'coverage':
        return <BarChart3 size={16} className="text-blue-600" />;
      case 'resource':
        return <Database size={16} className="text-purple-600" />;
      case 'api':
        return <Code size={16} className="text-orange-600" />;
      case 'general':
        return <FileText size={16} className="text-gray-600" />;
      case 'security':
        return <AlertCircle size={16} className="text-red-600" />;
      case 'performance':
        return <Zap size={16} className="text-yellow-600" />;
      case 'architecture':
        return <Settings size={16} className="text-indigo-600" />;
      default:
        return <Bot size={16} className="text-gray-600" />;
    }
  };

  // Analiz tipine göre renk
  const getAnalysisColor = (type: string) => {
    switch (type) {
      case 'scenario':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'coverage':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'resource':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'api':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'general':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'security':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'performance':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'architecture':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="border-b border-gray-200 p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <Bot size={24} className="text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">Jira AI Bot</h2>
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              Otomasyonel Analiz
            </Badge>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant={autoMode ? 'default' : 'outline'}
              size="sm"
              onClick={toggleAutoMode}
              className="flex items-center gap-2"
            >
              <Zap size={16} />
              {autoMode ? 'Otomatik Mod' : 'Manuel Mod'}
            </Button>

            <Button variant="outline" size="sm">
              <Settings size={16} />
            </Button>
          </div>
        </div>

        {/* Bağlantı ve Proje Seçimi */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Jira Bağlantısı
            </label>
            <select
              value={selectedConnection?.id || ''}
              onChange={(e) => {
                const connection = connections.find(c => c.id === parseInt(e.target.value));
                setSelectedConnection(connection || null);
                setSelectedProject(null);
              }}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Bağlantı seçin...</option>
              {connections.map(connection => (
                <option key={connection.id} value={connection.id}>
                  {connection.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Jira Projesi
            </label>
            <select
              value={selectedProject?.key || ''}
              onChange={(e) => {
                const project = jiraProjects.find(p => p.key === e.target.value);
                setSelectedProject(project || null);
              }}
              disabled={!selectedConnection}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
            >
              <option value="">Proje seçin...</option>
              {jiraProjects.map(project => (
                <option key={project.key} value={project.key}>
                  {project.name} ({project.key})
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Bot Komutları */}
        {selectedProject && (
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700">
                Bot Komutları
              </label>
              <div className="text-xs text-gray-500">
                {analysisCount}/3 analiz kullanıldı (5 dakikada)
              </div>
            </div>

            {rateLimitMessage && (
              <div className="mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="flex items-center gap-2">
                  <AlertCircle size={16} className="text-yellow-600" />
                  <span className="text-sm text-yellow-800">{rateLimitMessage}</span>
                </div>
              </div>
            )}

            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {BOT_COMMANDS.map(cmd => (
                <Button
                  key={cmd.command}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2 justify-start h-auto p-3"
                  onClick={() => setBotCommand(cmd.command)}
                  disabled={isProcessing || (analysisCount >= 3 && !checkRateLimit())}
                >
                  {cmd.icon}
                  <div className="text-left">
                    <div className="text-xs font-medium">{cmd.command}</div>
                    <div className="text-xs text-gray-500">{cmd.description}</div>
                  </div>
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Webhook Setup */}
        {selectedConnection && (
          <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <Zap size={20} className="text-blue-600" />
              <h3 className="font-medium text-blue-900">Otomatik Bot Tetikleme</h3>
            </div>

            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-blue-800 mb-1">
                  Webhook URL (Jira'ya ekleyin):
                </label>
                <div className="flex items-center gap-2">
                  <Input
                    type="text"
                    value={`${window.location.origin}/api/integrations/jira/webhook`}
                    readOnly
                    className="bg-white text-sm"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      navigator.clipboard.writeText(`${window.location.origin}/api/integrations/jira/webhook`);
                      toast({
                        title: 'Kopyalandı',
                        description: 'Webhook URL panoya kopyalandı.',
                      });
                    }}
                  >
                    <Download size={16} />
                  </Button>
                </div>
              </div>

              <div className="text-sm text-blue-700">
                <p className="font-medium mb-2">🚀 Nasıl Kullanılır:</p>
                <ol className="list-decimal list-inside space-y-1 text-xs">
                  <li>Yukarıdaki URL'yi Jira webhook ayarlarına ekleyin</li>
                  <li>Event türü olarak "Issue commented" seçin</li>
                  <li>Jira issue'larında comment olarak bot komutlarını yazın:</li>
                </ol>

                <div className="mt-2 p-2 bg-blue-100 rounded text-xs">
                  <p className="font-medium">Örnek kullanım:</p>
                  <code className="block mt-1">@ai-bot scenario</code>
                  <code className="block">@ai-bot security</code>
                  <code className="block">@ai-bot performance</code>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {!selectedConnection ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <Bot size={64} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Jira Bağlantısı Seçin
              </h3>
              <p className="text-gray-500">
                Bot'u kullanmaya başlamak için bir Jira bağlantısı seçin
              </p>
            </div>
          </div>
        ) : !selectedProject ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <Bot size={64} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Proje Seçin
              </h3>
              <p className="text-gray-500">
                Analiz yapmak için bir proje seçin
              </p>
            </div>
          </div>
        ) : (
          <div className="h-full flex">
            {/* Issue Listesi */}
            <div className="w-1/2 border-r border-gray-200 flex flex-col">
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-center gap-2 mb-3">
                  <h3 className="font-medium text-gray-900">Issue Listesi</h3>
                  <Badge variant="secondary">{filteredIssues.length}</Badge>
                </div>

                <div className="relative">
                  <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <Input
                    type="text"
                    placeholder="Issue ara..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <ScrollArea className="flex-1">
                <div className="p-4 space-y-3">
                  {issuesLoading ? (
                    <div className="text-center py-8">
                      <RefreshCw size={32} className="mx-auto animate-spin text-gray-400 mb-2" />
                      <p className="text-gray-500">Issue'lar yükleniyor...</p>
                    </div>
                  ) : filteredIssues.length === 0 ? (
                    <div className="text-center py-8">
                      <FileText size={32} className="mx-auto text-gray-400 mb-2" />
                      <p className="text-gray-500">Issue bulunamadı</p>
                    </div>
                  ) : (
                    filteredIssues.map(issue => (
                      <div
                        key={issue.id}
                        className={`border rounded-lg p-3 cursor-pointer transition-colors ${selectedIssue?.id === issue.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                          }`}
                        onClick={() => setSelectedIssue(issue)}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <span className="text-sm font-medium text-blue-600">
                            {issue.key}
                          </span>
                          <Badge variant="secondary" className="text-xs">
                            {issue.issueType}
                          </Badge>
                        </div>

                        <h4 className="text-sm font-medium text-gray-900 mb-1 line-clamp-2">
                          {issue.summary}
                        </h4>

                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <span>{issue.status}</span>
                          <span>•</span>
                          <span>{issue.priority}</span>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </div>

            {/* Bot Analiz Paneli */}
            <div className="w-1/2 flex flex-col">
              {selectedIssue ? (
                <>
                  {/* Issue Detayları */}
                  <div className="p-4 border-b border-gray-200">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="font-medium text-gray-900">{selectedIssue.key}</h3>
                        <p className="text-sm text-gray-600 mt-1">{selectedIssue.summary}</p>
                      </div>
                      <Badge variant="secondary">{selectedIssue.issueType}</Badge>
                    </div>

                    {/* Bot Komut Çalıştırma */}
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Input
                          type="text"
                          placeholder="Bot komutunu girin (örn: @ai-bot scenario)"
                          value={botCommand}
                          onChange={(e) => setBotCommand(e.target.value)}
                          className="flex-1"
                        />
                        <Button
                          size="sm"
                          onClick={() => executeBotCommand(selectedIssue, botCommand)}
                          disabled={!botCommand.trim() || isProcessing}
                        >
                          {isProcessing ? (
                            <RefreshCw size={16} className="animate-spin" />
                          ) : (
                            <Play size={16} />
                          )}
                        </Button>
                      </div>

                      <div className="flex flex-wrap gap-1">
                        {BOT_COMMANDS.map(cmd => (
                          <Button
                            key={cmd.command}
                            variant="ghost"
                            size="sm"
                            className="h-6 px-2 text-xs"
                            onClick={() => setBotCommand(cmd.command)}
                          >
                            {cmd.command}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Analiz Sonuçları */}
                  <div className="flex-1 overflow-hidden">
                    <div className="p-4 border-b border-gray-200">
                      <h4 className="font-medium text-gray-900">Analiz Sonuçları</h4>
                    </div>

                    <ScrollArea className="flex-1">
                      <div className="p-4 space-y-4">
                        {analysisResults.length === 0 ? (
                          <div className="text-center py-8">
                            <Bot size={32} className="mx-auto text-gray-400 mb-2" />
                            <p className="text-gray-500">Henüz analiz yapılmadı</p>
                            <p className="text-xs text-gray-400 mt-1">
                              Bir bot komutu çalıştırın
                            </p>
                          </div>
                        ) : (
                          analysisResults.map((result, index) => (
                            <div
                              key={index}
                              className={`border rounded-lg p-4 ${getAnalysisColor(result.type)}`}
                            >
                              <div className="flex items-start gap-3 mb-3">
                                {getAnalysisIcon(result.type)}
                                <div className="flex-1">
                                  <h5 className="font-medium text-sm">{result.title}</h5>
                                  <p className="text-xs opacity-75 mt-1">
                                    {new Date(result.timestamp).toLocaleString('tr-TR')}
                                  </p>
                                </div>
                              </div>

                              <div
                                className="text-sm prose prose-sm max-w-none"
                                dangerouslySetInnerHTML={{
                                  __html: formatAnalysisContent(result.content)
                                }}
                              />

                              {result.metadata && (
                                <div className="mt-3 pt-3 border-t border-current border-opacity-20">
                                  <details className="text-xs">
                                    <summary className="cursor-pointer font-medium">
                                      Metadata
                                    </summary>
                                    <pre className="mt-2 bg-black bg-opacity-10 p-2 rounded text-xs overflow-auto">
                                      {JSON.stringify(result.metadata, null, 2)}
                                    </pre>
                                  </details>
                                </div>
                              )}
                            </div>
                          ))
                        )}
                      </div>
                    </ScrollArea>
                  </div>
                </>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <Target size={64} className="mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Issue Seçin
                    </h3>
                    <p className="text-gray-500">
                      Analiz yapmak için bir issue seçin
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="border-t border-gray-200 p-4 bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center gap-4">
            <span>Bot Durumu: {autoMode ? 'Otomatik' : 'Manuel'}</span>
            <span>•</span>
            <span>Toplam Analiz: {analysisResults.length}</span>
          </div>

          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm">
              <Download size={16} className="mr-2" />
              Dışa Aktar
            </Button>
            <Button variant="ghost" size="sm">
              <Upload size={16} className="mr-2" />
              İçe Aktar
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
