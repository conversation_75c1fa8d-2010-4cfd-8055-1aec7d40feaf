import React, { use<PERSON>emo } from 'react';
import { Scroll<PERSON><PERSON> } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Plus,
  User,
  MessageSquare,
  Calendar,
  Flag,
  CheckCircle,
  Circle,
  Clock,
  AlertCircle
} from 'lucide-react';

interface JiraIssue {
  id: string;
  key: string;
  summary: string;
  description: string;
  issueType: string;
  status: string;
  priority: string;
  assignee?: string;
  reporter?: string;
  created: string;
  updated: string;
  comments: Array<{
    id: string;
    author: string;
    body: string;
    created: string;
  }>;
}

interface KanbanViewProps {
  issues: JiraIssue[];
  onIssueClick: (issue: JiraIssue) => void;
  getPriorityBadge: (priority: string) => React.ReactNode;
  getStatusBadge: (status: string) => React.ReactNode;
  getTypeBadge: (type: string) => React.ReactNode;
}

const KANBAN_COLUMNS = [
  {
    id: 'To Do',
    title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    color: 'bg-gray-100',
    icon: <Circle size={16} className="text-gray-500" />
  },
  {
    id: 'In Progress',
    title: 'Devam Ediyor',
    color: 'bg-yellow-100',
    icon: <Clock size={16} className="text-yellow-600" />
  },
  {
    id: 'In Review',
    title: 'İncelemede',
    color: 'bg-blue-100',
    icon: <AlertCircle size={16} className="text-blue-600" />
  },
  {
    id: 'Done',
    title: 'Tamamlandı',
    color: 'bg-green-100',
    icon: <CheckCircle size={16} className="text-green-600" />
  },
  {
    id: 'Blocked',
    title: 'Engellendi',
    color: 'bg-red-100',
    icon: <Flag size={16} className="text-red-600" />
  }
];

export default function KanbanView({
  issues,
  onIssueClick,
  getPriorityBadge,
  getStatusBadge,
  getTypeBadge
}: KanbanViewProps) {

  // Issues'ları status'a göre grupla
  const groupedIssues = useMemo(() => {
    const groups: Record<string, JiraIssue[]> = {};

    // Tüm kolonları başlat
    KANBAN_COLUMNS.forEach(column => {
      groups[column.id] = [];
    });

    // Issues'ları gruplara dağıt
    issues.forEach(issue => {
      const status = issue.status;
      if (groups[status]) {
        groups[status].push(issue);
      } else {
        // Bilinmeyen status için "To Do" kolonuna ekle
        groups['To Do'].push(issue);
      }
    });

    return groups;
  }, [issues]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Bugün';
    if (diffDays === 2) return 'Dün';
    if (diffDays <= 7) return `${diffDays} gün önce`;

    return date.toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit'
    });
  };

  const IssueCard = ({ issue }: { issue: JiraIssue }) => (
    <div
      className="bg-white border border-gray-200 rounded-lg p-3 mb-3 shadow-sm hover:shadow-md transition-shadow cursor-pointer group"
      onClick={() => onIssueClick(issue)}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2">
          {getTypeBadge(issue.issueType)}
          <span className="text-xs font-medium text-blue-600">
            {issue.key}
          </span>
        </div>
        {getPriorityBadge(issue.priority)}
      </div>

      {/* Title */}
      <h4 className="text-sm font-medium text-gray-900 mb-2" style={{
        display: '-webkit-box',
        WebkitLineClamp: 2,
        WebkitBoxOrient: 'vertical',
        overflow: 'hidden'
      }}>
        {issue.summary}
      </h4>

      {/* Description */}
      {issue.description && (
        <p className="text-xs text-gray-600 mb-3" style={{
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden'
        }}>
          {issue.description}
        </p>
      )}

      {/* Footer */}
      <div className="flex items-center justify-between text-xs text-gray-500">
        <div className="flex items-center gap-3">
          {/* Assignee */}
          <div className="flex items-center gap-1">
            <User size={12} />
            <span>{issue.assignee || 'Atanmamış'}</span>
          </div>

          {/* Comments */}
          {issue.comments.length > 0 && (
            <div className="flex items-center gap-1">
              <MessageSquare size={12} />
              <span>{issue.comments.length}</span>
            </div>
          )}
        </div>

        {/* Updated date */}
        <div className="flex items-center gap-1">
          <Calendar size={12} />
          <span>{formatDate(issue.updated)}</span>
        </div>
      </div>
    </div>
  );

  return (
    <div className="h-full flex flex-col">
      {/* Kanban Board */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full flex gap-4 p-4">
          {KANBAN_COLUMNS.map(column => {
            const columnIssues = groupedIssues[column.id] || [];

            return (
              <div key={column.id} className="flex-1 min-w-80 flex flex-col">
                {/* Column Header */}
                <div className={`${column.color} rounded-lg p-3 mb-4`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {column.icon}
                      <h3 className="font-medium text-gray-900">
                        {column.title}
                      </h3>
                      <Badge variant="secondary" className="bg-white text-gray-600">
                        {columnIssues.length}
                      </Badge>
                    </div>

                    <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                      <Plus size={14} />
                    </Button>
                  </div>
                </div>

                {/* Column Content */}
                <ScrollArea className="flex-1">
                  <div className="pr-2">
                    {columnIssues.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <div className="text-gray-300 mb-2">
                          {column.icon}
                        </div>
                        <p className="text-sm">Bu kolonda issue yok</p>
                      </div>
                    ) : (
                      columnIssues.map(issue => (
                        <IssueCard key={issue.id} issue={issue} />
                      ))
                    )}
                  </div>
                </ScrollArea>
              </div>
            );
          })}
        </div>
      </div>

      {/* Footer */}
      <div className="border-t border-gray-200 p-4 bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>Toplam {issues.length} issue</span>
          <div className="flex items-center gap-4">
            {KANBAN_COLUMNS.map(column => {
              const count = groupedIssues[column.id]?.length || 0;
              return (
                <div key={column.id} className="flex items-center gap-2">
                  {column.icon}
                  <span>{column.title}: {count}</span>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
