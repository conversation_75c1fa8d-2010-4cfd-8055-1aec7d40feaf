chunk-RPCDYKBN.js?v=f3f23686:521 Warning: validateDOMNesting(...): <div> cannot appear as a descendant of <p>.
    at div
    at Badge (https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/src/components/ui/badge.tsx:35:18)
    at p
    at _c7 (https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/src/components/ui/card.tsx:92:12)
    at div
    at _c3 (https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/src/components/ui/card.tsx:45:12)
    at div
    at _c (https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/src/components/ui/card.tsx:20:11)
    at div
    at div
    at https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-ABBTHC7K.js?v=f3f23686:191:13
    at Presence (https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-CJLRRE66.js?v=f3f23686:24:11)
    at https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=f3f23686:169:13
    at _c5 (https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/src/components/ui/tabs.tsx:72:12)
    at div
    at https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-ABBTHC7K.js?v=f3f23686:191:13
    at Provider (https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-ABBTHC7K.js?v=f3f23686:44:15)
    at https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=f3f23686:47:7
    at div
    at div
    at TestScenarios (https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/src/components/test-scenarios.tsx:30:26)
    at main
    at div
    at div
    at Dashboard (https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/src/pages/dashboard.tsx:30:37)
    at Route (https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=f3f23686:323:16)
    at Switch (https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=f3f23686:379:17)
    at Router
    at QueryClientProvider (https://e4c0d600-bdb6-4257-8afe-53181b5e38b4-00-25vauwxx3hczw.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@tanstack_react-query.js?v=f3f23686:2805:3)
    at App