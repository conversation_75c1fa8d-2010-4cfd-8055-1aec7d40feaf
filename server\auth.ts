import passport from "passport";
import { Strategy as LocalStrategy } from "passport-local";
import { Express } from "express";
import session from "express-session";
import { scrypt, randomBytes, timingSafeEqual } from "crypto";
import { promisify } from "util";
import { storage } from "./storage";
import { User as SelectUser } from "@shared/schema";
import MemoryStore from "memorystore";

declare global {
  namespace Express {
    interface User extends SelectUser {}
  }
}

const scryptAsync = promisify(scrypt);

export async function hashPassword(password: string) {
  const salt = randomBytes(16).toString("hex");
  const buf = (await scryptAsync(password, salt, 64)) as Buffer;
  return `${buf.toString("hex")}.${salt}`;
}

export async function comparePasswords(supplied: string, stored: string) {
  const [hashed, salt] = stored.split(".");
  const hashedBuf = Buffer.from(hashed, "hex");
  const suppliedBuf = (await scryptAsync(supplied, salt, 64)) as Buffer;
  return timingSafeEqual(hashedBuf, suppliedBuf);
}

export function setupAuth(app: Express) {
  const MemorySessionStore = MemoryStore(session);
  const sessionSettings: session.SessionOptions = {
    secret: process.env.SESSION_SECRET || "dokuman-analiz-gizli-anahtar",
    resave: false,
    saveUninitialized: false,
    store: new MemorySessionStore({
      checkPeriod: 86400000 // Her 24 saatte bir만 지남 항목 삭제
    }),
    cookie: {
      maxAge: 24 * 60 * 60 * 1000, // 24 saat
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax"
    }
  };

  app.set("trust proxy", 1);
  app.use(session(sessionSettings));
  app.use(passport.initialize());
  app.use(passport.session());

  passport.use(
    new LocalStrategy(async (username, password, done) => {
      try {
        const user = await storage.getUserByUsername(username);
        // Kullanıcı yok veya şifre hatalı ise
        if (!user || !(await comparePasswords(password, user.password))) {
          return done(null, false, { message: "Kullanıcı adı veya şifre hatalı" });
        } 
        // Kullanıcı deaktif edilmiş ise
        else if (user.status === 'inactive') {
          return done(null, false, { message: "Bu hesap devre dışı bırakılmıştır. Lütfen yönetici ile iletişime geçin." });
        } 
        // Kullanıcı aktif ve şifre doğru ise
        else {
          return done(null, user);
        }
      } catch (error) {
        console.error("Passport doğrulama hatası:", error);
        return done(error);
      }
    }),
  );

  passport.serializeUser((user, done) => done(null, user.id));
  passport.deserializeUser(async (id: number, done) => {
    try {
      const user = await storage.getUser(id);
      
      // Kullanıcı bulunamadı veya deaktif edilmişse oturumu sonlandır
      if (!user || user.status === 'inactive') {
        return done(null, false);
      }
      
      done(null, user);
    } catch (error) {
      done(error, null);
    }
  });

  // Kullanıcı kayıt API endpoint'i
  app.post("/api/register", async (req, res, next) => {
    try {
      // Aynı kullanıcı adı ile kayıt var mı kontrol et
      const existingUser = await storage.getUserByUsername(req.body.username);
      if (existingUser) {
        return res.status(400).json({ error: "Bu kullanıcı adı zaten kayıtlı" });
      }

      // Şifreyi hashle ve kullanıcıyı oluştur
      const hashedPassword = await hashPassword(req.body.password);
      const user = await storage.createUser({
        ...req.body,
        password: hashedPassword,
      });

      // Doğrudan login yap
      req.login(user, (err) => {
        if (err) return next(err);
        res.status(201).json(user);
      });
    } catch (error) {
      next(error);
    }
  });

  // Login API endpoint'i
  app.post("/api/login", (req, res, next) => {
    passport.authenticate("local", (err, user, info) => {
      if (err) return next(err);
      if (!user) {
        return res.status(401).json({ error: info.message || "Giriş başarısız" });
      }
      req.login(user, (err) => {
        if (err) return next(err);
        return res.status(200).json(user);
      });
    })(req, res, next);
  });

  // Logout API endpoint'i
  app.post("/api/logout", (req, res, next) => {
    req.logout((err) => {
      if (err) return next(err);
      res.sendStatus(200);
    });
  });

  // Mevcut kullanıcı bilgisini al
  app.get("/api/user", (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ error: "Oturum açılmamış" });
    }
    res.json(req.user);
  });
}

// Oturum kontrolü için middleware
export function isAuthenticated(req: Express.Request, res: Express.Response, next: Express.NextFunction) {
  if (req.isAuthenticated()) {
    // Kullanıcı aktif mi kontrol et
    if (req.user.status === 'inactive') {
      req.logout((err) => {
        if (err) console.error('Logout sırasında hata oluştu:', err);
        return res.status(403).json({ error: "Hesabınız devre dışı bırakılmıştır. Lütfen yönetici ile iletişime geçin." });
      });
    } else {
      return next();
    }
  } else {
    res.status(401).json({ error: "Bu işlem için oturum açmanız gerekiyor" });
  }
}

// Admin kontrolü için middleware
export function isAdmin(req: Express.Request, res: Express.Response, next: Express.NextFunction) {
  if (req.isAuthenticated() && req.user.role === "admin") {
    return next();
  }
  res.status(403).json({ error: "Bu işlem için yönetici yetkileri gerekiyor" });
}