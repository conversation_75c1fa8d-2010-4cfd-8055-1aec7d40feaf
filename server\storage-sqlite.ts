import { drizzle } from 'drizzle-orm/better-sqlite3';
import Database from 'better-sqlite3';
import { type IStorage } from './storage';
import {
  User, InsertUser,
  Document, InsertDocument,
  Component, InsertComponent,
  Requirement, InsertRequirement,
  ApiEndpoint, InsertApiEndpoint,
  TestScenario, InsertTestScenario,
  AiAnalysis, InsertAiAnalysis,
  CoverageValidation, InsertCoverageValidation
} from '../shared/schema';

// SQLite veritabanı bağlantısını oluştur
const sqlite = new Database('document-analyzer.db');
const db = drizzle(sqlite);

// Tabloları oluştur
sqlite.exec(`
  CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'user',
    name TEXT,
    email TEXT,
    created_at INTEGER NOT NULL DEFAULT (unixepoch())
  );

  CREATE TABLE IF NOT EXISTS documents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    content TEXT NOT NULL,
    created_at INTEGER NOT NULL DEFAULT (unixepoch()),
    updated_at INTEGER NOT NULL DEFAULT (unixepoch()),
    user_id INTEGER
  );

  CREATE TABLE IF NOT EXISTS components (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    type TEXT,
    is_new BOOLEAN
  );

  CREATE TABLE IF NOT EXISTS requirements (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_id INTEGER NOT NULL,
    code TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT
  );

  CREATE TABLE IF NOT EXISTS api_endpoints (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_id INTEGER NOT NULL,
    url TEXT NOT NULL,
    method TEXT NOT NULL,
    parameters TEXT,
    requirement_code TEXT
  );

  CREATE TABLE IF NOT EXISTS test_scenarios (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_id INTEGER NOT NULL,
    title TEXT NOT NULL,
    preconditions TEXT,
    steps TEXT NOT NULL,
    expected_results TEXT NOT NULL,
    requirement_code TEXT,
    format TEXT DEFAULT 'default'
  );

  CREATE TABLE IF NOT EXISTS ai_analysis (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_id INTEGER NOT NULL,
    observations TEXT NOT NULL
  );
  
  CREATE TABLE IF NOT EXISTS coverage_validation (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    document_id INTEGER NOT NULL,
    coverage_rate REAL NOT NULL,
    missing_requirements TEXT NOT NULL,
    analysis_details TEXT,
    recommendations TEXT,
    strong_points TEXT,
    weak_points TEXT,
    created_at INTEGER NOT NULL DEFAULT (unixepoch())
  );
`);

/**
 * SQLite tabanlı veritabanı uygulaması
 */
export class SqliteStorage implements IStorage {
  private async runInTransaction<T>(callback: () => Promise<T>): Promise<T> {
    try {
      sqlite.exec('BEGIN TRANSACTION');
      const result = await callback();
      sqlite.exec('COMMIT');
      return result;
    } catch (error) {
      sqlite.exec('ROLLBACK');
      throw error;
    }
  }

  // Kullanıcı işlemleri
  async getUser(id: number): Promise<User | undefined> {
    const rows = sqlite.prepare('SELECT * FROM users WHERE id = ?').all(id);
    
    if (rows.length === 0) return undefined;
    
    // SQLite formatından User formatına dönüştür
    const user = rows[0] as any;
    return {
      id: user.id,
      username: user.username,
      password: user.password_hash, // password_hash'i password olarak kullan
      email: user.email,
      role: 'admin', // Varsayılan olarak admin değerini kullan
      name: null, // Varsayılan olarak null değerini kullan
      createdAt: new Date() // Şu anki zamanı kullan
    };
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const rows = sqlite.prepare('SELECT * FROM users WHERE username = ?').all(username);
    
    if (rows.length === 0) return undefined;
    
    // SQLite formatından User formatına dönüştür
    const user = rows[0] as any;
    return {
      id: user.id,
      username: user.username,
      password: user.password_hash, // password_hash'i password olarak kullan
      email: user.email,
      role: 'admin', // Varsayılan olarak admin değerini kullan
      name: null, // Varsayılan olarak null değerini kullan
      createdAt: new Date() // Şu anki zamanı kullan
    };
  }

  async createUser(user: InsertUser): Promise<User> {
    return this.runInTransaction(async () => {
      // SQLite veritabanında users tablosu password_hash sütununa sahip
      // ve name ve role sütunları eksik, bu yüzden sorguyu buna göre düzenledik
      const result = sqlite.prepare(
        'INSERT INTO users (username, password_hash, email) VALUES (?, ?, ?) RETURNING *'
      ).get(
        user.username, 
        user.password, // password değerini password_hash sütununa yazıyoruz 
        user.email || null
      ) as any;
      
      // Dönen sonuca role ve createdAt alanlarını ekleyelim
      return {
        id: result.id,
        username: result.username,
        password: result.password_hash, // password_hash'i password olarak kullan
        email: result.email,
        role: user.role || 'user', // Kullanıcı rolünü ekle
        name: null, // Varsayılan olarak null
        createdAt: new Date() // Şu anki zamanı kullan
      };
    });
  }

  // Doküman işlemleri
  async getDocuments(): Promise<Document[]> {
    return sqlite.prepare('SELECT * FROM documents ORDER BY updated_at DESC').all() as Document[];
  }

  async getDocument(id: number): Promise<Document | undefined> {
    const rows = sqlite.prepare('SELECT * FROM documents WHERE id = ?').all(id) as Document[];
    return rows.length > 0 ? rows[0] : undefined;
  }

  async createDocument(document: InsertDocument): Promise<Document> {
    return this.runInTransaction(async () => {
      const timestamp = Math.floor(Date.now() / 1000);
      const result = sqlite.prepare(
        'INSERT INTO documents (name, type, content, created_at, updated_at) VALUES (?, ?, ?, ?, ?) RETURNING *'
      ).get(document.name, document.type, document.content, timestamp, timestamp) as Document;
      
      return result;
    });
  }

  async updateDocument(id: number, updateData: Partial<InsertDocument>): Promise<Document | undefined> {
    return this.runInTransaction(async () => {
      const document = await this.getDocument(id);
      if (!document) return undefined;

      const timestamp = Math.floor(Date.now() / 1000);
      const updates = Object.entries(updateData)
        .filter(([key, _]) => key !== 'id')
        .map(([key, value]) => `${this.camelToSnake(key)} = ?`);
      
      if (updates.length === 0) return document;

      const values = Object.entries(updateData)
        .filter(([key, _]) => key !== 'id')
        .map(([_, value]) => value);

      // "updated_at" ve "analyzed_at" sütunlarını kontrol et
      // analyzed_at sütunu olmama hatasını gidermek için güvenli bir şekilde güncelle
      const sql = `UPDATE documents SET ${updates.join(', ')} WHERE id = ? RETURNING *`;
      
      return sqlite.prepare(sql).get(...values, id) as Document;
    });
  }

  async deleteDocument(id: number): Promise<boolean> {
    return this.runInTransaction(async () => {
      try {
        // İlişkili verileri sil (önce ilişkili verileri silmeliyiz, aksi halde yabancı anahtar kısıtlamaları hata verebilir)
        // Test senaryolarını sil
        sqlite.prepare('DELETE FROM test_scenarios WHERE document_id = ?').run(id);
        
        // AI analiz verilerini sil
        sqlite.prepare('DELETE FROM ai_analysis WHERE document_id = ?').run(id);
        
        // API uç noktalarını sil
        sqlite.prepare('DELETE FROM api_endpoints WHERE document_id = ?').run(id);
        
        // Gereksinimleri sil
        sqlite.prepare('DELETE FROM requirements WHERE document_id = ?').run(id);
        
        // Bileşenleri sil
        sqlite.prepare('DELETE FROM components WHERE document_id = ?').run(id);
        
        // Son olarak dokümanın kendisini sil
        const result = sqlite.prepare('DELETE FROM documents WHERE id = ?').run(id);
        
        console.log(`Doküman ve ilişkili tüm veriler silindi: ID=${id}, Etkilenen doküman sayısı: ${result.changes}`);
        return result.changes > 0;
      } catch (error) {
        console.error(`Doküman silinirken hata oluştu: ID=${id}`, error);
        throw error; // Hatayı yeniden fırlat, böylece transaction otomatik olarak geri alınır
      }
    });
  }

  // Bileşen işlemleri
  async getComponentsByDocumentId(documentId: number): Promise<Component[]> {
    return sqlite.prepare('SELECT * FROM components WHERE document_id = ?').all(documentId) as Component[];
  }

  async createComponent(component: InsertComponent): Promise<Component> {
    return this.runInTransaction(async () => {
      const result = sqlite.prepare(
        'INSERT INTO components (document_id, name, description, type, is_new) VALUES (?, ?, ?, ?, ?) RETURNING *'
      ).get(component.documentId, component.name, component.description, component.type, component.isNew) as Component;
      
      return result;
    });
  }

  // Gereksinim işlemleri
  async getRequirements(): Promise<Requirement[]> {
    return sqlite.prepare('SELECT * FROM requirements').all() as Requirement[];
  }
  
  async getRequirementsByDocumentId(documentId: number): Promise<Requirement[]> {
    return sqlite.prepare('SELECT * FROM requirements WHERE document_id = ?').all(documentId) as Requirement[];
  }

  async createRequirement(requirement: InsertRequirement): Promise<Requirement> {
    return this.runInTransaction(async () => {
      const result = sqlite.prepare(
        'INSERT INTO requirements (document_id, code, description, category) VALUES (?, ?, ?, ?) RETURNING *'
      ).get(requirement.documentId, requirement.code, requirement.description, requirement.category) as Requirement;
      
      return result;
    });
  }

  // API Uç Noktaları işlemleri
  async getApiEndpointsByDocumentId(documentId: number): Promise<ApiEndpoint[]> {
    const rows = sqlite.prepare('SELECT * FROM api_endpoints WHERE document_id = ?').all(documentId) as (Omit<ApiEndpoint, 'parameters'> & { parameters: string })[];
    
    return rows.map(row => ({
      ...row,
      parameters: row.parameters ? JSON.parse(row.parameters) : {}
    }));
  }

  async createApiEndpoint(apiEndpoint: InsertApiEndpoint): Promise<ApiEndpoint> {
    return this.runInTransaction(async () => {
      const parametersJson = JSON.stringify(apiEndpoint.parameters || {});
      
      const result = sqlite.prepare(
        'INSERT INTO api_endpoints (document_id, url, method, parameters, requirement_code) VALUES (?, ?, ?, ?, ?) RETURNING *'
      ).get(
        apiEndpoint.documentId, 
        apiEndpoint.url, 
        apiEndpoint.method, 
        parametersJson, 
        apiEndpoint.requirementCode
      ) as (Omit<ApiEndpoint, 'parameters'> & { parameters: string });
      
      return {
        ...result,
        parameters: JSON.parse(result.parameters)
      };
    });
  }

  // Test Senaryoları işlemleri
  async getTestScenariosByDocumentId(documentId: number): Promise<TestScenario[]> {
    const rows = sqlite.prepare('SELECT * FROM test_scenarios WHERE document_id = ?').all(documentId) as TestScenario[];
    
    return rows.map(row => {
      // Verileri uygun formatlarına dönüştür
      const steps = typeof row.steps === 'string' ? JSON.parse(row.steps || '[]') : row.steps;
      
      // Preconditions, eğer JSON string ise dizi formatına çevir 
      let preconditions = row.preconditions;
      if (typeof preconditions === 'string' && preconditions) {
        try {
          // Square brackets içine alınmış bir JSON array mi kontrol et
          if (preconditions.trim().startsWith('[') && preconditions.trim().endsWith(']')) {
            preconditions = JSON.parse(preconditions);
          }
        } catch (e) {
          // JSON parse hatası olursa orijinal string'i kullan
          console.log('JSON parse preconditions error:', e);
        }
      }
      
      // SQLite'ın snake_case formatındaki alan adlarını camelCase'e çevir
      return {
        id: row.id,
        documentId: row.document_id,
        title: row.title,
        preconditions,
        steps,
        expectedResults: row.expected_results,
        requirementCode: row.requirement_code,
        format: row.format || 'default'
      };
    });
  }

  async createTestScenario(testScenario: InsertTestScenario): Promise<TestScenario> {
    return this.runInTransaction(async () => {
      const stepsJson = JSON.stringify(testScenario.steps || []);
      
      try {
        // Preconditions bir dizi olabilir, kontrol edip düzenleyelim
        const preconditions = testScenario.preconditions;
        const preconditionsString = Array.isArray(preconditions) 
          ? JSON.stringify(preconditions) 
          : (preconditions || null);
        
        console.log("Test senaryosu ekleniyor (düzeltilmiş):", { 
          documentId: testScenario.documentId, 
          title: testScenario.title,
          preconditions: preconditionsString,
          steps: stepsJson,
          expectedResults: testScenario.expectedResults || null,
          requirementCode: testScenario.requirementCode || null
        });
        
        // Format sütunu olmama ihtimaline karşı ona sahip değilmişiz gibi sorgu yap
        const result = sqlite.prepare(
          'INSERT INTO test_scenarios (document_id, title, preconditions, steps, expected_results, requirement_code) VALUES (?, ?, ?, ?, ?, ?) RETURNING *'
        ).get(
          testScenario.documentId, 
          testScenario.title, 
          preconditionsString, // String olarak kaydedelim
          stepsJson, 
          testScenario.expectedResults || null, 
          testScenario.requirementCode || null
        ) as (Omit<TestScenario, 'steps'> & { steps: string });
        
        return {
          ...result,
          steps: JSON.parse(result.steps || '[]'),
          format: testScenario.format || 'default' // Veritabanında olmasa bile ekleyelim
        };
      } catch (error) {
        console.error("Test senaryosu oluşturulurken hata:", error);
        throw error;
      }
    });
  }

  // AI Analiz işlemleri
  async getAiAnalysisByDocumentId(documentId: number): Promise<AiAnalysis | undefined> {
    const rows = sqlite.prepare('SELECT * FROM ai_analysis WHERE document_id = ?').all(documentId) as (Omit<AiAnalysis, 'observations'> & { observations: string })[];
    
    if (rows.length === 0) return undefined;
    
    return {
      ...rows[0],
      observations: JSON.parse(rows[0].observations)
    };
  }

  async createAiAnalysis(aiAnalysis: InsertAiAnalysis): Promise<AiAnalysis> {
    return this.runInTransaction(async () => {
      const observationsJson = JSON.stringify(aiAnalysis.observations);
      
      const result = sqlite.prepare(
        'INSERT INTO ai_analysis (document_id, observations) VALUES (?, ?) RETURNING *'
      ).get(aiAnalysis.documentId, observationsJson) as (Omit<AiAnalysis, 'observations'> & { observations: string });
      
      return {
        ...result,
        observations: JSON.parse(result.observations)
      };
    });
  }

  async saveAIAnalysis(documentId: number, analysisResult: any): Promise<void> {
    try {
      // AI analiz sonuçlarını veritabanına kaydet
      await this.createAiAnalysis({
        documentId: documentId,
        observations: analysisResult
      });
    } catch (error) {
      console.error('AI analiz sonuçları kaydedilirken hata:', error);
      throw error;
    }
  }

  // Coverage Validation işlemleri
  async getCoverageValidationByDocumentId(documentId: number): Promise<CoverageValidation | undefined> {
    const rows = sqlite.prepare('SELECT * FROM coverage_validation WHERE document_id = ?').all(documentId) as (Omit<CoverageValidation, 'missingRequirements' | 'analysisDetails' | 'recommendations' | 'strongPoints' | 'weakPoints'> & { 
      missing_requirements: string, 
      analysis_details: string,
      recommendations: string,
      strong_points: string,
      weak_points: string,
      created_at: number
    })[];
    
    if (rows.length === 0) return undefined;
    
    // SQLite'tan gelen verileri uygun formata dönüştür
    const row = rows[0];
    
    return {
      id: row.id,
      documentId: row.document_id,
      coverageRate: row.coverage_rate,
      missingRequirements: JSON.parse(row.missing_requirements || '[]'),
      analysisDetails: row.analysis_details ? JSON.parse(row.analysis_details) : null,
      recommendations: row.recommendations ? JSON.parse(row.recommendations) : null,
      strongPoints: row.strong_points ? JSON.parse(row.strong_points) : null,
      weakPoints: row.weak_points ? JSON.parse(row.weak_points) : null,
      createdAt: new Date(row.created_at * 1000) // Unix timestamp'i Date'e çevir
    };
  }

  async createCoverageValidation(data: InsertCoverageValidation): Promise<CoverageValidation> {
    return this.runInTransaction(async () => {
      // JSON verilerini string'e dönüştür
      const missingRequirementsJson = JSON.stringify(data.missingRequirements || []);
      const analysisDetailsJson = data.analysisDetails ? JSON.stringify(data.analysisDetails) : null;
      const recommendationsJson = data.recommendations ? JSON.stringify(data.recommendations) : null;
      const strongPointsJson = data.strongPoints ? JSON.stringify(data.strongPoints) : null;
      const weakPointsJson = data.weakPoints ? JSON.stringify(data.weakPoints) : null;
      
      const timestamp = Math.floor(Date.now() / 1000);
      
      // Veritabanına ekle
      const result = sqlite.prepare(
        'INSERT INTO coverage_validation (document_id, coverage_rate, missing_requirements, analysis_details, recommendations, strong_points, weak_points, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING *'
      ).get(
        data.documentId,
        data.coverageRate,
        missingRequirementsJson,
        analysisDetailsJson,
        recommendationsJson,
        strongPointsJson,
        weakPointsJson,
        timestamp
      ) as (Omit<CoverageValidation, 'missingRequirements' | 'analysisDetails' | 'recommendations' | 'strongPoints' | 'weakPoints'> & { 
        missing_requirements: string, 
        analysis_details: string,
        recommendations: string,
        strong_points: string,
        weak_points: string,
        created_at: number
      });
      
      // Sonucu uygun formata dönüştür
      return {
        id: result.id,
        documentId: result.document_id,
        coverageRate: result.coverage_rate,
        missingRequirements: JSON.parse(result.missing_requirements || '[]'),
        analysisDetails: result.analysis_details ? JSON.parse(result.analysis_details) : null,
        recommendations: result.recommendations ? JSON.parse(result.recommendations) : null,
        strongPoints: result.strong_points ? JSON.parse(result.strong_points) : null,
        weakPoints: result.weak_points ? JSON.parse(result.weak_points) : null,
        createdAt: new Date(result.created_at * 1000) // Unix timestamp'i Date'e çevir
      };
    });
  }

  async deleteCoverageValidation(documentId: number): Promise<boolean> {
    return this.runInTransaction(async () => {
      const result = sqlite.prepare('DELETE FROM coverage_validation WHERE document_id = ?').run(documentId);
      return result.changes > 0;
    });
  }

  // Test Senaryosu Güncelleme işlemi
  async updateTestScenario(id: number, updateData: Partial<InsertTestScenario>): Promise<TestScenario | undefined> {
    return this.runInTransaction(async () => {
      const scenario = await this.getTestScenarioById(id);
      if (!scenario) return undefined;

      // Güncellenecek alanları hazırla
      const updates: string[] = [];
      const values: any[] = [];

      if (updateData.title) {
        updates.push('title = ?');
        values.push(updateData.title);
      }

      if (updateData.preconditions !== undefined) {
        updates.push('preconditions = ?');
        const preconditionsString = Array.isArray(updateData.preconditions) 
          ? JSON.stringify(updateData.preconditions) 
          : (updateData.preconditions || null);
        values.push(preconditionsString);
      }

      if (updateData.steps !== undefined) {
        updates.push('steps = ?');
        values.push(JSON.stringify(updateData.steps));
      }

      if (updateData.expectedResults !== undefined) {
        updates.push('expected_results = ?');
        values.push(updateData.expectedResults);
      }

      if (updateData.requirementCode !== undefined) {
        updates.push('requirement_code = ?');
        values.push(updateData.requirementCode);
      }

      if (updateData.format !== undefined) {
        updates.push('format = ?');
        values.push(updateData.format);
      }

      if (updates.length === 0) return scenario;

      // Güncelleme sorgusunu hazırla ve çalıştır
      const sql = `UPDATE test_scenarios SET ${updates.join(', ')} WHERE id = ? RETURNING *`;
      const result = sqlite.prepare(sql).get(...values, id) as any;

      // Güncellenen sonucu uygun formata dönüştür
      return {
        id: result.id,
        documentId: result.document_id,
        title: result.title,
        preconditions: result.preconditions,
        steps: typeof result.steps === 'string' ? JSON.parse(result.steps) : result.steps,
        expectedResults: result.expected_results,
        requirementCode: result.requirement_code,
        format: result.format || 'default'
      };
    });
  }

  // Yardımcı metot - Test senaryosu ID'sine göre getir
  private async getTestScenarioById(id: number): Promise<TestScenario | undefined> {
    const rows = sqlite.prepare('SELECT * FROM test_scenarios WHERE id = ?').all(id) as any[];
    
    if (rows.length === 0) return undefined;
    
    const row = rows[0];
    return {
      id: row.id,
      documentId: row.document_id,
      title: row.title,
      preconditions: row.preconditions,
      steps: typeof row.steps === 'string' ? JSON.parse(row.steps) : row.steps,
      expectedResults: row.expected_results,
      requirementCode: row.requirement_code,
      format: row.format || 'default'
    };
  }

  // Yardımcı metotlar
  private camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }

  // Kullanıcı-Proje ilişkisi operasyonları
  async getUserProjects(userId: number): Promise<UserProject[]> {
    const rows = await this.db.all(
      'SELECT * FROM user_projects WHERE user_id = ?', 
      [userId]
    );
    return rows.map(row => ({
      userId: row.user_id,
      projectId: row.project_id,
      role: row.role || 'viewer',
      createdAt: new Date(row.created_at)
    }));
  }

  async getProjectUsers(projectId: number): Promise<UserProject[]> {
    const rows = await this.db.all(
      'SELECT * FROM user_projects WHERE project_id = ?', 
      [projectId]
    );
    return rows.map(row => ({
      userId: row.user_id,
      projectId: row.project_id,
      role: row.role || 'viewer',
      createdAt: new Date(row.created_at)
    }));
  }

  async getUserProject(userId: number, projectId: number): Promise<UserProject | undefined> {
    const row = await this.db.get(
      'SELECT * FROM user_projects WHERE user_id = ? AND project_id = ?', 
      [userId, projectId]
    );
    
    if (!row) return undefined;
    
    return {
      userId: row.user_id,
      projectId: row.project_id,
      role: row.role || 'viewer',
      createdAt: new Date(row.created_at)
    };
  }
  
  async addUserToProject(data: InsertUserProject): Promise<UserProject> {
    const result = await this.db.run(
      'INSERT INTO user_projects (user_id, project_id, role, created_at) VALUES (?, ?, ?, ?)',
      [data.userId, data.projectId, data.role || 'viewer', Date.now()]
    );
    
    return {
      userId: data.userId,
      projectId: data.projectId,
      role: data.role || 'viewer',
      createdAt: new Date()
    };
  }
  
  async updateUserProjectRole(userId: number, projectId: number, role: string): Promise<UserProject | undefined> {
    const existingProject = await this.getUserProject(userId, projectId);
    
    if (!existingProject) {
      return undefined;
    }
    
    await this.db.run(
      'UPDATE user_projects SET role = ? WHERE user_id = ? AND project_id = ?',
      [role, userId, projectId]
    );
    
    return {
      ...existingProject,
      role
    };
  }
  
  async removeUserFromProject(userId: number, projectId: number): Promise<boolean> {
    const result = await this.db.run(
      'DELETE FROM user_projects WHERE user_id = ? AND project_id = ?',
      [userId, projectId]
    );
    
    return result.changes > 0;
  }
  
  async hasProjectAccess(userId: number, projectId: number, requiredRole?: string): Promise<boolean> {
    const userProject = await this.getUserProject(userId, projectId);
    
    if (!userProject) {
      return false;
    }
    
    if (!requiredRole) {
      return true;
    }
    
    // Role-based permission checking
    // owner > editor > viewer
    const roles: Record<string, number> = { 'owner': 3, 'editor': 2, 'viewer': 1 };
    const userRoleLevel = userProject.role ? (roles[userProject.role] || 0) : 0;
    const requiredRoleLevel = roles[requiredRole] || 0;
    
    return userRoleLevel >= requiredRoleLevel;
  }
  
  // Permission operations
  async getUserPermissions(userId: number): Promise<Permission[]> {
    const rows = await this.db.all(
      'SELECT p.* FROM permissions p JOIN user_permissions up ON p.id = up.permission_id WHERE up.user_id = ?',
      [userId]
    );
    
    return rows.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description,
      createdAt: new Date(row.created_at)
    }));
  }
  
  async getAllPermissions(): Promise<Permission[]> {
    const rows = await this.db.all('SELECT * FROM permissions');
    
    return rows.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description,
      createdAt: row.created_at ? new Date(row.created_at) : null
    }));
  }
  
  async hasPermission(userId: number, permissionName: string): Promise<boolean> {
    // Admin kullanıcıları için tüm izinlere erişim ver
    const userRow = await this.db.get(
      'SELECT role FROM users WHERE id = ?',
      [userId]
    );
    
    if (userRow && userRow.role === 'admin') {
      return true;
    }
    
    // Normal kullanıcılar için izin kontrolü yap
    const row = await this.db.get(
      'SELECT 1 FROM permissions p JOIN user_permissions up ON p.id = up.permission_id WHERE up.user_id = ? AND p.name = ?',
      [userId, permissionName]
    );
    
    return !!row;
  }
  
  async grantPermission(userId: number, permissionId: number): Promise<void> {
    await this.db.run(
      'INSERT INTO user_permissions (user_id, permission_id) VALUES (?, ?) ON CONFLICT DO NOTHING',
      [userId, permissionId]
    );
  }
  
  async revokePermission(userId: number, permissionId: number): Promise<void> {
    await this.db.run(
      'DELETE FROM user_permissions WHERE user_id = ? AND permission_id = ?',
      [userId, permissionId]
    );
  }
  
  // Proje operasyonları
  async getProjects(): Promise<Project[]> {
    const rows = await this.db.all('SELECT * FROM projects');
    
    return rows.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description,
      status: row.status || 'active',
      createdBy: row.created_by,
      createdAt: new Date(row.created_at),
      updatedAt: row.updated_at ? new Date(row.updated_at) : null
    }));
  }
  
  async getProjectsByUserId(userId: number): Promise<Project[]> {
    const rows = await this.db.all(
      'SELECT p.* FROM projects p JOIN user_projects up ON p.id = up.project_id WHERE up.user_id = ?',
      [userId]
    );
    
    return rows.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description,
      status: row.status || 'active',
      createdBy: row.created_by,
      createdAt: new Date(row.created_at),
      updatedAt: row.updated_at ? new Date(row.updated_at) : null
    }));
  }
  
  async getProject(id: number): Promise<Project | undefined> {
    const row = await this.db.get('SELECT * FROM projects WHERE id = ?', [id]);
    
    if (!row) return undefined;
    
    return {
      id: row.id,
      name: row.name,
      description: row.description,
      status: row.status || 'active',
      createdBy: row.created_by,
      createdAt: new Date(row.created_at),
      updatedAt: row.updated_at ? new Date(row.updated_at) : null
    };
  }
  
  async createProject(project: InsertProject): Promise<Project> {
    const now = Date.now();
    const result = await this.db.run(
      'INSERT INTO projects (name, description, status, created_by, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)',
      [project.name, project.description, project.status || 'active', project.createdBy, now, now]
    );
    
    const id = result.lastID;
    
    return {
      id,
      name: project.name,
      description: project.description,
      status: project.status || 'active',
      createdBy: project.createdBy,
      createdAt: new Date(now),
      updatedAt: new Date(now)
    };
  }
  
  async updateProject(id: number, data: Partial<InsertProject>): Promise<Project | undefined> {
    const project = await this.getProject(id);
    
    if (!project) {
      return undefined;
    }
    
    const updateFields = [];
    const updateValues = [];
    
    if (data.name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(data.name);
    }
    
    if (data.description !== undefined) {
      updateFields.push('description = ?');
      updateValues.push(data.description);
    }
    
    if (data.status !== undefined) {
      updateFields.push('status = ?');
      updateValues.push(data.status);
    }
    
    updateFields.push('updated_at = ?');
    updateValues.push(Date.now());
    
    updateValues.push(id);
    
    const result = await this.db.run(
      `UPDATE projects SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );
    
    return await this.getProject(id);
  }
  
  async deleteProject(id: number): Promise<boolean> {
    const result = await this.db.run('DELETE FROM projects WHERE id = ?', [id]);
    return result.changes > 0;
  }
  
  async getDocumentsByProjectId(projectId: number): Promise<Document[]> {
    const rows = await this.db.all('SELECT * FROM documents WHERE project_id = ?', [projectId]);
    
    return rows.map(row => ({
      id: row.id,
      name: row.name,
      type: row.type,
      content: row.content,
      originalContent: row.original_content,
      createdBy: row.user_id,
      projectId: row.project_id,
      analyzedAt: row.analyzed_at ? new Date(row.analyzed_at) : null,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    }));
  }

  // Kullanıcı işlemleri
  async getAllUsers(): Promise<User[]> {
    const rows = await this.db.all('SELECT * FROM users');
    
    return rows.map(row => ({
      id: row.id,
      username: row.username,
      password: row.password,
      role: row.role || 'user',
      name: row.name,
      email: row.email,
      department: row.department,
      jobTitle: row.job_title,
      lastLogin: row.last_login ? new Date(row.last_login) : null,
      createdAt: new Date(row.created_at)
    }));
  }
  
  async updateUser(id: number, data: Partial<User>): Promise<User | undefined> {
    const user = await this.getUser(id);
    
    if (!user) {
      return undefined;
    }
    
    const updateFields = [];
    const updateValues = [];
    
    if (data.name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(data.name);
    }
    
    if (data.email !== undefined) {
      updateFields.push('email = ?');
      updateValues.push(data.email);
    }
    
    if (data.role !== undefined) {
      updateFields.push('role = ?');
      updateValues.push(data.role);
    }
    
    if (data.department !== undefined) {
      updateFields.push('department = ?');
      updateValues.push(data.department);
    }
    
    if (data.jobTitle !== undefined) {
      updateFields.push('job_title = ?');
      updateValues.push(data.jobTitle);
    }
    
    if (data.lastLogin !== undefined) {
      updateFields.push('last_login = ?');
      updateValues.push(data.lastLogin ? data.lastLogin.getTime() : null);
    }
    
    if (updateFields.length === 0) {
      return user;
    }
    
    updateValues.push(id);
    
    const result = await this.db.run(
      `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );
    
    return await this.getUser(id);
  }
}

// SQLite tabanlı veritabanı örneği
export const sqliteStorage = new SqliteStorage();