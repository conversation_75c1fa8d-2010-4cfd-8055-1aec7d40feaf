import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";

interface IntegrationsModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialIntegrationType?: string;
}

const IntegrationsModal = ({ isOpen, onClose }: IntegrationsModalProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Entegrasyon Yönetimi</DialogTitle>
          <DialogDescription>
            Harici sistemlerden veri çekerek otomatik analiz yapın
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="text-center py-8">
            <h3 className="text-lg font-semibold mb-2">Yakında Gelecek</h3>
            <p className="text-neutral-600 mb-4">
              Jira, ALM, TestRail ve diğer entegrasyonlar yakında eklenecek
            </p>
            <Badge variant="outline">Geliştirme Aşamasında</Badge>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default IntegrationsModal;