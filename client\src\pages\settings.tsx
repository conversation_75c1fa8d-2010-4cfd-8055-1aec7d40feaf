import { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { queryClient, apiRequest } from '@/lib/queryClient';
import { resetUserPassword, updateUserStatus } from '@/lib/api';
import { useAuth } from '@/hooks/use-auth';
import { usePermission } from '@/hooks/use-permission';
import { useToast } from '@/hooks/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Loader2, UserPlus, Users, Settings as SettingsIcon, Key, AlertTriangle } from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';

// Yeni kullanıcı formu şeması
const newUserSchema = z.object({
  username: z.string().min(3, 'Kullanıcı adı en az 3 karakter olmalıdır'),
  password: z.string().min(6, 'Şifre en az 6 karakter olmalıdır'),
  role: z.string().min(1, 'Rol seçilmelidir'),
  name: z.string().optional(),
  email: z.string().email('Geçerli bir e-posta adresi giriniz').optional().or(z.literal('')),
  department: z.string().optional(),
  jobTitle: z.string().optional()
});

type NewUserFormValues = z.infer<typeof newUserSchema>;

// Kullanıcı düzenleme formu şeması
const editUserSchema = z.object({
  name: z.string().optional(),
  email: z.string().email('Geçerli bir e-posta adresi giriniz').optional().or(z.literal('')),
  role: z.string().min(1, 'Rol seçilmelidir'),
  department: z.string().optional(),
  jobTitle: z.string().optional()
});

type EditUserFormValues = z.infer<typeof editUserSchema>;

export default function Settings() {
  const { user: loggedInUser } = useAuth();
  const { hasPermission } = usePermission();
  const { toast } = useToast();
  const [editUser, setEditUser] = useState<any>(null);
  const [userPermissionsOpen, setUserPermissionsOpen] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [passwordResetOpen, setPasswordResetOpen] = useState(false);
  const [passwordResetUserId, setPasswordResetUserId] = useState<number | null>(null);
  const [newPassword, setNewPassword] = useState('');

  const isAdmin = loggedInUser?.role === 'admin';
  const canManageUsers = isAdmin || hasPermission('user_management');

  // Kullanıcıları getir
  const { data: users, isLoading: usersLoading } = useQuery({
    queryKey: ['/api/users'],
    queryFn: () => apiRequest('GET', '/api/users').then(res => res.json()),
    enabled: canManageUsers
  });

  // İzinleri getir
  const { data: permissions } = useQuery({
    queryKey: ['/api/permissions/all'],
    queryFn: () => apiRequest('GET', '/api/permissions/all').then(res => res.json()),
    enabled: canManageUsers
  });

  // Kullanıcı izinlerini getir
  const { data: userPermissions, isLoading: permissionsLoading, refetch: refetchUserPermissions } = useQuery({
    queryKey: ['/api/users', selectedUserId, 'permissions'],
    queryFn: () => apiRequest('GET', `/api/users/${selectedUserId}/permissions`).then(res => res.json()),
    enabled: !!selectedUserId
  });

  // Yeni kullanıcı oluşturma
  const newUserForm = useForm<NewUserFormValues>({
    resolver: zodResolver(newUserSchema),
    defaultValues: {
      username: '',
      password: '',
      role: 'user',
      name: '',
      email: '',
      department: '',
      jobTitle: ''
    }
  });

  // Kullanıcı düzenleme
  const editUserForm = useForm<EditUserFormValues>({
    resolver: zodResolver(editUserSchema),
    defaultValues: {
      name: '',
      email: '',
      role: 'user',
      department: '',
      jobTitle: ''
    }
  });

  // Kullanıcı oluşturma mutation
  const createUserMutation = useMutation({
    mutationFn: async (data: NewUserFormValues) => {
      const response = await apiRequest('POST', '/api/users', data);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Kullanıcı oluşturulurken bir hata oluştu');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/users'] });
      newUserForm.reset();
      toast({
        title: 'Başarılı',
        description: 'Kullanıcı başarıyla oluşturuldu',
        variant: 'default',
      });
    },
    onError: (error) => {
      toast({
        title: 'Hata',
        description: `${error}`,
        variant: 'destructive',
      });
    }
  });

  // Kullanıcı güncelleme mutation
  const updateUserMutation = useMutation({
    mutationFn: async (data: { userId: number, userData: EditUserFormValues }) => {
      const response = await apiRequest('PUT', `/api/users/${data.userId}`, data.userData);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Kullanıcı güncellenirken bir hata oluştu');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/users'] });
      setEditUser(null);
      toast({
        title: 'Başarılı',
        description: 'Kullanıcı başarıyla güncellendi',
        variant: 'default',
      });
    },
    onError: (error) => {
      toast({
        title: 'Hata',
        description: `${error}`,
        variant: 'destructive',
      });
    }
  });

  // İzin değiştirme mutation
  const togglePermissionMutation = useMutation({
    mutationFn: async (data: { userId: number, permissionId: number, action: 'grant' | 'revoke' }) => {
      const response = await apiRequest('POST', `/api/users/${data.userId}/permissions`, {
        permissionId: data.permissionId,
        action: data.action
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'İzin güncellenirken bir hata oluştu');
      }
      return response.json();
    },
    onSuccess: () => {
      refetchUserPermissions();
      toast({
        title: 'Başarılı',
        description: 'Kullanıcı izinleri güncellendi',
        variant: 'default',
      });
    },
    onError: (error) => {
      toast({
        title: 'Hata',
        description: `${error}`,
        variant: 'destructive',
      });
    }
  });
  
  // Şifre sıfırlama mutation
  const resetPasswordMutation = useMutation({
    mutationFn: async (data: { userId: number, password: string }) => {
      const response = await resetUserPassword(data.userId, data.password);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Şifre sıfırlanırken bir hata oluştu');
      }
      return response.json();
    },
    onSuccess: () => {
      setPasswordResetOpen(false);
      setNewPassword('');
      setPasswordResetUserId(null);
      toast({
        title: 'Başarılı',
        description: 'Kullanıcı şifresi başarıyla sıfırlandı',
        variant: 'default',
      });
    },
    onError: (error) => {
      toast({
        title: 'Hata',
        description: `${error}`,
        variant: 'destructive',
      });
    }
  });
  
  // Kullanıcı durumu değiştirme mutation
  const toggleUserStatusMutation = useMutation({
    mutationFn: async (data: { userId: number, status: 'active' | 'inactive' }) => {
      const response = await updateUserStatus(data.userId, data.status);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Kullanıcı durumu değiştirilirken bir hata oluştu');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/users'] });
      toast({
        title: 'Başarılı',
        description: 'Kullanıcı durumu başarıyla güncellendi',
        variant: 'default',
      });
    },
    onError: (error) => {
      toast({
        title: 'Hata',
        description: `${error}`,
        variant: 'destructive',
      });
    }
  });

  // Düzenleme formuna kullanıcı bilgilerini doldur
  const handleEditUser = (user: any) => {
    setEditUser(user);
    editUserForm.reset({
      name: user.name || '',
      email: user.email || '',
      role: user.role || 'user',
      department: user.department || '',
      jobTitle: user.jobTitle || ''
    });
  };

  // İzinleri düzenleme modalını aç
  const handleEditPermissions = (userId: number) => {
    setSelectedUserId(userId);
    setUserPermissionsOpen(true);
  };

  // Bir izne sahip olup olmadığını kontrol et
  const hasUserPermission = (permissionName: string) => {
    return userPermissions?.find((p: any) => p.name === permissionName);
  };

  // İzin toggle'i
  const handleTogglePermission = (permissionId: number, has: boolean) => {
    if (!selectedUserId) return;
    
    togglePermissionMutation.mutate({
      userId: selectedUserId,
      permissionId,
      action: has ? 'revoke' : 'grant'
    });
  };

  // Yeni kullanıcı oluşturma formu gönderimi
  const onSubmitNewUser = (data: NewUserFormValues) => {
    createUserMutation.mutate(data);
  };

  // Kullanıcı düzenleme formu gönderimi
  const onSubmitEditUser = (data: EditUserFormValues) => {
    if (!editUser) return;
    updateUserMutation.mutate({
      userId: editUser.id,
      userData: data
    });
  };
  
  // Şifre sıfırlama modalını aç
  const handleResetPassword = (userId: number) => {
    setPasswordResetUserId(userId);
    setPasswordResetOpen(true);
    setNewPassword('');
  };
  
  // Şifre sıfırlama işlemi
  const handlePasswordReset = () => {
    if (!passwordResetUserId || !newPassword) return;
    
    resetPasswordMutation.mutate({
      userId: passwordResetUserId,
      password: newPassword
    });
  };
  
  // Kullanıcı durumunu değiştir (aktif/pasif)
  const handleToggleUserStatus = (userId: number, status: 'active' | 'inactive') => {
    // Kullanıcının kendi hesabını devre dışı bırakmasını engelle
    if (userId === loggedInUser?.id && status === 'inactive') {
      toast({
        title: 'İşlem Engellendi',
        description: 'Kendi hesabınızı devre dışı bırakamazsınız.',
        variant: 'destructive',
      });
      return;
    }
    
    toggleUserStatusMutation.mutate({
      userId,
      status
    });
  };

  if (!canManageUsers) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6">
        <AlertTriangle className="h-20 w-20 text-orange-500 mb-4" />
        <h1 className="text-2xl font-bold mb-2">Yetkisiz Erişim</h1>
        <p className="text-center text-gray-500">Bu sayfaya erişim yetkiniz bulunmamaktadır. Yönetici ile iletişime geçiniz.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-4 flex items-center gap-2">
        <SettingsIcon className="h-8 w-8" />
        Sistem Ayarları
      </h1>

      <Tabs defaultValue="users" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Kullanıcı Yönetimi
          </TabsTrigger>
          <TabsTrigger value="api" className="flex items-center gap-2">
            <Key className="h-4 w-4" />
            API Ayarları
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-6">
          <div className="grid md:grid-cols-3 gap-6">
            {/* Yeni kullanıcı ekleme kartı */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <UserPlus className="h-5 w-5" />
                  Yeni Kullanıcı Ekle
                </CardTitle>
                <CardDescription>
                  Sisteme yeni bir kullanıcı ekleyin.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...newUserForm}>
                  <form onSubmit={newUserForm.handleSubmit(onSubmitNewUser)} className="space-y-4">
                    <FormField
                      control={newUserForm.control}
                      name="username"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Kullanıcı Adı</FormLabel>
                          <FormControl>
                            <Input placeholder="kullanici" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={newUserForm.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Şifre</FormLabel>
                          <FormControl>
                            <Input type="password" placeholder="••••••" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={newUserForm.control}
                      name="role"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Rol</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Rol seçin" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="user">Kullanıcı</SelectItem>
                              <SelectItem value="admin">Yönetici</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={newUserForm.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Ad Soyad</FormLabel>
                          <FormControl>
                            <Input placeholder="Tam Ad" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={newUserForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>E-posta</FormLabel>
                          <FormControl>
                            <Input placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <Button 
                      type="submit" 
                      className="w-full"
                      disabled={createUserMutation.isPending}
                    >
                      {createUserMutation.isPending ? (
                        <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Ekleniyor...</>
                      ) : (
                        "Kullanıcı Ekle"
                      )}
                    </Button>
                  </form>
                </Form>
              </CardContent>
            </Card>

            {/* Kullanıcı listesi */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Kullanıcı Listesi
                </CardTitle>
                <CardDescription>
                  Sistem kullanıcılarını yönetin.
                </CardDescription>
              </CardHeader>
              <CardContent>
                {usersLoading ? (
                  <div className="flex justify-center p-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableCaption>Toplam {users?.length || 0} kullanıcı</TableCaption>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Kullanıcı</TableHead>
                          <TableHead>Rol</TableHead>
                          <TableHead>E-posta</TableHead>
                          <TableHead className="text-right">İşlemler</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {users?.map((user: any) => (
                          <TableRow key={user.id}>
                            <TableCell className="font-medium">
                              <div className="flex items-center gap-2">
                                <Avatar className="h-8 w-8">
                                  <AvatarFallback>{user.name?.[0] || user.username[0]}</AvatarFallback>
                                </Avatar>
                                <div>
                                  <div>{user.name || user.username}</div>
                                  <div className="text-xs text-muted-foreground">{user.username}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                user.role === 'admin' ? 'bg-primary/20 text-primary' : 'bg-slate-100'
                              }`}>
                                {user.role === 'admin' ? 'Yönetici' : 'Kullanıcı'}
                              </span>
                            </TableCell>
                            <TableCell>{user.email || '-'}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                <Button variant="outline" size="sm" onClick={() => handleEditUser(user)}>
                                  Düzenle
                                </Button>
                                <Button variant="outline" size="sm" onClick={() => handleEditPermissions(user.id)}>
                                  İzinler
                                </Button>
                                <Button 
                                  variant="outline" 
                                  size="sm" 
                                  onClick={() => handleResetPassword(user.id)}
                                >
                                  Şifre Sıfırla
                                </Button>
                                <Button 
                                  variant={user.status === 'inactive' ? 'default' : 'destructive'} 
                                  size="sm" 
                                  onClick={() => handleToggleUserStatus(user.id, user.status === 'active' ? 'inactive' : 'active')}
                                  disabled={user.id === (loggedInUser?.id || 0)}
                                >
                                  {user.status === 'active' ? 'Devre Dışı Bırak' : 'Etkinleştir'}
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="api" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>API Ayarları</CardTitle>
              <CardDescription>
                OpenAI ve diğer API servisleri için ayarlar.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-center text-muted-foreground py-6">Bu özellik yakında eklenecektir.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Kullanıcı düzenleme modal'ı */}
      {editUser && (
        <Dialog open={!!editUser} onOpenChange={(open) => !open && setEditUser(null)}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Kullanıcı Düzenle</DialogTitle>
              <DialogDescription>
                Kullanıcı bilgilerini güncelleyin. Bitirdiğinizde kaydedin.
              </DialogDescription>
            </DialogHeader>
            
            <Form {...editUserForm}>
              <form onSubmit={editUserForm.handleSubmit(onSubmitEditUser)} className="space-y-4">
                <FormField
                  control={editUserForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ad Soyad</FormLabel>
                      <FormControl>
                        <Input placeholder="Tam Ad" {...field} value={field.value || ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={editUserForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>E-posta</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} value={field.value || ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={editUserForm.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Rol</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Rol seçin" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="user">Kullanıcı</SelectItem>
                          <SelectItem value="admin">Yönetici</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={editUserForm.control}
                  name="department"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Departman</FormLabel>
                      <FormControl>
                        <Input placeholder="Departman" {...field} value={field.value || ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={editUserForm.control}
                  name="jobTitle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>İş Unvanı</FormLabel>
                      <FormControl>
                        <Input placeholder="İş Unvanı" {...field} value={field.value || ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setEditUser(null)}
                  >
                    İptal
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={updateUserMutation.isPending}
                  >
                    {updateUserMutation.isPending ? (
                      <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Kaydediliyor...</>
                    ) : (
                      "Kaydet"
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      )}

      {/* Şifre sıfırlama modal'ı */}
      <Dialog open={passwordResetOpen} onOpenChange={setPasswordResetOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Şifre Sıfırlama</DialogTitle>
            <DialogDescription>
              Kullanıcı için yeni şifre belirleyin. Bu işlem geri alınamaz.
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="new-password">Yeni Şifre</Label>
                <Input 
                  id="new-password" 
                  type="password" 
                  placeholder="Yeni şifre" 
                  value={newPassword} 
                  onChange={(e) => setNewPassword(e.target.value)}
                />
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setPasswordResetOpen(false);
                setNewPassword('');
              }}
            >
              İptal
            </Button>
            <Button 
              onClick={handlePasswordReset}
              disabled={!newPassword || resetPasswordMutation.isPending}
            >
              {resetPasswordMutation.isPending ? (
                <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Sıfırlanıyor...</>
              ) : (
                "Şifreyi Sıfırla"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Kullanıcı izinleri modal'ı */}
      <Dialog open={userPermissionsOpen} onOpenChange={setUserPermissionsOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Kullanıcı İzinleri</DialogTitle>
            <DialogDescription>
              Kullanıcının sisteme erişim iznini yönetin.
            </DialogDescription>
          </DialogHeader>
          
          {permissionsLoading ? (
            <div className="flex justify-center p-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <div className="space-y-4">
              {permissions?.map((permission: any) => (
                <div key={permission.id} className="flex items-center justify-between py-2">
                  <div>
                    <Label htmlFor={`permission-${permission.id}`} className="font-medium">
                      {permission.description}
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {permission.name}
                    </p>
                  </div>
                  <Switch
                    id={`permission-${permission.id}`}
                    checked={!!hasUserPermission(permission.name)}
                    onCheckedChange={(checked) => 
                      handleTogglePermission(permission.id, !!hasUserPermission(permission.name))
                    }
                    disabled={togglePermissionMutation.isPending}
                  />
                </div>
              ))}
            </div>
          )}
          
          <DialogFooter>
            <Button onClick={() => setUserPermissionsOpen(false)}>
              Kapat
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}