import { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { queryClient, apiRequest } from '@/lib/queryClient';
import { resetUserPassword, updateUserStatus } from '@/lib/api';
import { useAuth } from '@/hooks/use-auth';
import { usePermission } from '@/hooks/use-permission';
import { useToast } from '@/hooks/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Loader2, UserPlus, Users, Settings as SettingsIcon, Key, AlertTriangle } from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';

// Yeni kullanıcı formu şeması
const newUserSchema = z.object({
  username: z.string().min(3, 'Kullanıcı adı en az 3 karakter olmalıdır'),
  password: z.string().min(6, 'Şifre en az 6 karakter olmalıdır'),
  role: z.string().min(1, 'Rol seçilmelidir'),
  name: z.string().optional(),
  email: z.string().email('Geçerli bir e-posta adresi giriniz').optional().or(z.literal('')),
  department: z.string().optional(),
  jobTitle: z.string().optional()
});

type NewUserFormValues = z.infer<typeof newUserSchema>;

// Kullanıcı düzenleme formu şeması
const editUserSchema = z.object({
  name: z.string().optional(),
  email: z.string().email('Geçerli bir e-posta adresi giriniz').optional().or(z.literal('')),
  role: z.string().min(1, 'Rol seçilmelidir'),
  department: z.string().optional(),
  jobTitle: z.string().optional()
});

type EditUserFormValues = z.infer<typeof editUserSchema>;

export default function Settings() {
  const { user: loggedInUser } = useAuth();
  const { hasPermission } = usePermission();
  const { toast } = useToast();
  const [editUser, setEditUser] = useState<any>(null);
  const [userPermissionsOpen, setUserPermissionsOpen] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [passwordResetOpen, setPasswordResetOpen] = useState(false);
  const [passwordResetUserId, setPasswordResetUserId] = useState<number | null>(null);
  const [newPassword, setNewPassword] = useState('');

  // OpenAI ayarları state'leri
  const [openAISettings, setOpenAISettings] = useState({
    azureEndpoint: 'https://netas-ai.openai.azure.com/',
    azureApiKey: '8RCCsKey1bgFh9FJVd9usDaPwAh7PfNMRJj2luuNt5yMARNFp91bJQQJ99BAACYeBjFXJ3w3AAABACOGwegj',
    azureApiVersion: '2024-08-01-preview',
    azureRegion: 'East US',
    o1DeploymentName: 'netas-ai-o1',
    o3DeploymentName: 'netas-ai-o3-mini',
    o4DeploymentName: 'gpt-4o',
    maxRequestsPerMinute: 450,
    maxTokensPerMinute: 450000,
    timeoutMs: 60000,
    visualAnalysisEnabled: false,
    defaultModel: 'o1' as 'o1' | 'o3' | 'o4'
  });
  const [settingsChanged, setSettingsChanged] = useState(false);

  const isAdmin = loggedInUser?.role === 'admin';
  const canManageUsers = isAdmin || hasPermission('user_management');
  const canManageAPI = isAdmin || hasPermission('api_settings');

  // Kullanıcıları getir
  const { data: users, isLoading: usersLoading } = useQuery({
    queryKey: ['/api/users'],
    queryFn: () => apiRequest('GET', '/api/users').then(res => res.json()),
    enabled: canManageUsers
  });

  // İzinleri getir
  const { data: permissions } = useQuery({
    queryKey: ['/api/permissions/all'],
    queryFn: () => apiRequest('GET', '/api/permissions/all').then(res => res.json()),
    enabled: canManageUsers
  });

  // Kullanıcı izinlerini getir
  const { data: userPermissions, isLoading: permissionsLoading, refetch: refetchUserPermissions } = useQuery({
    queryKey: ['/api/users', selectedUserId, 'permissions'],
    queryFn: () => apiRequest('GET', `/api/users/${selectedUserId}/permissions`).then(res => res.json()),
    enabled: !!selectedUserId
  });

  // Sistem ayarlarını getir
  const { data: systemSettings, isLoading: settingsLoading } = useQuery({
    queryKey: ['/api/system-settings'],
    queryFn: () => apiRequest('GET', '/api/system-settings').then(res => res.json()),
    enabled: canManageAPI
  });

  // Sistem ayarları yüklendiğinde state'i güncelle
  useEffect(() => {
    if (systemSettings && Array.isArray(systemSettings)) {
      const newSettings = { ...openAISettings };

      systemSettings.forEach((setting: any) => {
        const { category, key, value } = setting;

        if (category === 'azure_openai') {
          switch (key) {
            case 'endpoint':
              newSettings.azureEndpoint = value || newSettings.azureEndpoint;
              break;
            case 'api_key':
              newSettings.azureApiKey = value || newSettings.azureApiKey;
              break;
            case 'api_version':
              newSettings.azureApiVersion = value || newSettings.azureApiVersion;
              break;
            case 'region':
              newSettings.azureRegion = value || newSettings.azureRegion;
              break;
            case 'o1_deployment':
              newSettings.o1DeploymentName = value || newSettings.o1DeploymentName;
              break;
            case 'o3_deployment':
              newSettings.o3DeploymentName = value || newSettings.o3DeploymentName;
              break;
            case 'o4_deployment':
              newSettings.o4DeploymentName = value || newSettings.o4DeploymentName;
              break;
          }
        } else if (category === 'openai') {
          switch (key) {
            case 'max_requests_per_minute':
              newSettings.maxRequestsPerMinute = parseInt(value) || newSettings.maxRequestsPerMinute;
              break;
            case 'max_tokens_per_minute':
              newSettings.maxTokensPerMinute = parseInt(value) || newSettings.maxTokensPerMinute;
              break;
            case 'timeout_ms':
              newSettings.timeoutMs = parseInt(value) || newSettings.timeoutMs;
              break;
            case 'default_model':
              newSettings.defaultModel = (value as 'o1' | 'o3' | 'o4') || newSettings.defaultModel;
              break;
          }
        } else if (category === 'visual_analysis') {
          switch (key) {
            case 'enabled':
              newSettings.visualAnalysisEnabled = value === 'true';
              break;
          }
        }
      });

      setOpenAISettings(newSettings);
      setSettingsChanged(false);
    }
  }, [systemSettings]);

  // Yeni kullanıcı oluşturma
  const newUserForm = useForm<NewUserFormValues>({
    resolver: zodResolver(newUserSchema),
    defaultValues: {
      username: '',
      password: '',
      role: 'user',
      name: '',
      email: '',
      department: '',
      jobTitle: ''
    }
  });

  // Kullanıcı düzenleme
  const editUserForm = useForm<EditUserFormValues>({
    resolver: zodResolver(editUserSchema),
    defaultValues: {
      name: '',
      email: '',
      role: 'user',
      department: '',
      jobTitle: ''
    }
  });

  // Kullanıcı oluşturma mutation
  const createUserMutation = useMutation({
    mutationFn: async (data: NewUserFormValues) => {
      const response = await apiRequest('POST', '/api/users', data);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Kullanıcı oluşturulurken bir hata oluştu');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/users'] });
      newUserForm.reset();
      toast({
        title: 'Başarılı',
        description: 'Kullanıcı başarıyla oluşturuldu',
        variant: 'default',
      });
    },
    onError: (error) => {
      toast({
        title: 'Hata',
        description: `${error}`,
        variant: 'destructive',
      });
    }
  });

  // Kullanıcı güncelleme mutation
  const updateUserMutation = useMutation({
    mutationFn: async (data: { userId: number, userData: EditUserFormValues }) => {
      const response = await apiRequest('PUT', `/api/users/${data.userId}`, data.userData);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Kullanıcı güncellenirken bir hata oluştu');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/users'] });
      setEditUser(null);
      toast({
        title: 'Başarılı',
        description: 'Kullanıcı başarıyla güncellendi',
        variant: 'default',
      });
    },
    onError: (error) => {
      toast({
        title: 'Hata',
        description: `${error}`,
        variant: 'destructive',
      });
    }
  });

  // İzin değiştirme mutation
  const togglePermissionMutation = useMutation({
    mutationFn: async (data: { userId: number, permissionId: number, action: 'grant' | 'revoke' }) => {
      const response = await apiRequest('POST', `/api/users/${data.userId}/permissions`, {
        permissionId: data.permissionId,
        action: data.action
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'İzin güncellenirken bir hata oluştu');
      }
      return response.json();
    },
    onSuccess: () => {
      refetchUserPermissions();
      toast({
        title: 'Başarılı',
        description: 'Kullanıcı izinleri güncellendi',
        variant: 'default',
      });
    },
    onError: (error) => {
      toast({
        title: 'Hata',
        description: `${error}`,
        variant: 'destructive',
      });
    }
  });

  // Şifre sıfırlama mutation
  const resetPasswordMutation = useMutation({
    mutationFn: async (data: { userId: number, password: string }) => {
      const response = await resetUserPassword(data.userId, data.password);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Şifre sıfırlanırken bir hata oluştu');
      }
      return response.json();
    },
    onSuccess: () => {
      setPasswordResetOpen(false);
      setNewPassword('');
      setPasswordResetUserId(null);
      toast({
        title: 'Başarılı',
        description: 'Kullanıcı şifresi başarıyla sıfırlandı',
        variant: 'default',
      });
    },
    onError: (error) => {
      toast({
        title: 'Hata',
        description: `${error}`,
        variant: 'destructive',
      });
    }
  });

  // Kullanıcı durumu değiştirme mutation
  const toggleUserStatusMutation = useMutation({
    mutationFn: async (data: { userId: number, status: 'active' | 'inactive' }) => {
      const response = await updateUserStatus(data.userId, data.status);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Kullanıcı durumu değiştirilirken bir hata oluştu');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/users'] });
      toast({
        title: 'Başarılı',
        description: 'Kullanıcı durumu başarıyla güncellendi',
        variant: 'default',
      });
    },
    onError: (error) => {
      toast({
        title: 'Hata',
        description: `${error}`,
        variant: 'destructive',
      });
    }
  });

  // OpenAI ayarlarını kaydetme mutation
  const saveOpenAISettingsMutation = useMutation({
    mutationFn: async (settings: typeof openAISettings) => {
      // Her ayarı ayrı ayrı kaydet
      const settingsToSave = [
        // Azure OpenAI ayarları
        { category: 'azure_openai', key: 'endpoint', value: settings.azureEndpoint },
        { category: 'azure_openai', key: 'api_key', value: settings.azureApiKey },
        { category: 'azure_openai', key: 'api_version', value: settings.azureApiVersion },
        { category: 'azure_openai', key: 'region', value: settings.azureRegion },
        { category: 'azure_openai', key: 'o1_deployment', value: settings.o1DeploymentName },
        { category: 'azure_openai', key: 'o3_deployment', value: settings.o3DeploymentName },
        { category: 'azure_openai', key: 'o4_deployment', value: settings.o4DeploymentName },

        // Genel OpenAI ayarları
        { category: 'openai', key: 'max_requests_per_minute', value: settings.maxRequestsPerMinute.toString() },
        { category: 'openai', key: 'max_tokens_per_minute', value: settings.maxTokensPerMinute.toString() },
        { category: 'openai', key: 'timeout_ms', value: settings.timeoutMs.toString() },
        { category: 'openai', key: 'default_model', value: settings.defaultModel },

        // Görsel analizi ayarları
        { category: 'visual_analysis', key: 'enabled', value: settings.visualAnalysisEnabled.toString() }
      ];

      // Her ayarı kaydet
      for (const setting of settingsToSave) {
        // Önce mevcut ayarı kontrol et
        try {
          const existingResponse = await apiRequest('GET', `/api/system-settings/${setting.category}/${setting.key}`);

          if (existingResponse.ok) {
            // Mevcut ayarı güncelle
            const existingSetting = await existingResponse.json();
            const updateResponse = await apiRequest('PUT', `/api/system-settings/${existingSetting.id}`, {
              value: setting.value
            });

            if (!updateResponse.ok) {
              const errorData = await updateResponse.json();
              throw new Error(errorData.error || `${setting.key} ayarı güncellenirken hata oluştu`);
            }
          } else {
            // Yeni ayar oluştur
            const createResponse = await apiRequest('POST', '/api/system-settings', {
              category: setting.category,
              key: setting.key,
              value: setting.value,
              description: `OpenAI ${setting.key} ayarı`,
              type: typeof setting.value === 'string' ? 'string' : 'number',
              isActive: true
            });

            if (!createResponse.ok) {
              const errorData = await createResponse.json();
              throw new Error(errorData.error || `${setting.key} ayarı oluşturulurken hata oluştu`);
            }
          }
        } catch (error) {
          console.error(`Ayar kaydetme hatası (${setting.key}):`, error);
          throw error;
        }
      }

      return { success: true };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/system-settings'] });
      setSettingsChanged(false);
      toast({
        title: 'Başarılı',
        description: 'OpenAI ayarları başarıyla kaydedildi',
        variant: 'default',
      });
    },
    onError: (error) => {
      toast({
        title: 'Hata',
        description: `Ayarlar kaydedilirken hata oluştu: ${error}`,
        variant: 'destructive',
      });
    }
  });

  // Düzenleme formuna kullanıcı bilgilerini doldur
  const handleEditUser = (user: any) => {
    setEditUser(user);
    editUserForm.reset({
      name: user.name || '',
      email: user.email || '',
      role: user.role || 'user',
      department: user.department || '',
      jobTitle: user.jobTitle || ''
    });
  };

  // İzinleri düzenleme modalını aç
  const handleEditPermissions = (userId: number) => {
    setSelectedUserId(userId);
    setUserPermissionsOpen(true);
  };

  // Bir izne sahip olup olmadığını kontrol et
  const hasUserPermission = (permissionName: string) => {
    return userPermissions?.find((p: any) => p.name === permissionName);
  };

  // İzin toggle'i
  const handleTogglePermission = (permissionId: number, has: boolean) => {
    if (!selectedUserId) return;

    togglePermissionMutation.mutate({
      userId: selectedUserId,
      permissionId,
      action: has ? 'revoke' : 'grant'
    });
  };

  // Yeni kullanıcı oluşturma formu gönderimi
  const onSubmitNewUser = (data: NewUserFormValues) => {
    createUserMutation.mutate(data);
  };

  // Kullanıcı düzenleme formu gönderimi
  const onSubmitEditUser = (data: EditUserFormValues) => {
    if (!editUser) return;
    updateUserMutation.mutate({
      userId: editUser.id,
      userData: data
    });
  };

  // Şifre sıfırlama modalını aç
  const handleResetPassword = (userId: number) => {
    setPasswordResetUserId(userId);
    setPasswordResetOpen(true);
    setNewPassword('');
  };

  // Şifre sıfırlama işlemi
  const handlePasswordReset = () => {
    if (!passwordResetUserId || !newPassword) return;

    resetPasswordMutation.mutate({
      userId: passwordResetUserId,
      password: newPassword
    });
  };

  // Kullanıcı durumunu değiştir (aktif/pasif)
  const handleToggleUserStatus = (userId: number, status: 'active' | 'inactive') => {
    // Kullanıcının kendi hesabını devre dışı bırakmasını engelle
    if (userId === loggedInUser?.id && status === 'inactive') {
      toast({
        title: 'İşlem Engellendi',
        description: 'Kendi hesabınızı devre dışı bırakamazsınız.',
        variant: 'destructive',
      });
      return;
    }

    toggleUserStatusMutation.mutate({
      userId,
      status
    });
  };

  // OpenAI ayarlarını değiştir
  const handleSettingChange = (key: keyof typeof openAISettings, value: any) => {
    setOpenAISettings(prev => ({
      ...prev,
      [key]: value
    }));
    setSettingsChanged(true);
  };

  // OpenAI ayarlarını kaydet
  const handleSaveSettings = () => {
    saveOpenAISettingsMutation.mutate(openAISettings);
  };

  // Ayarları sıfırla
  const handleResetSettings = () => {
    setOpenAISettings({
      azureEndpoint: 'https://netas-ai.openai.azure.com/',
      azureApiKey: '8RCCsKey1bgFh9FJVd9usDaPwAh7PfNMRJj2luuNt5yMARNFp91bJQQJ99BAACYeBjFXJ3w3AAABACOGwegj',
      azureApiVersion: '2024-08-01-preview',
      azureRegion: 'East US',
      o1DeploymentName: 'netas-ai-o1',
      o3DeploymentName: 'netas-ai-o3-mini',
      o4DeploymentName: 'gpt-4o',
      maxRequestsPerMinute: 450,
      maxTokensPerMinute: 450000,
      timeoutMs: 60000,
      visualAnalysisEnabled: false,
      defaultModel: 'o1' as 'o1' | 'o3' | 'o4'
    });
    setSettingsChanged(true);
  };

  if (!canManageUsers) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6">
        <AlertTriangle className="h-20 w-20 text-orange-500 mb-4" />
        <h1 className="text-2xl font-bold mb-2">Yetkisiz Erişim</h1>
        <p className="text-center text-gray-500">Bu sayfaya erişim yetkiniz bulunmamaktadır. Yönetici ile iletişime geçiniz.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-4 flex items-center gap-2">
        <SettingsIcon className="h-8 w-8" />
        Sistem Ayarları
      </h1>

      <Tabs defaultValue="users" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Kullanıcı Yönetimi
          </TabsTrigger>
          <TabsTrigger value="api" className="flex items-center gap-2">
            <Key className="h-4 w-4" />
            API Ayarları
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-6">
          <div className="grid md:grid-cols-3 gap-6">
            {/* Yeni kullanıcı ekleme kartı */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <UserPlus className="h-5 w-5" />
                  Yeni Kullanıcı Ekle
                </CardTitle>
                <CardDescription>
                  Sisteme yeni bir kullanıcı ekleyin.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...newUserForm}>
                  <form onSubmit={newUserForm.handleSubmit(onSubmitNewUser)} className="space-y-4">
                    <FormField
                      control={newUserForm.control}
                      name="username"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Kullanıcı Adı</FormLabel>
                          <FormControl>
                            <Input placeholder="kullanici" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={newUserForm.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Şifre</FormLabel>
                          <FormControl>
                            <Input type="password" placeholder="••••••" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={newUserForm.control}
                      name="role"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Rol</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Rol seçin" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="user">Kullanıcı</SelectItem>
                              <SelectItem value="admin">Yönetici</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={newUserForm.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Ad Soyad</FormLabel>
                          <FormControl>
                            <Input placeholder="Tam Ad" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={newUserForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>E-posta</FormLabel>
                          <FormControl>
                            <Input placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Button
                      type="submit"
                      className="w-full"
                      disabled={createUserMutation.isPending}
                    >
                      {createUserMutation.isPending ? (
                        <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Ekleniyor...</>
                      ) : (
                        "Kullanıcı Ekle"
                      )}
                    </Button>
                  </form>
                </Form>
              </CardContent>
            </Card>

            {/* Kullanıcı listesi */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Kullanıcı Listesi
                </CardTitle>
                <CardDescription>
                  Sistem kullanıcılarını yönetin.
                </CardDescription>
              </CardHeader>
              <CardContent>
                {usersLoading ? (
                  <div className="flex justify-center p-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableCaption>Toplam {users?.length || 0} kullanıcı</TableCaption>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Kullanıcı</TableHead>
                          <TableHead>Rol</TableHead>
                          <TableHead>E-posta</TableHead>
                          <TableHead className="text-right">İşlemler</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {users?.map((user: any) => (
                          <TableRow key={user.id}>
                            <TableCell className="font-medium">
                              <div className="flex items-center gap-2">
                                <Avatar className="h-8 w-8">
                                  <AvatarFallback>{user.name?.[0] || user.username[0]}</AvatarFallback>
                                </Avatar>
                                <div>
                                  <div>{user.name || user.username}</div>
                                  <div className="text-xs text-muted-foreground">{user.username}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <span className={`px-2 py-1 rounded-full text-xs ${user.role === 'admin' ? 'bg-primary/20 text-primary' : 'bg-slate-100'
                                }`}>
                                {user.role === 'admin' ? 'Yönetici' : 'Kullanıcı'}
                              </span>
                            </TableCell>
                            <TableCell>{user.email || '-'}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                <Button variant="outline" size="sm" onClick={() => handleEditUser(user)}>
                                  Düzenle
                                </Button>
                                <Button variant="outline" size="sm" onClick={() => handleEditPermissions(user.id)}>
                                  İzinler
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleResetPassword(user.id)}
                                >
                                  Şifre Sıfırla
                                </Button>
                                <Button
                                  variant={user.status === 'inactive' ? 'default' : 'destructive'}
                                  size="sm"
                                  onClick={() => handleToggleUserStatus(user.id, user.status === 'active' ? 'inactive' : 'active')}
                                  disabled={user.id === (loggedInUser?.id || 0)}
                                >
                                  {user.status === 'active' ? 'Devre Dışı Bırak' : 'Etkinleştir'}
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="api" className="space-y-6">
          {!canManageAPI ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <AlertTriangle className="h-16 w-16 text-orange-500 mb-4" />
                <h3 className="text-lg font-semibold mb-2">Yetkisiz Erişim</h3>
                <p className="text-center text-gray-500">API ayarlarını yönetmek için yetkiniz bulunmamaktadır.</p>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>OpenAI API Ayarları</CardTitle>
                <CardDescription>
                  Azure OpenAI ve görsel analizi ayarları. Bu ayarlar tüm sistem genelinde kullanılır.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {settingsLoading ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Azure OpenAI Ayarları */}
                      <div className="space-y-4">
                        <h3 className="text-lg font-medium">Azure OpenAI</h3>
                        <div className="space-y-3">
                          <div>
                            <Label className="text-sm font-medium">Endpoint</Label>
                            <Input
                              value={openAISettings.azureEndpoint}
                              onChange={(e) => handleSettingChange('azureEndpoint', e.target.value)}
                              placeholder="https://your-resource.openai.azure.com/"
                            />
                          </div>
                          <div>
                            <Label className="text-sm font-medium">API Key</Label>
                            <Input
                              type="password"
                              value={openAISettings.azureApiKey}
                              onChange={(e) => handleSettingChange('azureApiKey', e.target.value)}
                              placeholder="Azure OpenAI API anahtarı"
                            />
                          </div>
                          <div>
                            <Label className="text-sm font-medium">API Version</Label>
                            <Input
                              value={openAISettings.azureApiVersion}
                              onChange={(e) => handleSettingChange('azureApiVersion', e.target.value)}
                              placeholder="2024-08-01-preview"
                            />
                          </div>
                          <div>
                            <Label className="text-sm font-medium">Region</Label>
                            <Input
                              value={openAISettings.azureRegion}
                              onChange={(e) => handleSettingChange('azureRegion', e.target.value)}
                              placeholder="East US"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Model Deployment Ayarları */}
                      <div className="space-y-4">
                        <h3 className="text-lg font-medium">Model Deployment'ları</h3>
                        <div className="space-y-3">
                          <div>
                            <Label className="text-sm font-medium">o1 Model Deployment</Label>
                            <Input
                              value={openAISettings.o1DeploymentName}
                              onChange={(e) => handleSettingChange('o1DeploymentName', e.target.value)}
                              placeholder="netas-ai-o1"
                            />
                          </div>
                          <div>
                            <Label className="text-sm font-medium">o3 Model Deployment</Label>
                            <Input
                              value={openAISettings.o3DeploymentName}
                              onChange={(e) => handleSettingChange('o3DeploymentName', e.target.value)}
                              placeholder="netas-ai-o3-mini"
                            />
                          </div>
                          <div>
                            <Label className="text-sm font-medium">o4 Model Deployment</Label>
                            <Input
                              value={openAISettings.o4DeploymentName}
                              onChange={(e) => handleSettingChange('o4DeploymentName', e.target.value)}
                              placeholder="gpt-4o"
                            />
                          </div>
                          <div>
                            <Label className="text-sm font-medium">Varsayılan Model</Label>
                            <Select
                              value={openAISettings.defaultModel}
                              onValueChange={(value: 'o1' | 'o3' | 'o4') => handleSettingChange('defaultModel', value)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Model seçin" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="o1">o1 (GPT-o1)</SelectItem>
                                <SelectItem value="o3">o3 (GPT-o3-mini)</SelectItem>
                                <SelectItem value="o4">o4 (GPT-o4)</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Görsel Analizi Ayarları */}
                    <div className="border-t pt-6">
                      <h3 className="text-lg font-medium mb-4">Görsel Analizi</h3>
                      <div className="flex items-center space-x-3 mb-4">
                        <Switch
                          checked={openAISettings.visualAnalysisEnabled}
                          onCheckedChange={(checked) => handleSettingChange('visualAnalysisEnabled', checked)}
                        />
                        <Label className="text-sm font-medium">
                          Görsel analizi etkinleştir
                        </Label>
                      </div>

                      {!openAISettings.visualAnalysisEnabled && (
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                          <div className="flex items-start space-x-3">
                            <div className="flex-shrink-0">
                              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium text-yellow-800">Görsel Analizi Devre Dışı</h4>
                              <p className="text-sm text-yellow-700 mt-1">
                                Görsel analizi kapalı. Sistem metin tabanlı alternatif analiz kullanacak.
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Rate Limiting */}
                    <div className="border-t pt-6">
                      <h3 className="text-lg font-medium mb-4">Rate Limiting</h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <Label className="text-sm font-medium">Requests/min</Label>
                          <Input
                            type="number"
                            value={openAISettings.maxRequestsPerMinute}
                            onChange={(e) => handleSettingChange('maxRequestsPerMinute', parseInt(e.target.value) || 0)}
                            placeholder="450"
                          />
                        </div>
                        <div>
                          <Label className="text-sm font-medium">Tokens/min</Label>
                          <Input
                            type="number"
                            value={openAISettings.maxTokensPerMinute}
                            onChange={(e) => handleSettingChange('maxTokensPerMinute', parseInt(e.target.value) || 0)}
                            placeholder="450000"
                          />
                        </div>
                        <div>
                          <Label className="text-sm font-medium">Timeout (ms)</Label>
                          <Input
                            type="number"
                            value={openAISettings.timeoutMs}
                            onChange={(e) => handleSettingChange('timeoutMs', parseInt(e.target.value) || 0)}
                            placeholder="60000"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Kaydet/Sıfırla Butonları */}
                    <div className="flex justify-between pt-6 border-t">
                      <Button
                        variant="outline"
                        onClick={handleResetSettings}
                        disabled={saveOpenAISettingsMutation.isPending}
                      >
                        Varsayılana Sıfırla
                      </Button>

                      <div className="flex gap-2">
                        {settingsChanged && (
                          <Badge variant="secondary" className="self-center">
                            Kaydedilmemiş değişiklikler
                          </Badge>
                        )}
                        <Button
                          onClick={handleSaveSettings}
                          disabled={!settingsChanged || saveOpenAISettingsMutation.isPending}
                        >
                          {saveOpenAISettingsMutation.isPending ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Kaydediliyor...
                            </>
                          ) : (
                            'Ayarları Kaydet'
                          )}
                        </Button>
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Kullanıcı düzenleme modal'ı */}
      {editUser && (
        <Dialog open={!!editUser} onOpenChange={(open) => !open && setEditUser(null)}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Kullanıcı Düzenle</DialogTitle>
              <DialogDescription>
                Kullanıcı bilgilerini güncelleyin. Bitirdiğinizde kaydedin.
              </DialogDescription>
            </DialogHeader>

            <Form {...editUserForm}>
              <form onSubmit={editUserForm.handleSubmit(onSubmitEditUser)} className="space-y-4">
                <FormField
                  control={editUserForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ad Soyad</FormLabel>
                      <FormControl>
                        <Input placeholder="Tam Ad" {...field} value={field.value || ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editUserForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>E-posta</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} value={field.value || ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editUserForm.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Rol</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Rol seçin" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="user">Kullanıcı</SelectItem>
                          <SelectItem value="admin">Yönetici</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editUserForm.control}
                  name="department"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Departman</FormLabel>
                      <FormControl>
                        <Input placeholder="Departman" {...field} value={field.value || ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editUserForm.control}
                  name="jobTitle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>İş Unvanı</FormLabel>
                      <FormControl>
                        <Input placeholder="İş Unvanı" {...field} value={field.value || ''} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setEditUser(null)}
                  >
                    İptal
                  </Button>
                  <Button
                    type="submit"
                    disabled={updateUserMutation.isPending}
                  >
                    {updateUserMutation.isPending ? (
                      <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Kaydediliyor...</>
                    ) : (
                      "Kaydet"
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      )}

      {/* Şifre sıfırlama modal'ı */}
      <Dialog open={passwordResetOpen} onOpenChange={setPasswordResetOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Şifre Sıfırlama</DialogTitle>
            <DialogDescription>
              Kullanıcı için yeni şifre belirleyin. Bu işlem geri alınamaz.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="new-password">Yeni Şifre</Label>
                <Input
                  id="new-password"
                  type="password"
                  placeholder="Yeni şifre"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setPasswordResetOpen(false);
                setNewPassword('');
              }}
            >
              İptal
            </Button>
            <Button
              onClick={handlePasswordReset}
              disabled={!newPassword || resetPasswordMutation.isPending}
            >
              {resetPasswordMutation.isPending ? (
                <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Sıfırlanıyor...</>
              ) : (
                "Şifreyi Sıfırla"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Kullanıcı izinleri modal'ı */}
      <Dialog open={userPermissionsOpen} onOpenChange={setUserPermissionsOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Kullanıcı İzinleri</DialogTitle>
            <DialogDescription>
              Kullanıcının sisteme erişim iznini yönetin.
            </DialogDescription>
          </DialogHeader>

          {permissionsLoading ? (
            <div className="flex justify-center p-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <div className="space-y-4">
              {permissions?.map((permission: any) => (
                <div key={permission.id} className="flex items-center justify-between py-2">
                  <div>
                    <Label htmlFor={`permission-${permission.id}`} className="font-medium">
                      {permission.description}
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {permission.name}
                    </p>
                  </div>
                  <Switch
                    id={`permission-${permission.id}`}
                    checked={!!hasUserPermission(permission.name)}
                    onCheckedChange={(checked) =>
                      handleTogglePermission(permission.id, !!hasUserPermission(permission.name))
                    }
                    disabled={togglePermissionMutation.isPending}
                  />
                </div>
              ))}
            </div>
          )}

          <DialogFooter>
            <Button onClick={() => setUserPermissionsOpen(false)}>
              Kapat
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}