2025-04-22 21:26:15.22
c2571147
User
severity: 'ERROR',
2025-04-22 21:26:15.22
c2571147
User
code: '42703',
2025-04-22 21:26:15.22
c2571147
User
detail: undefined,
2025-04-22 21:26:15.22
c2571147
User
hint: undefined,
2025-04-22 21:26:15.22
c2571147
User
position: '159',
2025-04-22 21:26:15.22
c2571147
User
internalPosition: undefined,
2025-04-22 21:26:15.22
c2571147
User
internalQuery: undefined,
2025-04-22 21:26:15.22
c2571147
User
where: undefined,
2025-04-22 21:26:15.22
c2571147
User
schema: undefined,
2025-04-22 21:26:15.22
c2571147
User
table: undefined,
2025-04-22 21:26:15.22
c2571147
User
column: undefined,
2025-04-22 21:26:15.22
c2571147
User
dataType: undefined,
2025-04-22 21:26:15.22
c2571147
User
constraint: undefined,
2025-04-22 21:26:15.22
c2571147
User
file: 'parse_relation.c',
2025-04-22 21:26:15.22
c2571147
User
line: '3722',
2025-04-22 21:26:15.22
c2571147
User
routine: 'errorMissingColumn'
2025-04-22 21:26:15.22
c2571147
User
}
2025-04-22 21:26:15.22
c2571147
User
6:26:15 PM [express] DELETE /api/documents/36 500 in 494ms :: {"error":"Doküman silinemedi"}
2025-04-22 21:26:17.50
c2571147
User
Doküman silme hatası: error: column "status" does not exist
2025-04-22 21:26:17.50
c2571147
User
at file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1345:74
2025-04-22 21:26:17.50
c2571147
User
at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-04-22 21:26:17.50
c2571147
User
at async NeonPreparedQuery.execute (file:///home/<USER>/workspace/node_modules/drizzle-orm/neon-serverless/session.js:76:20)
2025-04-22 21:26:17.50
c2571147
User
at async PostgresStorage.deleteDocument (file:///home/<USER>/workspace/dist/index.js:1450:22)
2025-04-22 21:26:17.50
c2571147
User
at async file:///home/<USER>/workspace/dist/index.js:4332:22 {
2025-04-22 21:26:17.50
c2571147
User
length: 107,
2025-04-22 21:26:17.50
c2571147
User
severity: 'ERROR',
2025-04-22 21:26:17.50
c2571147
User
code: '42703',
2025-04-22 21:26:17.50
c2571147
User
detail: undefined,
2025-04-22 21:26:17.50
c2571147
User
hint: undefined,
2025-04-22 21:26:17.50
c2571147
User
position: '159',
2025-04-22 21:26:17.50
c2571147
User
internalPosition: undefined,
2025-04-22 21:26:17.50
c2571147
User
internalQuery: undefined,
2025-04-22 21:26:17.50
c2571147
User
where: undefined,
2025-04-22 21:26:17.50
c2571147
User
schema: undefined,
2025-04-22 21:26:17.50
c2571147
User
table: undefined,
2025-04-22 21:26:17.50
c2571147
User
column: undefined,
2025-04-22 21:26:17.50
c2571147
User
dataType: undefined,
2025-04-22 21:26:17.50
c2571147
User
constraint: undefined,
2025-04-22 21:26:17.50
c2571147
User
file: 'parse_relation.c',
2025-04-22 21:26:17.50
c2571147
User
line: '3722',
2025-04-22 21:26:17.50
c2571147
User
routine: 'errorMissingColumn'
2025-04-22 21:26:17.50
c2571147
User
}