 npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
12:21:26 PM [vite] warning: This case clause will never be evaluated because it duplicates an earlier case clause
354|  // ${expectedResults}`;
355|  
356|        case "cypress":
   |             ^
357|          return `/// <reference types="cypress" />
358|  

  Plugin: vite:esbuild
  File: /home/<USER>/workspace/client/src/components/test-scenarios.tsx
12:22:15 PM [vite] hmr update /src/components/test-scenarios.tsx, /src/index.css?v=9548d6bMxLFqUwNvCNsMb
12:22:28 PM [vite] hmr update /src/components/test-scenarios.tsx, /src/index.css?v=9548d6bMxLFqUwNvCNsMb (x2)
12:22:33 PM [vite] hmr update /src/components/test-scenarios.tsx, /src/index.css?v=9548d6bMxLFqUwNvCNsMb (x3)
12:22:52 PM [vite] hmr update /src/components/test-scenarios.tsx, /src/index.css?v=9548d6bMxLFqUwNvCNsMb (x4)
12:23:35 PM [vite] hmr update /src/components/test-scenarios.tsx, /src/index.css?v=9548d6bMxLFqUwNvCNsMb (x5)
12:26:33 PM [express] GET /api/ai-models 304 in 44ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
12:26:34 PM [express] GET /api/documents 304 in 342ms :: [{"id":3,"name":"demo.docx","type":"docx","c…
12:26:35 PM [express] GET /api/documents/6/components 304 in 73ms :: [{"id":22,"documentId":6,"name":…
12:26:35 PM [express] GET /api/documents/6/requirements 304 in 75ms :: [{"id":47,"documentId":6,"code…
12:26:35 PM [express] GET /api/documents/6 304 in 250ms :: {"id":6,"name":"AD_TLP-2023-000342_Vergi_O…
12:26:35 PM [express] GET /api/documents/6/api-endpoints 304 in 72ms :: [{"id":10,"documentId":6,"url…
12:26:35 PM [express] GET /api/documents/6/ai-analysis 304 in 75ms :: {"id":5,"documentId":6,"observa…
12:26:36 PM [express] GET /api/documents/6/test-scenarios 304 in 73ms :: [{"id":47,"documentId":6,"ti…
o1/o3 modelleri için Azure OpenAI API anahtarı: ******GrPml
12:27:45 PM [express] POST /api/documents/upload 201 in 41029ms :: {"document":{"id":7,"name":"AD_TLP…
12:27:46 PM [express] GET /api/documents 200 in 275ms :: [{"id":3,"name":"demo.docx","type":"docx","c…
12:30:54 PM [vite] hmr update /src/components/test-scenarios.tsx, /src/index.css?v=9548d6bMxLFqUwNvCNsMb (x6)
12:31:08 PM [express] GET /api/ai-models 304 in 35ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
12:31:09 PM [express] GET /api/documents 304 in 417ms :: [{"id":3,"name":"demo.docx","type":"docx","c…
12:31:32 PM [express] GET /api/documents/7/requirements 200 in 78ms :: [{"id":52,"documentId":7,"code…
12:31:32 PM [express] GET /api/documents/7 200 in 240ms :: {"id":7,"name":"AD_TLP-2023-000342_Vergi_O…
12:31:32 PM [express] GET /api/documents/7/components 200 in 96ms :: [{"id":25,"documentId":7,"name":…
12:31:32 PM [express] GET /api/documents/7/api-endpoints 200 in 119ms :: [{"id":13,"documentId":7,"ur…
12:31:32 PM [express] GET /api/documents/7/ai-analysis 200 in 92ms :: {"id":6,"documentId":7,"observa…
12:31:34 PM [express] GET /api/documents/7/test-scenarios 200 in 95ms :: []
12:31:36 PM [express] GET /api/documents/6/requirements 304 in 70ms :: [{"id":47,"documentId":6,"code…
12:31:36 PM [express] GET /api/documents/6/components 304 in 86ms :: [{"id":22,"documentId":6,"name":…
12:31:36 PM [express] GET /api/documents/6/api-endpoints 304 in 70ms :: [{"id":10,"documentId":6,"url…
12:31:36 PM [express] GET /api/documents/6/ai-analysis 304 in 70ms :: {"id":5,"documentId":6,"observa…
12:31:36 PM [express] GET /api/documents/6 304 in 248ms :: {"id":6,"name":"AD_TLP-2023-000342_Vergi_O…
12:31:37 PM [express] GET /api/documents/6/test-scenarios 304 in 86ms :: [{"id":47,"documentId":6,"ti…
12:34:16 PM [vite] hmr update /src/components/test-scenarios.tsx, /src/index.css?v=9548d6bMxLFqUwNvCNsMb (x7)
12:35:08 PM [express] GET /api/ai-models 304 in 46ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
12:35:09 PM [express] GET /api/documents 304 in 1045ms :: [{"id":3,"name":"demo.docx","type":"docx","…
12:42:54 PM [express] GET /api/documents/7/ai-analysis 304 in 81ms :: {"id":6,"documentId":7,"observa…
12:42:54 PM [express] GET /api/documents/7/requirements 304 in 84ms :: [{"id":52,"documentId":7,"code…
12:42:54 PM [express] GET /api/documents/7 304 in 273ms :: {"id":7,"name":"AD_TLP-2023-000342_Vergi_O…
12:42:55 PM [express] GET /api/documents/7/components 304 in 78ms :: [{"id":25,"documentId":7,"name":…
12:42:55 PM [express] GET /api/documents/7/api-endpoints 304 in 95ms :: [{"id":13,"documentId":7,"url…
12:42:55 PM [express] GET /api/documents/7/test-scenarios 304 in 92ms :: []
12:42:58 PM [express] GET /api/documents/6/api-endpoints 304 in 72ms :: [{"id":10,"documentId":6,"url…
12:42:58 PM [express] GET /api/documents/6/ai-analysis 304 in 74ms :: {"id":5,"documentId":6,"observa…
12:42:58 PM [express] GET /api/documents/6/components 304 in 85ms :: [{"id":22,"documentId":6,"name":…
12:42:58 PM [express] GET /api/documents/6/requirements 304 in 85ms :: [{"id":47,"documentId":6,"code…
12:42:58 PM [express] GET /api/documents/6 304 in 211ms :: {"id":6,"name":"AD_TLP-2023-000342_Vergi_O…
12:42:58 PM [express] GET /api/documents/6/test-scenarios 304 in 70ms :: [{"id":47,"documentId":6,"ti…


































