import { useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { Redirect } from "wouter";
import { Loader2, Brain, Database, Zap, Wrench, Layers, Bot, RefreshCw, Cpu } from "lucide-react";
import { Badge } from "@/components/ui/badge";

export default function AuthPage() {
  const { user, isLoading, loginMutation } = useAuth();
  const { toast } = useToast();
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // <PERSON><PERSON><PERSON> kullan<PERSON>ı zaten giriş yapmışsa ana sayfaya yö<PERSON>
  if (user) {
    return <Redirect to="/" />;
  }

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!username || !password) {
      toast({
        title: "Giriş hatası",
        description: "Kullanıcı adı ve şifre gereklidir",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      await loginMutation.mutateAsync({ username, password });
      toast({
        title: "Giriş başarılı",
        description: "Doküman analiz sistemine hoş geldiniz",
      });
    } catch (error) {
      console.error("Giriş hatası:", error);
      toast({
        title: "Giriş başarısız",
        description: "Geçersiz kullanıcı adı veya şifre",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="flex min-h-screen bg-muted/40">
      <div className="flex flex-col justify-center items-center w-full md:w-1/2 p-6 bg-gradient-to-b from-slate-900 to-slate-800">
        <div className="w-full max-w-md">
          <Card className="border shadow-xl bg-white">
            <CardHeader className="space-y-1">
              <div className="flex justify-center mb-2">
                <Brain className="h-12 w-12 text-primary" />
              </div>
              <CardTitle className="text-2xl font-bold text-center bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary-foreground">Netaş Test Ajanı</CardTitle>
              <CardDescription className="text-center text-slate-600">
                Akıllı test otomasyon asistanınıza giriş yapın
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="username">Kullanıcı Adı</Label>
                  <Input
                    id="username"
                    placeholder="Kullanıcı adınızı girin"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Şifre</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="Şifrenizi girin"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                  />
                </div>
                <Button type="submit" className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : null}
                  Giriş Yap
                </Button>
              </form>
            </CardContent>
            <CardFooter className="justify-center">
              <div className="flex flex-wrap gap-2 justify-center">
                <Badge variant="outline" className="bg-primary/10 text-primary font-medium">
                  <Bot className="w-3 h-3 mr-1" />
                  Test Ajanı
                </Badge>
                <Badge variant="outline" className="bg-primary/10 text-primary font-medium">
                  <Cpu className="w-3 h-3 mr-1" />
                  Çoklu Model
                </Badge>
                <Badge variant="outline" className="bg-primary/10 text-primary font-medium">
                  <RefreshCw className="w-3 h-3 mr-1" />
                  Sürekli Öğrenen
                </Badge>
                <Badge variant="outline" className="bg-primary/10 text-primary font-medium">
                  <Wrench className="w-3 h-3 mr-1" />
                  Otomasyon
                </Badge>
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
      <div className="hidden md:flex md:w-1/2 bg-white items-center justify-center p-8">
        <div className="max-w-lg text-slate-800">
          <h1 className="text-3xl font-bold mb-2 text-primary">
            Netaş - Agentic Yazılım Test Asistanı
          </h1>
          <p className="text-slate-600 mb-6 font-medium">
            Yapay zeka destekli, hafızalı ve sürekli gelişen test asistanı ile geliştirme süreçlerinizi hızlandırın
          </p>
          
          <div className="space-y-5">
            <div className="flex items-start p-4 rounded-lg border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
              <Database className="h-8 w-8 mr-3 text-primary" />
              <div>
                <h3 className="font-semibold text-slate-800">Hafızalı Ajan Teknolojisi</h3>
                <p className="text-sm text-slate-600">Önceki analizlerden öğrenerek benzer dokumanları daha hızlı ve etkili işler, proje hafızasında kalıcı bilgi depolar</p>
              </div>
            </div>
            
            <div className="flex items-start p-4 rounded-lg border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
              <Layers className="h-8 w-8 mr-3 text-primary" />
              <div>
                <h3 className="font-semibold text-slate-800">Hibrit AI Model Desteği</h3>
                <p className="text-sm text-slate-600">Hem bulut tabanlı (Azure o1, o3-mini, o4) hem de yerel modelleri destekleyerek her ortamda çalışabilir</p>
              </div>
            </div>
            
            <div className="flex items-start p-4 rounded-lg border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
              <Wrench className="h-8 w-8 mr-3 text-primary" />
              <div>
                <h3 className="font-semibold text-slate-800">Otomatik Test Çalıştırma</h3>
                <p className="text-sm text-slate-600">Dokümandan test senaryoları çıkararak otomatik koşulabilir testler oluşturur ve mevcut otomasyon araçlarıyla entegre çalışır</p>
              </div>
            </div>
            
            <div className="flex items-start p-4 rounded-lg border border-slate-200 shadow-sm hover:shadow-md transition-shadow">
              <Zap className="h-8 w-8 mr-3 text-primary" />
              <div>
                <h3 className="font-semibold text-slate-800">Sürekli Öğrenme Mekanizması</h3>
                <p className="text-sm text-slate-600">Her etkileşimden öğrenerek kendini geliştiren ve test senaryolarını sürekli iyileştiren adaptif yapay zeka</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}