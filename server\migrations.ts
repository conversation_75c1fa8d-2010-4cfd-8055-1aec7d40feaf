/**
 * Veritabanı şema güncellemeleri ve başlangıç verileri için yardımcı fonksiyonlar
 */
import { db } from './db';
import { permissions, projects, userPermissions, userProjects } from '../shared/schema';
import { storage } from './storage';
import { eq } from 'drizzle-orm';

// Sistem izinleri - bunlar kullanıcılara atanabilecek temel yetkilerdir
export const SYSTEM_PERMISSIONS = {
  // Admin yetkileri
  ADMIN_ACCESS: 'ADMIN_ACCESS', // Admin paneline erişim
  USER_MANAGE: 'USER_MANAGE', // Kullanıcı yönetimi

  // Proje yetkileri
  PROJECT_CREATE: 'PROJECT_CREATE', // Yeni proje oluşturma
  PROJECT_DELETE: 'PROJECT_DELETE', // Proje silme

  // Doküman yetkileri
  DOCUMENT_CREATE: 'DOCUMENT_CREATE', // Yeni doküman oluşturma
  DOCUMENT_DELETE: 'document_delete', // Doküman silme

  // Test senaryosu yetkileri
  SCENARIO_CREATE: 'SCENARIO_CREATE', // Test senaryosu oluşturma
  SCENARIO_DELETE: 'SCENARIO_DELETE', // Test senaryosu silme

  // Analiz yetkileri
  ANALYSIS_CREATE: 'ANALYSIS_CREATE', // Analiz oluşturma
  COVERAGE_CREATE: 'COVERAGE_CREATE', // Kapsam analizi oluşturma

  // Ayarlar yetkileri
  SETTINGS_VIEW: 'SETTINGS_VIEW', // Ayarları görüntüleme
  SETTINGS_EDIT: 'SETTINGS_EDIT', // Ayarları düzenleme

  // Prompt yönetim yetkileri
  PROMPT_MANAGEMENT: 'prompt_management', // Prompt düzenleme izni
};

// Kullanıcı rolleri ve her rolün sahip olduğu izinler
export const USER_ROLES = {
  ADMIN: {
    name: 'admin',
    permissions: Object.values(SYSTEM_PERMISSIONS),
    description: 'Sistem yöneticisi, tüm özelliklere erişebilir'
  },
  USER: {
    name: 'user',
    permissions: [
      SYSTEM_PERMISSIONS.DOCUMENT_CREATE,
      SYSTEM_PERMISSIONS.SCENARIO_CREATE,
      SYSTEM_PERMISSIONS.ANALYSIS_CREATE,
      SYSTEM_PERMISSIONS.COVERAGE_CREATE,
      SYSTEM_PERMISSIONS.SETTINGS_VIEW,
    ],
    description: 'Standart kullanıcı, temel özelliklere erişebilir'
  }
};

// Veritabanı göç işlemini gerçekleştir ve temel izinleri oluştur
export async function migrateDatabase() {
  console.log('Veritabanı şeması güncelleniyor...');
  try {
    // İzinleri oluştur
    await createPermissions();

    // Mevcut kullanıcılara rolleri ve izinleri ata
    await assignPermissionsToUsers();

    console.log('Veritabanı başarıyla güncellendi.');
  } catch (error) {
    console.error('Veritabanı güncellemesi sırasında hata:', error);
    throw error;
  }
}

// Temel izinleri oluştur
async function createPermissions() {
  console.log('Sistem izinleri oluşturuluyor...');

  for (const [key, value] of Object.entries(SYSTEM_PERMISSIONS)) {
    const existingPermission = await db.select().from(permissions).where(eq(permissions.name, value)).limit(1);

    if (existingPermission.length === 0) {
      await db.insert(permissions).values({
        name: value,
        description: `${key} izni`
      });
      console.log(`İzin oluşturuldu: ${value}`);
    }
  }
}

// Kullanıcılara rollerine göre izinleri ata
async function assignPermissionsToUsers() {
  console.log('Kullanıcı izinleri atanıyor...');

  // Tüm kullanıcıları al
  const allUsers = await storage.getAllUsers();

  for (const user of allUsers) {
    console.log(`Kullanıcı izinleri atanıyor: ${user.username} (${user.role})`);

    // Kullanıcının rolünü belirle
    const userRole = user.role.toLowerCase() === 'admin'
      ? USER_ROLES.ADMIN
      : USER_ROLES.USER;

    // Bu role ait izinleri al
    const rolePermissions = userRole.permissions;

    // Veritabanından izin kayıtlarını al
    const permissionRecords = await db.select().from(permissions)
      .where(sql`${permissions.name} IN (${rolePermissions.join(', ')})`);

    // Kullanıcıya izinleri ata
    for (const permission of permissionRecords) {
      // Önce bu izin zaten atanmış mı kontrol et
      const existingUserPermission = await db.select().from(userPermissions)
        .where(
          eq(userPermissions.userId, user.id),
          eq(userPermissions.permissionId, permission.id)
        ).limit(1);

      if (existingUserPermission.length === 0) {
        await db.insert(userPermissions).values({
          userId: user.id,
          permissionId: permission.id
        });
        console.log(`İzin atandı: Kullanıcı ${user.username}, İzin: ${permission.name}`);
      }
    }
  }
}

// Varsayılan bir proje oluştur
export async function createDefaultProjectIfNeeded() {
  console.log('Varsayılan proje kontrolü yapılıyor...');

  // Hiç proje var mı kontrol et
  const existingProjects = await db.select().from(projects).limit(1);

  if (existingProjects.length === 0) {
    console.log('Varsayılan proje oluşturuluyor...');

    // Admin kullanıcısını bul
    const adminUser = await storage.getUserByUsername('admin');

    if (!adminUser) {
      console.error('Admin kullanıcısı bulunamadı, varsayılan proje oluşturulamıyor');
      return;
    }

    // Varsayılan proje oluştur
    const [newProject] = await db.insert(projects).values({
      name: 'Genel Projeler',
      description: 'Varsayılan proje alanı. Tüm dokümanlar burada görüntülenebilir.',
      createdBy: adminUser.id,
      status: 'active'
    }).returning();

    console.log(`Varsayılan proje oluşturuldu: ID=${newProject.id}, Ad=${newProject.name}`);

    // Tüm kullanıcılara bu projeye erişim ver
    const allUsers = await storage.getAllUsers();

    for (const user of allUsers) {
      await db.insert(userProjects).values({
        userId: user.id,
        projectId: newProject.id,
        role: user.role.toLowerCase() === 'admin' ? 'owner' : 'editor'
      });

      console.log(`Kullanıcı ${user.username} varsayılan projeye eklendi.`);
    }
  } else {
    console.log('Zaten projeler mevcut, varsayılan proje oluşturulmadı');
  }
}