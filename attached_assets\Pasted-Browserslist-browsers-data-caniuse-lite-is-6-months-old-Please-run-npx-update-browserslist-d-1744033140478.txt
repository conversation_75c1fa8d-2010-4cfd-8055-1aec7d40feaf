Browserslist: browsers data (caniuse-lite) is 6 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
1:37:32 PM [express] GET /api/documents 304 in 2ms :: [{"id":1,"name":"demo.docx","type":"docx","con…
1:37:39 PM [express] GET /api/documents/1 304 in 1ms :: {"id":1,"name":"demo.docx","type":"docx","co…
1:37:39 PM [express] GET /api/documents/1/ai-analysis 404 in 1ms :: {"error":"Bu doküman için AI ana…
1:37:39 PM [express] GET /api/documents/1/components 304 in 1ms :: []
1:37:39 PM [express] GET /api/documents/1/api-endpoints 304 in 0ms :: []
1:37:39 PM [express] GET /api/documents/1/requirements 304 in 1ms :: []
1:37:41 PM [express] GET /api/documents/1/test-scenarios 304 in 1ms :: []
1:37:41 PM [express] POST /api/documents/1/generate-test-scenarios 400 in 1ms :: {"error":"Bu doküma…
Doküman analiz hatası: SqliteError: no such column: analyzed_at
    at Database.prepare (/home/<USER>/workspace/node_modules/better-sqlite3/lib/methods/wrappers.js:5:21)
    at <anonymous> (/home/<USER>/workspace/server/storage-sqlite.ts:157:21)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async SqliteStorage.runInTransaction (/home/<USER>/workspace/server/storage-sqlite.ts:88:22) {
  code: 'SQLITE_ERROR'
}
1:38:11 PM [express] POST /api/documents/upload 201 in 19884ms :: {"document":{"id":2,"name":"demo.d…
1:38:11 PM [express] GET /api/documents 200 in 2ms :: [{"id":2,"name":"demo.docx","type":"docx","con…
1:38:14 PM [express] GET /api/documents/2 200 in 1ms :: {"id":2,"name":"demo.docx","type":"docx","co…
1:38:14 PM [express] GET /api/documents/2/requirements 200 in 1ms :: [{"id":1,"document_id":2,"code"…
1:38:14 PM [express] GET /api/documents/2/test-scenarios 200 in 1ms :: []
1:38:14 PM [express] GET /api/documents/2/components 200 in 1ms :: [{"id":1,"document_id":2,"name":"…
1:38:14 PM [express] GET /api/documents/2/api-endpoints 200 in 1ms :: [{"id":1,"document_id":2,"url"…
1:38:14 PM [express] GET /api/documents/2/ai-analysis 200 in 1ms :: {"id":1,"document_id":2,"observa…
1:38:15 PM [express] GET /api/documents/1/ai-analysis 404 in 1ms :: {"error":"Bu doküman için AI ana…
1:38:19 PM [express] GET /api/documents/1/ai-analysis 404 in 1ms :: {"error":"Bu doküman için AI ana…
Test senaryoları oluşturulurken hata: RangeError: Too few parameter values were provided
    at <anonymous> (/home/<USER>/workspace/server/storage-sqlite.ts:249:9)
    at SqliteStorage.runInTransaction (/home/<USER>/workspace/server/storage-sqlite.ts:88:28)
    at SqliteStorage.createTestScenario (/home/<USER>/workspace/server/storage-sqlite.ts:244:17)
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:234:45)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1:38:41 PM [express] POST /api/documents/2/generate-test-scenarios 500 in 19600ms :: {"error":"Test …