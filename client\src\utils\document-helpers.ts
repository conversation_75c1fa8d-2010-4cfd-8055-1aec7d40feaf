/**
 * Format file size in human-readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Check if file type is supported
 */
export function isSupportedFileType(filename: string): boolean {
  const extension = filename.split('.').pop()?.toLowerCase();
  return ['pdf', 'docx', 'doc'].includes(extension || '');
}

/**
 * Get document icon based on file type
 */
export function getDocumentIcon(type: string): string {
  switch (type.toLowerCase()) {
    case 'pdf':
      return 'fa-file-pdf';
    case 'docx':
    case 'doc':
      return 'fa-file-word';
    default:
      return 'fa-file-alt';
  }
}

/**
 * Get document icon color based on file type
 */
export function getDocumentIconColor(type: string): string {
  switch (type.toLowerCase()) {
    case 'pdf':
      return 'text-red-600';
    case 'docx':
    case 'doc':
      return 'text-blue-600';
    default:
      return 'text-neutral-700';
  }
}

/**
 * Extract text snippet from document content
 * Useful for previews or search results
 */
export function extractTextSnippet(content: string, maxLength = 150): string {
  if (!content) return '';
  
  // Remove extra whitespace
  const cleanText = content.replace(/\s+/g, ' ').trim();
  
  if (cleanText.length <= maxLength) {
    return cleanText;
  }
  
  return cleanText.substring(0, maxLength) + '...';
}

/**
 * Calculate read time for document content
 * Based on average reading speed of 200 words per minute
 */
export function calculateReadTime(content: string): number {
  if (!content) return 0;
  
  const wordCount = content.trim().split(/\s+/).length;
  const readTimeMinutes = Math.ceil(wordCount / 200);
  
  return readTimeMinutes;
}

export default {
  formatFileSize,
  isSupportedFileType,
  getDocumentIcon,
  getDocumentIconColor,
  extractTextSnippet,
  calculateReadTime
};
