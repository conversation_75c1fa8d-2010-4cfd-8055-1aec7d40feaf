  Why you should do it regularly: https://github.com/browserslist/update-db#readme
2:41:17 PM [express] GET /api/ai-models 304 in 70ms :: {"models":{"o1":{"displayName":"GPT-o1","depl…
2:41:18 PM [express] GET /api/documents 304 in 610ms :: [{"id":3,"name":"demo.docx","type":"docx","c…
2:41:19 PM [express] GET /api/documents/6/components 304 in 141ms :: [{"id":22,"documentId":6,"name"…
2:41:19 PM [express] GET /api/documents/6 304 in 190ms :: {"id":6,"name":"AD_TLP-2023-000342_Vergi_O…
2:41:19 PM [express] GET /api/documents/6/api-endpoints 304 in 78ms :: [{"id":10,"documentId":6,"url…
2:41:20 PM [express] GET /api/documents/6/requirements 304 in 136ms :: [{"id":47,"documentId":6,"cod…
2:41:20 PM [express] GET /api/documents/6/ai-analysis 304 in 134ms :: {"id":5,"documentId":6,"observ…
2:41:20 PM [express] GET /api/documents/6/test-scenarios 304 in 131ms :: [{"id":47,"documentId":6,"t…
AI Assistant isteği: {
  message: 'Aşağıdaki test senaryosunu gherkin formatında otomasyon koduna dönüştür. \n' +
    '                 \n' +
    '# Gherki...'
}
o1/o3 modelleri için Azure OpenAI API anahtarı: ******GrPml
o1 modeli için API isteği hazırlanıyor...
API parametreleri: {"model":"netas-ai-o1","messages":[{"role":"user","content":"\n    Sen bir doküman analiz asistanısın. Teknik dokümanları analiz edip, gereksinimleri ve test senaryolarını belirleyebilirsin.\n    \n    \n    \n    Aşağıdaki soruya Türkçe olarak cevap ver:\n    \n    Aşağıdaki test senaryosunu gherkin formatında otomasyon koduna dönüştür. \n                 \n# Gherkin formatında mutlaka şu yapıyı kullan:\nFeature: [Senaryo Başlığı]\n  Scenario: [Senaryo Açıklaması]\n    Given [Ön koşullar]\n    When [Adımlar - her biri ayrı When olmalı]\n    Then [Beklenen sonuç - her biri ayrı Then olmalı]\n\nÖrnek:\nFeature: Login Functionality\n  Scenario: Successful login with valid credentials\n    Given user is on the login page\n    When user enters username \"user1\"\n    And user enters password \"pass1\"\n    And user clicks on login button\n    Then user should be navigated to home page\n    And user should see welcome message\n\n                 \n                 Senaryo Başlığı: ******* - Harç Ödeme Yetkisi Olan Vergi Türü Seçilmesi Durumunda Vergi Dairesi Filtrelemesi\n                 Gereksinim: *******\n                 Ön Koşullar: {\"Kullanıcının BİŞ/KİŞ/KKGİŞ kanalına başarıyla giriş yapmış olması gerekir. Bu bağlantı dokümanda belirtilen kanal kodlarından herhangi biri üzerinden sağlanmalıdır (örneğin BİŞ: 050101).\",\"RSA_TVERGI_TUR tablosunda RSA_VTR_YETKI_VRGDAIRE_ODEME parametresi ’E’ olan en az bir vergi türü tanımlı olmalıdır. Dokümana göre 9265, 9264 gibi harç yetkisi olan vergi türlerinin RSA_VTR_YETKI_VRGDAIRE_ODEME değeri ‘E’ olmalıdır.\"}\n                 Adımlar: 1. Kullanıcı internet şube üzerinden 'Ödemeler -> Vergi / Para Cezası -> Vergi Öde' menüsüne tıklar ve Vergi Öde fonksiyonuna erişir.\n2. Açılan Vergi Öde sayfasında Bilgi Girişi kırılımında 'Vergi Türü Seçerek' seçeneği seçilir ve Ana Vergi Kodu/Türü alanındaki combobox görüntülenir.\n3. Combobox içinden harç ödeme yetkisi olan bir vergi türü (örneğin kodu 9265 olan SÜRÜCÜ BELGESİ HARCI) kullanıcı tarafından seçilir.\n4. Kullanıcı, Vergi Dairesi seçimi alanına geçtiğinde, sistem RSA_TVERGI_DAIRE tablosunda RSA_VRD_HARC_ODM_YET=’E’ olan kayıtları listelemelidir.\n5. Kullanıcı listeyi ekran üzerinde inceler ve yalnızca harç yetkisi olan vergi dairelerinin görüntülendiğini doğrular.\n                 Beklenen Sonuç: Sistem, harç ödeme yetkili vergi türü seçildiğinde sadece RSA_VRD_HARC_ODM_YET=’E’ olan vergi dairelerini listelemelidir. Seçim sonrasında kullanıcı ödeme adımlarına normal şekilde devam edebilmelidir.\n    "}],"max_completion_tokens":2000}
2:41:42 PM [express] POST /api/ai-assistant 200 in 19155ms :: {"answer":"Yanıt oluşturulamadı.","suc…