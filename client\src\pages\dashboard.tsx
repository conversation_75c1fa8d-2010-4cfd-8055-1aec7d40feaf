import { useState, useEffect } from "react";
import Header from "@/components/header";
import Sidebar from "@/components/sidebar";
import DocumentViewer from "@/components/document-viewer";
import ExtractedInfo from "@/components/extracted-info";
import StatusBar from "@/components/status-bar";
import TestScenarios from "@/components/test-scenarios";
import CoverageAnalysis from "@/components/coverage-analysis";
import DocumentUploadModal from "@/components/document-upload-modal";
import { useQuery } from "@tanstack/react-query";
import { type Document, type Component, type Requirement, type ApiEndpoint, type AiAnalysis } from "@shared/schema";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import { 
  FileText, 
  CheckSquare, 
  PieChart, 
  ChevronLeft, 
  ChevronRight,
  FileQuestion,
  FilePlus
} from "lucide-react";

type TabType = "document" | "test-scenarios" | "coverage";

const Dashboard = () => {
  const [activeTab, setActiveTab] = useState<TabType>("document");
  const [activeDocumentId, setActiveDocumentId] = useState<number | null>(null);
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [documentsLoaded, setDocumentsLoaded] = useState(false);

  // Fetch documents (bu endpoint sadece erişimi olan dokümanları döndürür)
  const { data: documents = [], error: documentsError, isLoading: isDocumentsLoading } = useQuery<Document[]>({
    queryKey: ["/api/documents"],
    refetchOnWindowFocus: true, // Sayfa yeniden odaklandığında yenileme yap
  });
  
  // Kaynakların yüklenme durumunu izle
  useEffect(() => {
    if (!isDocumentsLoading) {
      setDocumentsLoaded(true);
    }
  }, [isDocumentsLoading]);

  // Fetch active document details
  const { data: activeDocument, error: activeDocumentError } = useQuery<Document>({
    queryKey: [`/api/documents/${activeDocumentId}`],
    enabled: activeDocumentId !== null,
  });

  // Fetch components for active document
  const { data: components = [] } = useQuery<Component[]>({
    queryKey: [`/api/documents/${activeDocumentId}/components`],
    enabled: activeDocumentId !== null,
  });

  // Fetch requirements for active document
  const { data: requirements = [] } = useQuery<Requirement[]>({
    queryKey: [`/api/documents/${activeDocumentId}/requirements`],
    enabled: activeDocumentId !== null,
  });

  // Fetch API endpoints for active document
  const { data: apiEndpoints = [] } = useQuery<ApiEndpoint[]>({
    queryKey: [`/api/documents/${activeDocumentId}/api-endpoints`],
    enabled: activeDocumentId !== null,
  });

  // Fetch AI analysis for active document
  const { data: aiAnalysis } = useQuery<AiAnalysis>({
    queryKey: [`/api/documents/${activeDocumentId}/ai-analysis`],
    enabled: activeDocumentId !== null,
  });

  // Fetch test scenarios for active document
  const { data: testScenarios = [] } = useQuery({
    queryKey: [`/api/documents/${activeDocumentId}/test-scenarios`],
    enabled: activeDocumentId !== null && (activeTab === "test-scenarios" || activeTab === "coverage"),
  });

  // useEffect ile kullanıcı değişikliğini izleyelim
  const { user } = useAuth();
  
  // Kullanıcı değiştiğinde state'leri sıfırla
  useEffect(() => {
    setActiveDocumentId(null);
    setActiveTab("document");
    setDocumentsLoaded(false);
  }, [user?.id]);
  
  // Handle document selection
  const handleDocumentSelect = (documentId: number) => {
    // Eğer 0 ise seçimi kaldır (doküman silme işleminden sonra)
    if (documentId === 0) {
      setActiveDocumentId(null);
      return;
    }
    
    setActiveDocumentId(documentId);
    // Doküman seçildiğinde otomatik olarak doküman analiz tabına geç
    setActiveTab("document");
  };

  // Toggle upload modal
  const toggleUploadModal = () => {
    setIsUploadModalOpen(!isUploadModalOpen);
  };

  // Toggle sidebar
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value as TabType);
  };

  return (
    <div className="bg-neutral-50 text-neutral-900 h-screen flex flex-col overflow-hidden">
      <Header />
      
      <div className="flex-1 flex overflow-hidden">
        <div className={`transition-all duration-300 ${sidebarCollapsed ? 'w-0' : 'w-64'}`}>
          {!sidebarCollapsed && (
            <Sidebar 
              documents={documents} 
              activeDocumentId={activeDocumentId} 
              onDocumentSelect={handleDocumentSelect}
              onUploadClick={toggleUploadModal}
              documentsLoaded={documentsLoaded}
            />
          )}
        </div>

        <button 
          onClick={toggleSidebar}
          className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white border border-neutral-200 rounded-r-md p-1.5 shadow-sm hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary"
          aria-label={sidebarCollapsed ? "Kenar çubuğunu aç" : "Kenar çubuğunu kapat"}
        >
          {sidebarCollapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
        </button>
        
        <main className="flex-1 flex flex-col overflow-hidden bg-white rounded-tl-lg shadow-sm mx-2 my-2">
          {/* Modern Tab Navigation */}
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full h-full flex flex-col">
            <div className="border-b border-neutral-200 bg-white px-5 py-2 flex-shrink-0">
              <TabsList className="bg-neutral-100 p-1">
                <TabsTrigger value="document" className="flex items-center gap-2 px-4 py-2">
                  <FileText size={16} />
                  <span>Kaynak Analizi</span>
                </TabsTrigger>
                <TabsTrigger value="test-scenarios" className="flex items-center gap-2 px-4 py-2">
                  <CheckSquare size={16} />
                  <span>Test Senaryoları</span>
                </TabsTrigger>
                <TabsTrigger value="coverage" className="flex items-center gap-2 px-4 py-2">
                  <PieChart size={16} />
                  <span>Kapsam Analizi</span>
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Main Content Based on Active Tab */}
            <div className="flex-1 overflow-hidden flex flex-col">
              {activeTab === 'document' && (
                <TabsContent value="document" className="h-full flex-1 mt-0 border-none p-0 flex flex-col">
                  {documentsError || (documents && documents.length === 0) ? (
                    <div className="flex h-full items-center justify-center p-6">
                      <div className="max-w-lg p-8 shadow-lg border-neutral-200 bg-white rounded-lg text-center">
                        <FileQuestion size={48} className="mx-auto mb-4 text-neutral-400" />
                        <h3 className="text-lg font-medium mb-2">Henüz kaynak yüklenmemiş</h3>
                        <p className="text-neutral-500 text-sm mb-4">
                          Başlamak için sol menüden "Kaynak Ekle" butonuna tıklayarak bir kaynak yükleyin
                        </p>
                        <Button
                          variant="default"
                          size="sm"
                          className="mt-2"
                          onClick={toggleUploadModal}
                        >
                          <FilePlus size={18} className="mr-2" />
                          <span>Kaynak Ekle</span>
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex w-full h-full flex-1">
                      <DocumentViewer 
                        document={activeDocument}
                        error={activeDocumentError}
                      />
                      <ExtractedInfo 
                        components={components}
                        requirements={requirements}
                        apiEndpoints={apiEndpoints}
                        aiAnalysis={aiAnalysis}
                      />
                    </div>
                  )}
                </TabsContent>
              )}

              {activeTab === 'test-scenarios' && (
                <TabsContent value="test-scenarios" className="h-full flex-1 mt-0 border-none p-0">
                  {documentsError || (documents && documents.length === 0) ? (
                    <div className="flex h-full items-center justify-center p-6">
                      <div className="max-w-lg p-8 shadow-lg border-neutral-200 bg-white rounded-lg text-center">
                        <FileQuestion size={48} className="mx-auto mb-4 text-neutral-400" />
                        <h3 className="text-lg font-medium mb-2">Henüz kaynak yüklenmemiş</h3>
                        <p className="text-neutral-500 text-sm mb-4">
                          Test senaryoları oluşturmak için önce bir kaynak yüklemelisiniz
                        </p>
                        <Button
                          variant="default"
                          size="sm"
                          className="mt-2"
                          onClick={toggleUploadModal}
                        >
                          <FilePlus size={18} className="mr-2" />
                          <span>Kaynak Ekle</span>
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <TestScenarios 
                      documentId={activeDocumentId}
                      requirements={requirements}
                      testScenarios={testScenarios}
                    />
                  )}
                </TabsContent>
              )}

              {activeTab === 'coverage' && (
                <TabsContent value="coverage" className="h-full flex-1 mt-0 border-none p-0">
                  {documentsError || (documents && documents.length === 0) ? (
                    <div className="flex h-full items-center justify-center p-6">
                      <div className="max-w-lg p-8 shadow-lg border-neutral-200 bg-white rounded-lg text-center">
                        <FileQuestion size={48} className="mx-auto mb-4 text-neutral-400" />
                        <h3 className="text-lg font-medium mb-2">Henüz kaynak yüklenmemiş</h3>
                        <p className="text-neutral-500 text-sm mb-4">
                          Kapsam analizi yapmak için önce bir kaynak yüklemelisiniz
                        </p>
                        <Button
                          variant="default"
                          size="sm"
                          className="mt-2"
                          onClick={toggleUploadModal}
                        >
                          <FilePlus size={18} className="mr-2" />
                          <span>Kaynak Ekle</span>
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <CoverageAnalysis 
                      requirements={requirements}
                      testScenarios={testScenarios}
                    />
                  )}
                </TabsContent>
              )}
            </div>
          </Tabs>
        </main>
      </div>

      <StatusBar 
        componentsCount={components.length}
        requirementsCount={requirements.length}
        apiEndpointsCount={apiEndpoints.length}
        lastUpdated={activeDocument?.analyzedAt}
      />

      <DocumentUploadModal 
        isOpen={isUploadModalOpen} 
        onClose={toggleUploadModal} 
      />
    </div>
  );
};

export default Dashboard;
