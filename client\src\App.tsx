import { Switch, Route } from "wouter";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient";
import Dashboard from "./pages/dashboard";
import AIAssistant from "./pages/ai-assistant";
import PromptEditor from "./pages/prompt-editor";
import NotFound from "./pages/not-found";
import AuthPage from "./pages/auth-page";
import { ProtectedRoute } from "./lib/protected-route";
import { AuthProvider } from "./hooks/use-auth";
import { PermissionProvider } from "./hooks/use-permission";
import { Toaster } from "@/components/ui/toaster";

// Yeni eklenecek sayfalar
import UserProfile from "./pages/user-profile";
import Settings from "./pages/settings";
import Projects from "./pages/projects";
import ProjectDetails from "./pages/project-details";

function Router() {
  return (
    <Switch>
      <ProtectedRoute path="/" component={Dashboard} />
      <ProtectedRoute path="/ai-assistant" component={AIAssistant} />
      <ProtectedRoute 
        path="/prompt-editor" 
        component={PromptEditor} 
        requiredPermission="prompt_management" 
      />
      <ProtectedRoute path="/profile" component={UserProfile} />
      <ProtectedRoute path="/settings" component={Settings} />
      <ProtectedRoute path="/projects" component={Projects} />
      <ProtectedRoute path="/projects/:id" component={ProjectDetails} />
      <Route path="/auth" component={AuthPage} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <PermissionProvider>
          <Router />
          <Toaster />
        </PermissionProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
