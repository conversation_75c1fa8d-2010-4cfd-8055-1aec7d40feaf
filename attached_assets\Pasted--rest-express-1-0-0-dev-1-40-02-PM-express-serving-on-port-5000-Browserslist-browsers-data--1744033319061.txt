
> rest-express@1.0.0 dev
1:40:02 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 6 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
1:40:05 PM [express] GET /api/documents 304 in 3ms :: [{"id":2,"name":"demo.docx","type":"docx","con…
1:40:10 PM [express] GET /api/documents 200 in 2ms :: [{"id":2,"name":"demo.docx","type":"docx","con…
1:41:03 PM [express] POST /api/documents/upload 201 in 17142ms :: {"document":{"id":3,"name":"demo.d…
1:41:03 PM [express] GET /api/documents 200 in 3ms :: [{"id":3,"name":"demo.docx","type":"docx","con…
1:41:06 PM [express] GET /api/documents/3 200 in 1ms :: {"id":3,"name":"demo.docx","type":"docx","co…
1:41:06 PM [express] GET /api/documents/3/ai-analysis 200 in 0ms :: {"id":2,"document_id":3,"observa…
1:41:06 PM [express] GET /api/documents/3/components 200 in 1ms :: [{"id":4,"document_id":3,"name":"…
1:41:06 PM [express] GET /api/documents/3/requirements 200 in 1ms :: [{"id":10,"document_id":3,"code…
1:41:06 PM [express] GET /api/documents/3/api-endpoints 200 in 1ms :: [{"id":2,"document_id":3,"url"…
1:41:07 PM [express] GET /api/documents/2/components 304 in 1ms :: [{"id":1,"document_id":2,"name":"…
1:41:07 PM [express] GET /api/documents/2/api-endpoints 304 in 1ms :: [{"id":1,"document_id":2,"url"…
1:41:07 PM [express] GET /api/documents/2 304 in 1ms :: {"id":2,"name":"demo.docx","type":"docx","co…
1:41:07 PM [express] GET /api/documents/2/requirements 304 in 0ms :: [{"id":1,"document_id":2,"code"…
1:41:07 PM [express] GET /api/documents/2/ai-analysis 304 in 0ms :: {"id":1,"document_id":2,"observa…
1:41:11 PM [express] GET /api/documents/3/test-scenarios 200 in 1ms :: []
Test senaryosu ekleniyor: {
  documentId: 3,
  title: 'Kaptan Ses Dosyalarının Görüntülenmesi ve Çalınması',
  preconditions: [
    'Kaptan bir ses dosyası yüklemiş ve bu rota için izin verildiği doğrulanmış olmalı.',
    'Uygulama yüklü ve çalışıyor olmalı.'
  ],
  steps: '["Uygulamayı aç ve giriş yap.","Belirli bir rotayı seç.","Kaptan tarafından eklenen ses dosyasını görüntüle.","Play butonuna bas."]',
  expectedResults: 'Ses dosyası başarıyla oynatılmalı ve yolcular tarafından duyulmalı.',
  requirementCode: 'REQ-001'
}
Test senaryosu oluşturulurken hata: RangeError: Too many parameter values were provided
    at <anonymous> (/home/<USER>/workspace/server/storage-sqlite.ts:264:11)
    at SqliteStorage.runInTransaction (/home/<USER>/workspace/server/storage-sqlite.ts:88:28)
    at SqliteStorage.createTestScenario (/home/<USER>/workspace/server/storage-sqlite.ts:246:17)
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:241:45)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
Test senaryoları oluşturulurken hata: RangeError: Too many parameter values were provided
    at <anonymous> (/home/<USER>/workspace/server/storage-sqlite.ts:264:11)
    at SqliteStorage.runInTransaction (/home/<USER>/workspace/server/storage-sqlite.ts:88:28)
    at SqliteStorage.createTestScenario (/home/<USER>/workspace/server/storage-sqlite.ts:246:17)
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:241:45)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
1:41:40 PM [express] POST /api/documents/3/generate-test-scenarios 500 in 26041ms :: {"error":"Test …