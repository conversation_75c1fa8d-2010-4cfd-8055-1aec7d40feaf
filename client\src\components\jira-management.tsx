import React, { useState, useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import IssueListView from '@/components/jira-views/issue-list-view';
import KanbanView from '@/components/jira-views/kanban-view';
import BacklogView from '@/components/jira-views/backlog-view';
import IssueDetailPopup from '@/components/jira-views/issue-detail-popup';
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Calendar,
  User,
  Flag,
  CheckCircle,
  Circle,
  Clock,
  ArrowRight,
  Maximize2,
  X
} from 'lucide-react';

interface JiraIssue {
  id: string;
  key: string;
  summary: string;
  description: string;
  issueType: string;
  status: string;
  priority: string;
  assignee?: string;
  reporter?: string;
  created: string;
  updated: string;
  comments: Array<{
    id: string;
    author: string;
    body: string;
    created: string;
  }>;
  attachments: Array<{
    id: string;
    filename: string;
    size: number;
    mimeType: string;
    author: string;
    created: string;
    content: string;
  }>;
}

interface JiraConnection {
  id: number;
  name: string;
  baseUrl: string;
  username: string;
  token: string;
  status: string;
}

interface JiraProject {
  id: string;
  key: string;
  name: string;
  description?: string;
  lead?: string;
}

interface JiraManagementProps {
  projectId: number;
}

const ISSUE_TYPES = [
  { value: 'Story', label: 'Story', color: 'bg-green-100 text-green-800' },
  { value: 'Bug', label: 'Bug', color: 'bg-red-100 text-red-800' },
  { value: 'Task', label: 'Task', color: 'bg-blue-100 text-blue-800' },
  { value: 'Epic', label: 'Epic', color: 'bg-purple-100 text-purple-800' },
  { value: 'Subtask', label: 'Subtask', color: 'bg-gray-100 text-gray-800' }
];

const ISSUE_STATUSES = [
  { value: 'To Do', label: 'To Do', color: 'bg-gray-100 text-gray-800' },
  { value: 'In Progress', label: 'In Progress', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'In Review', label: 'In Review', color: 'bg-blue-100 text-blue-800' },
  { value: 'Done', label: 'Done', color: 'bg-green-100 text-green-800' },
  { value: 'Blocked', label: 'Blocked', color: 'bg-red-100 text-red-800' }
];

const PRIORITIES = [
  { value: 'Highest', label: 'Highest', color: 'bg-red-100 text-red-800', icon: '🔴' },
  { value: 'High', label: 'High', color: 'bg-orange-100 text-orange-800', icon: '🟠' },
  { value: 'Medium', label: 'Medium', color: 'bg-yellow-100 text-yellow-800', icon: '🟡' },
  { value: 'Low', label: 'Low', color: 'bg-green-100 text-green-800', icon: '🟢' },
  { value: 'Lowest', label: 'Lowest', color: 'bg-gray-100 text-gray-800', icon: '⚪' }
];

export default function JiraManagement({ projectId }: JiraManagementProps) {
  const [selectedConnection, setSelectedConnection] = useState<JiraConnection | null>(null);
  const [selectedProject, setSelectedProject] = useState<JiraProject | null>(null);
  const [selectedIssue, setSelectedIssue] = useState<JiraIssue | null>(null);
  const [isIssuePopupOpen, setIsIssuePopupOpen] = useState(false);
  const [isPopupMaximized, setIsPopupMaximized] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [priorityFilter, setPriorityFilter] = useState<string>('');
  const [viewMode, setViewMode] = useState<'list' | 'kanban' | 'backlog'>('list');

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Jira bağlantılarını getir
  const { data: connections = [] } = useQuery<JiraConnection[]>({
    queryKey: [`/api/projects/${projectId}/connections`],
    queryFn: async () => {
      const response = await apiRequest('GET', `/api/projects/${projectId}/connections?type=jira`);
      return response.json();
    }
  });

  // Jira projelerini getir
  const { data: jiraProjects = [] } = useQuery<JiraProject[]>({
    queryKey: ['jira-projects', selectedConnection?.id],
    queryFn: async () => {
      if (!selectedConnection) return [];
      const response = await apiRequest('POST', '/api/integrations/jira/projects', {
        url: selectedConnection.baseUrl,
        username: selectedConnection.username,
        apiToken: selectedConnection.token
      });
      const data = await response.json();
      return data.projects || [];
    },
    enabled: !!selectedConnection
  });

  // Jira issue'larını getir
  const { data: jiraIssues = [], isLoading: issuesLoading } = useQuery<JiraIssue[]>({
    queryKey: ['jira-issues', selectedConnection?.id, selectedProject?.key],
    queryFn: async () => {
      if (!selectedConnection || !selectedProject) return [];
      const response = await apiRequest('POST', '/api/integrations/jira/issues', {
        url: selectedConnection.baseUrl,
        username: selectedConnection.username,
        apiToken: selectedConnection.token,
        projectKey: selectedProject.key
      });
      const data = await response.json();
      return data.issues || [];
    },
    enabled: !!selectedConnection && !!selectedProject
  });

  // Filtrelenmiş issue'lar
  const filteredIssues = useMemo(() => {
    return jiraIssues.filter(issue => {
      const matchesSearch = !searchTerm ||
        issue.summary.toLowerCase().includes(searchTerm.toLowerCase()) ||
        issue.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
        issue.description.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = !statusFilter || issue.status === statusFilter;
      const matchesType = !typeFilter || issue.issueType === typeFilter;
      const matchesPriority = !priorityFilter || issue.priority === priorityFilter;

      return matchesSearch && matchesStatus && matchesType && matchesPriority;
    });
  }, [jiraIssues, searchTerm, statusFilter, typeFilter, priorityFilter]);

  // Issue silme
  const deleteIssueMutation = useMutation({
    mutationFn: async (issueKey: string) => {
      if (!selectedConnection) throw new Error('Bağlantı seçilmedi');

      const response = await apiRequest('DELETE', `/api/integrations/jira/issues/${issueKey}`, {
        url: selectedConnection.baseUrl,
        username: selectedConnection.username,
        apiToken: selectedConnection.token
      });
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Issue silindi',
        description: 'Issue başarıyla silindi.',
      });
      queryClient.invalidateQueries({ queryKey: ['jira-issues'] });
    },
    onError: (error: any) => {
      toast({
        title: 'Silme hatası',
        description: error.message || 'Issue silinemedi.',
        variant: 'destructive',
      });
    }
  });

  // Issue popup'ını aç
  const openIssuePopup = useCallback((issue: JiraIssue) => {
    setSelectedIssue(issue);
    setIsIssuePopupOpen(true);
    setIsPopupMaximized(false);
  }, []);

  // Issue popup'ını kapat
  const closeIssuePopup = useCallback(() => {
    setSelectedIssue(null);
    setIsIssuePopupOpen(false);
    setIsPopupMaximized(false);
  }, []);

  // Popup'ı büyüt/küçült
  const togglePopupSize = useCallback(() => {
    setIsPopupMaximized(!isPopupMaximized);
  }, [isPopupMaximized]);

  // Priority badge'i al
  const getPriorityBadge = (priority: string) => {
    const priorityConfig = PRIORITIES.find(p => p.value === priority);
    if (!priorityConfig) return null;

    return (
      <Badge variant="secondary" className={priorityConfig.color}>
        <span className="mr-1">{priorityConfig.icon}</span>
        {priorityConfig.label}
      </Badge>
    );
  };

  // Status badge'i al
  const getStatusBadge = (status: string) => {
    const statusConfig = ISSUE_STATUSES.find(s => s.value === status);
    if (!statusConfig) return null;

    return (
      <Badge variant="secondary" className={statusConfig.color}>
        {statusConfig.label}
      </Badge>
    );
  };

  // Issue type badge'i al
  const getTypeBadge = (type: string) => {
    const typeConfig = ISSUE_TYPES.find(t => t.value === type);
    if (!typeConfig) return null;

    return (
      <Badge variant="secondary" className={typeConfig.color}>
        {typeConfig.label}
      </Badge>
    );
  };

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="border-b border-gray-200 p-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Jira Proje Yönetimi</h2>
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              Liste
            </Button>
            <Button
              variant={viewMode === 'kanban' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('kanban')}
            >
              Kanban
            </Button>
            <Button
              variant={viewMode === 'backlog' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('backlog')}
            >
              Backlog
            </Button>
          </div>
        </div>

        {/* Bağlantı ve Proje Seçimi */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Jira Bağlantısı
            </label>
            <select
              value={selectedConnection?.id || ''}
              onChange={(e) => {
                const connection = connections.find(c => c.id === parseInt(e.target.value));
                setSelectedConnection(connection || null);
                setSelectedProject(null);
              }}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Bağlantı seçin...</option>
              {connections.map(connection => (
                <option key={connection.id} value={connection.id}>
                  {connection.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Jira Projesi
            </label>
            <select
              value={selectedProject?.key || ''}
              onChange={(e) => {
                const project = jiraProjects.find(p => p.key === e.target.value);
                setSelectedProject(project || null);
              }}
              disabled={!selectedConnection}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
            >
              <option value="">Proje seçin...</option>
              {jiraProjects.map(project => (
                <option key={project.key} value={project.key}>
                  {project.name} ({project.key})
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      {selectedProject && (
        <div className="border-b border-gray-200 p-4">
          <div className="flex flex-wrap items-center gap-4">
            {/* Search */}
            <div className="relative flex-1 min-w-64">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                type="text"
                placeholder="Issue ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Filters */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Tüm Durumlar</option>
              {ISSUE_STATUSES.map(status => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </select>

            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Tüm Tipler</option>
              {ISSUE_TYPES.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>

            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Tüm Öncelikler</option>
              {PRIORITIES.map(priority => (
                <option key={priority.value} value={priority.value}>
                  {priority.label}
                </option>
              ))}
            </select>

            {/* Clear Filters */}
            {(searchTerm || statusFilter || typeFilter || priorityFilter) && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('');
                  setTypeFilter('');
                  setPriorityFilter('');
                }}
              >
                Filtreleri Temizle
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        {!selectedConnection ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="text-gray-400 mb-4">
                <Calendar size={64} className="mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Jira Bağlantısı Seçin
              </h3>
              <p className="text-gray-500">
                Başlamak için bir Jira bağlantısı seçin
              </p>
            </div>
          </div>
        ) : !selectedProject ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="text-gray-400 mb-4">
                <Calendar size={64} className="mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Proje Seçin
              </h3>
              <p className="text-gray-500">
                Issue'ları görüntülemek için bir proje seçin
              </p>
            </div>
          </div>
        ) : issuesLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-500">Issue'lar yükleniyor...</p>
            </div>
          </div>
        ) : filteredIssues.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="text-gray-400 mb-4">
                <Search size={64} className="mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Issue Bulunamadı
              </h3>
              <p className="text-gray-500">
                {searchTerm || statusFilter || typeFilter || priorityFilter
                  ? 'Filtrelere uygun issue bulunamadı'
                  : 'Bu projede henüz issue yok'}
              </p>
            </div>
          </div>
        ) : (
          <div className="h-full">
            {viewMode === 'list' && (
              <IssueListView
                issues={filteredIssues}
                onIssueClick={openIssuePopup}
                onDeleteIssue={(issueKey) => deleteIssueMutation.mutate(issueKey)}
                getPriorityBadge={getPriorityBadge}
                getStatusBadge={getStatusBadge}
                getTypeBadge={getTypeBadge}
              />
            )}
            {viewMode === 'kanban' && (
              <KanbanView
                issues={filteredIssues}
                onIssueClick={openIssuePopup}
                getPriorityBadge={getPriorityBadge}
                getStatusBadge={getStatusBadge}
                getTypeBadge={getTypeBadge}
              />
            )}
            {viewMode === 'backlog' && (
              <BacklogView
                issues={filteredIssues}
                onIssueClick={openIssuePopup}
                getPriorityBadge={getPriorityBadge}
                getStatusBadge={getStatusBadge}
                getTypeBadge={getTypeBadge}
              />
            )}
          </div>
        )}
      </div>

      {/* Issue Detail Popup */}
      {isIssuePopupOpen && selectedIssue && (
        <IssueDetailPopup
          issue={selectedIssue}
          isMaximized={isPopupMaximized}
          onClose={closeIssuePopup}
          onToggleSize={togglePopupSize}
          connection={selectedConnection}
          project={selectedProject}
        />
      )}
    </div>
  );
}
