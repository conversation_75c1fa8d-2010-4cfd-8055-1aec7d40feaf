
> rest-express@1.0.0 dev
10:32:27 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 6 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
10:32:31 PM [express] GET /api/documents 304 in 5ms :: [{"id":20,"name":"demo.docx","type":"docx","co…
10:32:32 PM [express] GET /api/documents 304 in 1ms :: [{"id":20,"name":"demo.docx","type":"docx","co…
10:32:32 PM [express] GET /api/documents 304 in 1ms :: [{"id":20,"name":"demo.docx","type":"docx","co…
10:33:03 PM [express] POST /api/documents/upload 201 in 14324ms :: {"document":{"id":21,"name":"demo.…
10:33:04 PM [express] GET /api/documents 200 in 1ms :: [{"id":21,"name":"demo.docx","type":"docx","co…
10:33:18 PM [express] GET /api/documents/21/components 200 in 1ms :: [{"id":93,"document_id":21,"name…
10:33:18 PM [express] GET /api/documents/21/requirements 200 in 0ms :: [{"id":162,"document_id":21,"c…
10:33:18 PM [express] GET /api/documents/21 200 in 1ms :: {"id":21,"name":"demo.docx","type":"docx","…
10:33:18 PM [express] GET /api/documents/21/ai-analysis 200 in 1ms :: {"id":21,"document_id":21,"obse…
10:33:18 PM [express] GET /api/documents/21/api-endpoints 200 in 0ms :: [{"id":36,"document_id":21,"u…
10:33:21 PM [express] GET /api/documents/21/test-scenarios 200 in 1ms :: []
OpenAI ile test senaryoları oluşturma hatası: BadRequestError: 400 max_tokens is too large: 32000. This model supports at most 16384 completion tokens, whereas you provided 32000.
    at Function.generate (/home/<USER>/workspace/node_modules/openai/src/error.ts:72:14)
    at OpenAI.makeStatusError (/home/<USER>/workspace/node_modules/openai/src/core.ts:462:21)
    at OpenAI.makeRequest (/home/<USER>/workspace/node_modules/openai/src/core.ts:526:24)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Object.generateTestScenarios (/home/<USER>/workspace/server/services/openai.ts:217:22)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:241:35) {
  status: 400,
  headers: {
    'apim-request-id': '4cb65d8d-4be6-4130-94eb-371c7e594ca2',
    'azureml-model-session': 'v20250319-1-164616836',
    'content-length': '232',
    'content-type': 'application/json',
    date: 'Mon, 07 Apr 2025 22:33:25 GMT',
    'ms-azureml-model-error-reason': 'model_error',
    'ms-azureml-model-error-statuscode': '400',
    'ms-azureml-model-time': '36',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    'x-aml-cluster': 'hyena-japaneast-01',
    'x-content-type-options': 'nosniff',
    'x-envoy-upstream-service-time': '40',
    'x-ms-client-request-id': '4cb65d8d-4be6-4130-94eb-371c7e594ca2',
    'x-ms-rai-invoked': 'true',
    'x-ms-region': 'East US',
    'x-ratelimit-limit-requests': '450',
    'x-ratelimit-limit-tokens': '450000',
    'x-ratelimit-remaining-requests': '449',
    'x-ratelimit-remaining-tokens': '393027',
    'x-request-id': '08fed4f5-f802-454c-b13e-a26c348496f5'
  },
  request_id: '08fed4f5-f802-454c-b13e-a26c348496f5',
  error: {
    message: 'max_tokens is too large: 32000. This model supports at most 16384 completion tokens, whereas you provided 32000.',
    type: 'invalid_request_error',
    param: 'max_tokens',
    code: null
  },
  code: null,
  param: 'max_tokens',
  type: 'invalid_request_error'
}
Test senaryoları oluşturulurken hata: Error: Test senaryoları oluşturulamadı: 400 max_tokens is too large: 32000. This model supports at most 16384 completion tokens, whereas you provided 32000.
    at Object.generateTestScenarios (/home/<USER>/workspace/server/services/openai.ts:245:11)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async <anonymous> (/home/<USER>/workspace/server/routes.ts:241:35)
10:33:25 PM [express] POST /api/documents/21/generate-test-scenarios 500 in 501ms :: {"error":"Test s…