
> rest-express@1.0.0 dev
> tsx server/index.ts

2:42:03 PM [express] serving on port 5000
Browserslist: browsers data (caniuse-lite) is 6 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
2:42:06 PM [express] GET /api/documents 304 in 3ms :: [{"id":4,"name":"demo.docx","type":"docx","con…
2:42:07 PM [express] GET /api/documents 304 in 2ms :: [{"id":4,"name":"demo.docx","type":"docx","con…
2:42:08 PM [express] GET /api/documents 304 in 2ms :: [{"id":4,"name":"demo.docx","type":"docx","con…
2:42:20 PM [express] GET /api/documents/4/components 304 in 1ms :: [{"id":8,"document_id":4,"name":"…
2:42:20 PM [express] GET /api/documents/4 304 in 0ms :: {"id":4,"name":"demo.docx","type":"docx","co…
2:42:20 PM [express] GET /api/documents/4/ai-analysis 304 in 1ms :: {"id":3,"document_id":4,"observa…
2:42:20 PM [express] GET /api/documents/4/api-endpoints 304 in 1ms :: [{"id":3,"document_id":4,"url"…
2:42:20 PM [express] GET /api/documents/4/requirements 304 in 1ms :: [{"id":15,"document_id":4,"code…
2:42:25 PM [express] GET /api/documents/4/test-scenarios 304 in 2ms :: [{"id":35,"document_id":4,"ti…
2:42:36 PM [express] GET /api/documents/3/components 304 in 1ms :: [{"id":4,"document_id":3,"name":"…
2:42:36 PM [express] GET /api/documents/3 304 in 3ms :: {"id":3,"name":"demo.docx","type":"docx","co…
2:42:36 PM [express] GET /api/documents/3/requirements 304 in 1ms :: [{"id":10,"document_id":3,"code…
2:42:36 PM [express] GET /api/documents/3/ai-analysis 304 in 1ms :: {"id":2,"document_id":3,"observa…
2:42:36 PM [express] GET /api/documents/3/api-endpoints 304 in 1ms :: [{"id":2,"document_id":3,"url"…
2:42:40 PM [express] GET /api/documents/3/test-scenarios 304 in 1ms :: [{"id":1,"document_id":3,"tit…
2:43:13 PM [express] GET /api/documents 200 in 2ms :: [{"id":5,"name":"demo.docx","type":"docx","con…
2:43:52 PM [express] POST /api/documents/upload 201 in 47370ms :: {"document":{"id":5,"name":"demo.d…
2:43:53 PM [express] GET /api/documents 200 in 2ms :: [{"id":5,"name":"demo.docx","type":"docx","con…
2:43:56 PM [express] GET /api/documents/5 200 in 1ms :: {"id":5,"name":"demo.docx","type":"docx","co…
2:43:56 PM [express] GET /api/documents/5/requirements 200 in 0ms :: [{"id":21,"document_id":5,"code…
2:43:56 PM [express] GET /api/documents/5/api-endpoints 200 in 1ms :: [{"id":4,"document_id":5,"url"…
2:43:56 PM [express] GET /api/documents/5/components 200 in 1ms :: [{"id":13,"document_id":5,"name":…
2:43:56 PM [express] GET /api/documents/5/ai-analysis 200 in 0ms :: {"id":4,"document_id":5,"observa…
2:44:05 PM [express] GET /api/documents/5/test-scenarios 200 in 1ms :: []
OpenAI API'den alınan test senaryoları: {
  "scenarios": [
    {
      "title": "REQ-001: Anonsların Doğru Şekilde Oynatılması",
      "preconditions": [
        "Voice Readout modülü yüklenmiş ve çalışır durumda",
        "Uygulama online moda alınmış",
        "Pilotun uçuş bilgileri sistemde kayıtlı"
      ],
      "steps": [
        "Pilot uçuş bilgilerini sisteme girer.",
        "Voice Readout modülünde anonsları oynatma seçeneği seçilir.",
        "Modül, girilen uçuş bilgilerinin doğruluğunu kontrol eder ve anonsları oynatır."
      ],
      "expectedResults": "Uçuş bilgileri doğrulanır ve uygun anonslar doğru şekilde oynatılır.",
      "requirementCode": "REQ-001",
      "format": "default"
    },
    {
      "title": "REQ-002: Gün İçerisindeki Belirli Bir Zaman Dilimindeki Uçuşların Görüntülenmesi",
      "preconditions": [
        "Voice Readout ekranı açılmış",
        "Kullanıcı geçerli bir zaman dilimi seçmiş"
      ],
      "steps": [
        "Kullanıcı zaman aralığını belirler (örneğin, '08:00 - 14:00').",
        "Voice Readout ekranı, seçilen zaman dilimi için uçuş bilgilerini filtreler.",
        "Ekranda sadece belirtilen zaman dilimine ait uçuşlar görüntülenir."
      ],
      "expectedResults": "Seçilen zaman aralığına uygun uçuşlar doğru şekilde görüntülenir.",
      "requirementCode": "REQ-002",
      "format": "default"
    },
    {
      "title": "REQ-003: Offline Modda Daha Önceden İndirilen Ses Dosyalarının Kullanılabilirliği",
      "preconditions": [
        "Kullanıcı ses dosyasını online modda başarıyla indirmiş",
        "Uygulama offline moda alınmış"
      ],
      "steps": [
        "Ses dosyasını oynatma seçeneği seçilir.",
        "Uygulama ses dosyasının offline modda kullanılabilir olduğunu kontrol eder.",
        "Offline modda indirilen dosya oynatılır."
      ],
      "expectedResults": "İndirilen ses dosyası offline modda başarıyla oynatılır.",
      "requirementCode": "REQ-003",
      "format": "default"
    },
    {
      "title": "REQ-004: Voice Readout Ekranında Düşük Gecikme Süresi ile Performans Testi",
      "preconditions": [
        "Voice Readout modülü başlatılmış",
        "Ekranda uçuş bilgileri görüntülenebilir durumda"
      ],
      "steps": [
        "Kullanıcı anons oynatma seçeneğini seçer.",
        "Uygulama anonsu başlatır.",
        "Anonsun oynatma süresi ile ilgili zamanlama ölçülür."
      ],
      "expectedResults": "Anons oynatma süresi 1-10 saniye arasında olmalı.",
      "requirementCode": "REQ-004",
      "format": "default"
    },
    {
      "title": "REQ-005: Admin Kullanıcı Yetkilendirme Testi",
      "preconditions": [
        "Admin kullanıcı giriş yapmış",
        "Kişisel veri görüntüleme ekranı açık"
      ],
      "steps": [
        "Admin kullanıcı, kişisel veri görüntüleme seçeneğini seçer.",
        "Uygulama kullanıcı yetkisini kontrol eder.",
        "Yetki doğrulanırsa kişisel veriler görüntülenir."
      ],
      "expectedResults": "Sadece admin yetkisine sahip kullanıcılar kişisel verileri görüntüleyebilir.",
      "requirementCode": "REQ-005",
      "format": "default"
    },
    {
      "title": "REQ-006: Admin Arayüzü Kullanım Dostluğu Testi",
      "preconditions": [
        "Admin kullanıcı giriş yapmış"
      ],
      "steps": [
        "Admin kullanıcı arayüzünde ilgili menüye erişir.",
        "Menüler arasında hızlı geçiş yapılır.",
        "Kullanıcı işlemleri kolayca yürütür."
      ],
      "expectedResults": "Admin kullanıcı arayüzü kullanım dostudur ve hızlı erişim sağlar.",
      "requirementCode": "REQ-006",
      "format": "default"
    },
    {
      "title": "REQ-007: Dil Varyasyonunun Seçilebilirliği Testi",
      "preconditions": [
        "Voice Readout modülü çalışır durumda",
        "Dil seçenekleri yüklenmiş"
      ],
      "steps": [
        "Kullanıcı anons sesi türü olarak 'Male' seçeneğini seçer.",
        "Anons oynatma işlemi başlatılır.",
        "Aynı test 'Female' seçeneği için tekrar edilir."
      ],
      "expectedResults": "Kullanıcı seçtiği dil varyasyonunda (Male/Female) anonsları dinleyebilir.",
      "requirementCode": "REQ-007",
      "format": "default"
    },
    {
      "title": "REQ-008: Crane Sistemi ile Uçuş Bilgileri ve Anons Dosyalarının Güncellenmesi",
      "preconditions": [
        "Crane sistemi ile bağlantı kuruldu",
        "Güncelleme talebi oluşturuldu"
      ],
      "steps": [
        "Uygulama güncelleme talebini Crane sistemi ile paylaşır.",
        "Crane sistemi yeni uçuş bilgileri ve anons dosyalarını gönderir.",
        "Uygulama dosyaları başarıyla günceller."
      ],
      "expectedResults": "Uçuş bilgileri ve anons dosyaları sistemde başarıyla güncellenir.",
      "requirementCode": "REQ-008",
      "format": "default"
    }
  ],
  "coverageRate": 1,
  "missingRequirements": []
}
Test senaryosu formatı: default, Gereksinim: REQ-001
Test senaryosu ekleniyor (düzeltilmiş): {
  documentId: 5,
  title: 'REQ-001: Anonsların Doğru Şekilde Oynatılması',
  preconditions: '["Voice Readout modülü yüklenmiş ve çalışır durumda","Uygulama online moda alınmış","Pilotun uçuş bilgileri sistemde kayıtlı"]',
  steps: '["Pilot uçuş bilgilerini sisteme girer.","Voice Readout modülünde anonsları oynatma seçeneği seçilir.","Modül, girilen uçuş bilgilerinin doğruluğunu kontrol eder ve anonsları oynatır."]',
  expectedResults: 'Uçuş bilgileri doğrulanır ve uygun anonslar doğru şekilde oynatılır.',
  requirementCode: 'REQ-001'
}
Test senaryosu formatı: default, Gereksinim: REQ-002
Test senaryosu ekleniyor (düzeltilmiş): {
  documentId: 5,
  title: 'REQ-002: Gün İçerisindeki Belirli Bir Zaman Dilimindeki Uçuşların Görüntülenmesi',
  preconditions: '["Voice Readout ekranı açılmış","Kullanıcı geçerli bir zaman dilimi seçmiş"]',
  steps: `["Kullanıcı zaman aralığını belirler (örneğin, '08:00 - 14:00').","Voice Readout ekranı, seçilen zaman dilimi için uçuş bilgilerini filtreler.","Ekranda sadece belirtilen zaman dilimine ait uçuşlar görüntülenir."]`,
  expectedResults: 'Seçilen zaman aralığına uygun uçuşlar doğru şekilde görüntülenir.',
  requirementCode: 'REQ-002'
}
Test senaryosu formatı: default, Gereksinim: REQ-003
Test senaryosu ekleniyor (düzeltilmiş): {
  documentId: 5,
  title: 'REQ-003: Offline Modda Daha Önceden İndirilen Ses Dosyalarının Kullanılabilirliği',
  preconditions: '["Kullanıcı ses dosyasını online modda başarıyla indirmiş","Uygulama offline moda alınmış"]',
  steps: '["Ses dosyasını oynatma seçeneği seçilir.","Uygulama ses dosyasının offline modda kullanılabilir olduğunu kontrol eder.","Offline modda indirilen dosya oynatılır."]',
  expectedResults: 'İndirilen ses dosyası offline modda başarıyla oynatılır.',
  requirementCode: 'REQ-003'
}
Test senaryosu formatı: default, Gereksinim: REQ-004
Test senaryosu ekleniyor (düzeltilmiş): {
  documentId: 5,
  title: 'REQ-004: Voice Readout Ekranında Düşük Gecikme Süresi ile Performans Testi',
  preconditions: '["Voice Readout modülü başlatılmış","Ekranda uçuş bilgileri görüntülenebilir durumda"]',
  steps: '["Kullanıcı anons oynatma seçeneğini seçer.","Uygulama anonsu başlatır.","Anonsun oynatma süresi ile ilgili zamanlama ölçülür."]',
  expectedResults: 'Anons oynatma süresi 1-10 saniye arasında olmalı.',
  requirementCode: 'REQ-004'
}
Test senaryosu formatı: default, Gereksinim: REQ-005
Test senaryosu ekleniyor (düzeltilmiş): {
  documentId: 5,
  title: 'REQ-005: Admin Kullanıcı Yetkilendirme Testi',
  preconditions: '["Admin kullanıcı giriş yapmış","Kişisel veri görüntüleme ekranı açık"]',
  steps: '["Admin kullanıcı, kişisel veri görüntüleme seçeneğini seçer.","Uygulama kullanıcı yetkisini kontrol eder.","Yetki doğrulanırsa kişisel veriler görüntülenir."]',
  expectedResults: 'Sadece admin yetkisine sahip kullanıcılar kişisel verileri görüntüleyebilir.',
  requirementCode: 'REQ-005'
}
Test senaryosu formatı: default, Gereksinim: REQ-006
Test senaryosu ekleniyor (düzeltilmiş): {
  documentId: 5,
  title: 'REQ-006: Admin Arayüzü Kullanım Dostluğu Testi',
  preconditions: '["Admin kullanıcı giriş yapmış"]',
  steps: '["Admin kullanıcı arayüzünde ilgili menüye erişir.","Menüler arasında hızlı geçiş yapılır.","Kullanıcı işlemleri kolayca yürütür."]',
  expectedResults: 'Admin kullanıcı arayüzü kullanım dostudur ve hızlı erişim sağlar.',
  requirementCode: 'REQ-006'
}
Test senaryosu formatı: default, Gereksinim: REQ-007
Test senaryosu ekleniyor (düzeltilmiş): {
  documentId: 5,
  title: 'REQ-007: Dil Varyasyonunun Seçilebilirliği Testi',
  preconditions: '["Voice Readout modülü çalışır durumda","Dil seçenekleri yüklenmiş"]',
  steps: `["Kullanıcı anons sesi türü olarak 'Male' seçeneğini seçer.","Anons oynatma işlemi başlatılır.","Aynı test 'Female' seçeneği için tekrar edilir."]`,
  expectedResults: 'Kullanıcı seçtiği dil varyasyonunda (Male/Female) anonsları dinleyebilir.',
  requirementCode: 'REQ-007'
}
Test senaryosu formatı: default, Gereksinim: REQ-008
Test senaryosu ekleniyor (düzeltilmiş): {
  documentId: 5,
  title: 'REQ-008: Crane Sistemi ile Uçuş Bilgileri ve Anons Dosyalarının Güncellenmesi',
  preconditions: '["Crane sistemi ile bağlantı kuruldu","Güncelleme talebi oluşturuldu"]',
  steps: '["Uygulama güncelleme talebini Crane sistemi ile paylaşır.","Crane sistemi yeni uçuş bilgileri ve anons dosyalarını gönderir.","Uygulama dosyaları başarıyla günceller."]',
  expectedResults: 'Uçuş bilgileri ve anons dosyaları sistemde başarıyla güncellenir.',
  requirementCode: 'REQ-008'
}
2:44:34 PM [express] POST /api/documents/5/generate-test-scenarios 200 in 21832ms :: {"scenarios":[{…
2:44:34 PM [express] GET /api/documents/5/test-scenarios 200 in 2ms :: [{"id":53,"document_id":5,"ti…
