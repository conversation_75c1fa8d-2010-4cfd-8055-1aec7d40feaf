-- AI Bot Memory tablosu - <PERSON><PERSON> bazlı akıllı hafıza sistemi
CREATE TABLE IF NOT EXISTS ai_bot_memory (
  id SERIAL PRIMARY KEY,
  project_id INTEGER NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  memory_type VARCHAR(50) NOT NULL, -- 'project_info', 'issue_pattern', 'tech_stack', 'team_info', 'business_rules'
  memory_key VARCHAR(200) NOT NULL, -- <PERSON><PERSON><PERSON> anahtar (örn: 'main_tech_stack', 'team_size', 'coding_standards')
  memory_value JSONB NOT NULL, -- JSON formatında veri
  confidence_score INTEGER DEFAULT 1, -- 1-10 arası güven skoru
  usage_count INTEGER DEFAULT 1, -- Ka<PERSON> kez kullanıldı/referans verildi
  last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- <PERSON>
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  -- Benzersizlik kısıtı: A<PERSON><PERSON> proje, tip ve anahtar kombinasyonu sadece bir kez olabilir
  UNIQUE(project_id, memory_type, memory_key)
);

-- İndeksler - Performans için
CREATE INDEX IF NOT EXISTS idx_ai_bot_memory_project_id ON ai_bot_memory(project_id);
CREATE INDEX IF NOT EXISTS idx_ai_bot_memory_type ON ai_bot_memory(project_id, memory_type);
CREATE INDEX IF NOT EXISTS idx_ai_bot_memory_confidence ON ai_bot_memory(confidence_score);
CREATE INDEX IF NOT EXISTS idx_ai_bot_memory_usage ON ai_bot_memory(usage_count);
CREATE INDEX IF NOT EXISTS idx_ai_bot_memory_last_used ON ai_bot_memory(last_used);

-- Trigger: updated_at otomatik güncelleme
CREATE OR REPLACE FUNCTION update_ai_bot_memory_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_ai_bot_memory_updated_at
    BEFORE UPDATE ON ai_bot_memory
    FOR EACH ROW
    EXECUTE FUNCTION update_ai_bot_memory_updated_at();

-- Örnek veri (opsiyonel - test için)
-- INSERT INTO ai_bot_memory (project_id, memory_type, memory_key, memory_value, confidence_score) VALUES
-- (1, 'project_info', 'main_info', '{"name": "Test Project", "domain": "web", "size": "medium"}', 8),
-- (1, 'tech_stack', 'main_stack', '{"frontend": ["react", "typescript"], "backend": ["node.js"], "database": ["postgresql"]}', 9),
-- (1, 'issue_pattern', 'common_patterns', '{"common_types": ["Bug", "Feature"], "common_priorities": ["High", "Medium"]}', 7);
